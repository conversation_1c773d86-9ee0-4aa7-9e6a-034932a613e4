{"variable_pair": ["Stripper Underflow", "Component D Purge Gas"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Stripper Underflow → Component D Purge Gas", "association_votes": {"A": 1, "?": 4, "⊥": 2, "IA": 8}, "direction_votes": {"Stripper Underflow - Component D Purge Gas": 3, "Stripper Underflow → Component D Purge Gas": 6}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5549510717391968, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow - Component D Purge Gas"}, {"chunk_id": "pdf_1764_chunk_0", "chunk_text": "Machine Learned Interatomic Potential for Dispersion Strengthened Plasma Facing Components", "retrieval_score": 0.5260937213897705, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2212.01432v1_chunk_0", "chunk_text": "Machine Learned Interatomic Potential for Dispersion Strengthened Plasma\nFacing Components", "retrieval_score": 0.5260937213897705, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow → Component D Purge Gas"}, {"chunk_id": "pdf_131_chunk_0", "chunk_text": "arXiv:2303.05904v1  [cs.LG]  10 Mar 2023Deep Anomaly Detection on Tennessee Eastman Process Data", "retrieval_score": 0.5093567371368408, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow → Component D Purge Gas"}, {"chunk_id": "pdf_1917_chunk_3", "chunk_text": "Stadium\nAve, West Lafayette, 47907, IN, USA\nAbstract\nMachine learning algorithms are increasingly being applied to fault detection\nand diagnosis (FDD) in chemical processes. However, existing data-driven\nFDD platforms often lack interpretability for process operators and strug-\ngle to identify root causes of previously unseen faults. This paper presents\nFaultExplainer , an interactive tool designed to improve fault detection, diag-\nnosis, and explanation in the Tennessee Eastman Process (TEP).", "retrieval_score": 0.5029622316360474, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2403.10842v4_chunk_0", "chunk_text": "Twin Transformer using Gated Dynamic Learnable Attention mechanism for\nFault Detection and Diagnosis in the Tennessee Eastman Process", "retrieval_score": 0.4999127984046936, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "1807.03623v1_chunk_0", "chunk_text": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "retrieval_score": 0.49550116062164307, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Underflow → Component D Purge Gas"}, {"chunk_id": "2310.06013v3_chunk_0", "chunk_text": "The Not-So Dramatic Effect of Advective Flows on Gas Accretion", "retrieval_score": 0.4940026104450226, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_4254_chunk_5", "chunk_text": "Keywords: Tennessee Eastman Process, Chemical Processes, Graph neural\nnetworks, Fault diagnosis, Sensor dataarXiv:2210. 11164v1  [cs. AI]  20 Oct 2022\n1. Introduction\nDuring the production, equipment often stops due to the various faults. The process of \fndin.", "retrieval_score": 0.4893307685852051, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow → Component D Purge Gas"}, {"chunk_id": "pdf_7856_chunk_5", "chunk_text": "In the second stage, a reconstruction error-based threshold approach using the trained\nGAWNO is employed to detect and isolate faults based on the discrepancy values. We validate the\nproposed approach using the Tennessee Eastman Process (TEP) dataset and Avedore wastewater\ntreatment plant (WWTP) and N2Oemissions nam.", "retrieval_score": 0.4866868853569031, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow → Component D Purge Gas"}, {"chunk_id": "pdf_9406_chunk_5", "chunk_text": "The performance of the method is demonstrated\nand contrasted to (dynamic) principal component analysis, which is widely applied in the industry,\nin the benchmark Tennessee Eastman process (TEP) and a real chemical manufacturing dataset. Keywords: fault detection, fault identi\fcation, recurrent neural networks, variational dropout,\nBayesian inference, Tennessee Eastman process. 1.", "retrieval_score": 0.4840873181819916, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow - Component D Purge Gas"}, {"chunk_id": "pdf_1579_chunk_5", "chunk_text": "Such an unconventional symmetry of the order param-\neter has also important implications for the Josephson\nphysics because for a d-wave (D) superconductor the\nJosephson coupling is subject to an additional phase de-\npendence caused by the internal phase structure of the\nwave function. The phase properties of.", "retrieval_score": 0.48385658860206604, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_3805_chunk_2", "chunk_text": "Abstract: Fault detection and diagnosis (FDD) is a crucial task for ensuring the safety and \nefficiency of industrial processes. We propose a novel FDD methodology for the Tennessee \nEastman Process (TEP), a widely used benchmark for chemical process control. The model \nemploys two separate Transformer branches, enabling independent processing of input data and \npotential extraction of diverse information.", "retrieval_score": 0.4775357246398926, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow → Component D Purge Gas"}, {"chunk_id": "pdf_8440_chunk_1", "chunk_text": "Deep Compression of Neural Networks for Fault\nDetection on Tennessee Eastman Chemical\nProcesses\nMingxuan Li\nDepartment of Computer Science\nUniversity of North Carolina at Chapel Hill\nChapel Hill, NC, USA\nmingxuan li@unc. edu* <PERSON><PERSON>un Shao\nNuro Inc. Mountain View, CA, USA\nyuanxun@gatech. edu\nAbstract\nArtiﬁcial neural network has achieved the state-of-art performance in fault detection on the Tennessee Eastman process, but\nit often requires enormous memory to fund its massive parameters.", "retrieval_score": 0.47519779205322266, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow - Component D Purge Gas"}, {"chunk_id": "pdf_3805_chunk_5", "chunk_text": "This underscores the robustness and \nefficacy of the approach for FDD in intricate industrial processes. Keywords. Deep Learning, Fault Detection, Tennessee Eastman Process, Trans.", "retrieval_score": 0.47499287128448486, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}], "original_retrieval_results": 15}