{"variable_pair": ["Stripper Level", "Stripper Pressure"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Stripper Level → Stripper Pressure", "association_votes": {"A": 4, "?": 2, "IA": 8, "⊥": 1}, "direction_votes": {"Stripper Level - Stripper Pressure": 3, "Stripper Level → Stripper Pressure": 9}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5412631034851074, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Level → Stripper Pressure"}, {"chunk_id": "pdf_131_chunk_0", "chunk_text": "arXiv:2303.05904v1  [cs.LG]  10 Mar 2023Deep Anomaly Detection on Tennessee Eastman Process Data", "retrieval_score": 0.5380314588546753, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_3937_chunk_3", "chunk_text": "We demon-\nstrate our model’s performance on two publicly available datasets of the Tennessee\nEastman Process with various faults. The results show that our method significantly\noutperforms existing approaches (+0. 2-0. 3 TPR for a fixed FPR) and effectively de-\ntects most of the process faults without expert annotation. Moreover, we show that the\nmodel fine-tuned on a small fraction of labeled data nearly reaches the performance of\na SOTA model trained on the full dataset.", "retrieval_score": 0.5353156328201294, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Level → Stripper Pressure"}, {"chunk_id": "2210.14595v1_chunk_4", "chunk_text": "Finally, we demonstrate the\neffectiveness of the proposed switching strategy via numerical simulation on\nthe Tennessee Eastman Process.", "retrieval_score": 0.5283288955688477, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Level → Stripper Pressure"}, {"chunk_id": "2212.04140v2_chunk_3", "chunk_text": "The effectiveness of the switching strategy is\nalso demonstrated via numerical simulation on the Tennessee Eastman Process.", "retrieval_score": 0.5250970721244812, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Level → Stripper Pressure"}, {"chunk_id": "2301.11573v3_chunk_3", "chunk_text": "The theoretical analysis is verified through\nMonte Carlo simulations and Tennessee Eastman process (TEP) dataset.", "retrieval_score": 0.5224654674530029, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2212.12092v1_chunk_5", "chunk_text": "Finally, we use the benchmark Tennessee Eastman to\nperform experiments to test the ensemble classifier's prediction and anomaly\ndetection capabilities.", "retrieval_score": 0.5204007625579834, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Level → Stripper Pressure"}, {"chunk_id": "2309.00157v1_chunk_3", "chunk_text": "We validate the approach\nusing the Benchmark Tennessee Eastman while doing an ablation study of the\nmodel update parameters.", "retrieval_score": 0.5098967552185059, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Level → Stripper Pressure"}, {"chunk_id": "2203.11321v1_chunk_4", "chunk_text": "Finally, as a case study, the proposed\nmodel is implemented in the well-known Tennessee Eastman process, and the\nresults are presented.", "retrieval_score": 0.5097804665565491, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Level → Stripper Pressure"}, {"chunk_id": "2308.11247v2_chunk_0", "chunk_text": "Benchmarking Domain Adaptation for Chemical Processes on the Tennessee\nEastman Process", "retrieval_score": 0.5083895921707153, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_6959_chunk_5", "chunk_text": "Simulation results demonstrate the efficacy of this approach, showcasing superior performance in \naccuracy, false alarm rate, and misclassifica tion rate compared to existing methods. This \nmethodology provides a robust and interpretable solution for fault detection and diagnosis in the \nTEP, highlighting its potential for industrial applications. Keywords. Fault Detection, Tennessee Eastman Process, Supervised Feature Selection, Attention \nMechanism, Bidirectional Long Short -Term Memory.", "retrieval_score": 0.5081838369369507, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Level - Stripper Pressure"}, {"chunk_id": "pdf_2572_chunk_3", "chunk_text": "We apply our proposed compression\nmethod on the Tennessee Eastman Dataset, where we perform\nfault classification using the compressed data in two settings:\na fully supervised one and in a semi supervised, contrastive\nlearning setting. Both times, we were able to outperform real\nvalued counterparts as well as two baseline models: one with\nthe uncompressed time-series as the input and the other with a\nregular downsampling using the mean.", "retrieval_score": 0.5077134370803833, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Level → Stripper Pressure"}, {"chunk_id": "2403.11722v2_chunk_3", "chunk_text": "We\napply our proposed compression method on the Tennessee Eastman Dataset, where\nwe perform fault classification using the compressed data in two settings: a\nfully supervised one and in a semi supervised, contrastive learning setting. Both times, we were able to outperform real valued counterparts as well as two\nbaseline models: one with the uncompressed time-series as the input and the\nother with a regular downsampling using the mean.", "retrieval_score": 0.5077134370803833, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Level - Stripper Pressure"}, {"chunk_id": "1607.07423v3_chunk_5", "chunk_text": "We illustrate the\nsuccessful use of $K_T$ chart using the Tennessee Eastman process data.", "retrieval_score": 0.5073234438896179, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Level - Stripper Pressure"}, {"chunk_id": "2403.10842v4_chunk_0", "chunk_text": "Twin Transformer using Gated Dynamic Learnable Attention mechanism for\nFault Detection and Diagnosis in the Tennessee Eastman Process", "retrieval_score": 0.5057184100151062, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Level → Stripper Pressure"}], "original_retrieval_results": 15}