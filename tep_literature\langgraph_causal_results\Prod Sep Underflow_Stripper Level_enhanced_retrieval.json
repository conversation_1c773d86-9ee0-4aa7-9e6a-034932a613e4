{"variable_pair": ["Prod Sep Underflow", "Stripper Level"], "results": [{"chunk": {"chunk_id": "pdf_9723_chunk_4", "paper_id": "pdf_9723", "paper_title": "Draft version July 10, 2024", "chunk_text": "Here, we reevaluate this line of reasoning by incorporating recycling flows of gas\ninto a numerical one-dimensional thermodynamic model with more realistic equation of state and\nopacities and the thermal state of the advective flow. At 0. 1 AU, we find that advective flows are\nonly able to produce mini-Neptunes if they can penetrate below ∼0. 25 of the planet’s gravitational\nsphere of influence.", "found_variables": ["rate", "thermal", "flow"], "variable_count": 3, "chunk_length": 398}, "score": 0.5076831579208374, "query": "Prod Sep Underflow and Stripper Level"}, {"chunk": {"chunk_id": "2401.08380v1_chunk_1", "paper_id": "2401.08380v1", "paper_title": "Thermoelectric power of overdoped Tl2201 crystals: Charge density waves\nand $T^1$ and $T^2$ resistivities", "chunk_text": "We report measurements of the in-plane thermoelectric power (TEP) for an\noverdoped (OD) crystal of the single layer cuprate superconductor\nTl$_2$Ba$_2$CuO$_{6+x}$ (Tl2201) at several hole concentrations ($p$), from 300\nor 400 K to below the superconducting transition temperature ($T_c$). For $p$ =\n0. 192 and 0. 220, small upturns in the TEP below 150 K are attributed to the\npresence of charge density waves (CDW) detected by resonant inelastic X-ray\nscattering studies.", "found_variables": ["temperature", "rate", "temp", "concentration", "TEP"], "variable_count": 5, "chunk_length": 472}, "score": 0.5063824653625488, "query": "Prod Sep Underflow and Stripper Level"}, {"chunk": {"chunk_id": "1706.02043v1_chunk_2", "paper_id": "1706.02043v1", "paper_title": "Reduced chemistry for butanol isomers at engine-relevant conditions", "chunk_text": "During the reduction\nprocess, issues were encountered with pressure-dependent reactions formulated\nusing the logarithmic pressure interpolation approach; these issues are\ndiscussed and recommendations made to avoid ambiguity in its future\nimplementation in mechanism development.", "found_variables": ["press", "pressure"], "variable_count": 2, "chunk_length": 279}, "score": 0.49946337938308716, "query": "Prod Sep Underflow Stripper Level interaction"}, {"chunk": {"chunk_id": "1201.0715v3_chunk_4", "paper_id": "1201.0715v3", "paper_title": "Tree-Structure Expectation Propagation for LDPC Decoding over the BEC", "chunk_text": "The\nsolution of these equations is used to predict the TEP decoder performance in\nboth the asymptotic regime and the finite-length regime over the BEC. While the\nasymptotic threshold of the TEP decoder is the same as the BP decoder for\nregular and optimized codes, we propose a scaling law (SL) for finite-length\nLDPC codes, which accurately approximates the TEP improved performance and\nfacilitates its optimization.", "found_variables": ["rate", "TEP"], "variable_count": 2, "chunk_length": 417}, "score": 0.4969968795776367, "query": "Prod Sep Underflow and Stripper Level"}, {"chunk": {"chunk_id": "pdf_9794_chunk_4", "paper_id": "pdf_9794", "paper_title": "IEEE TRANSACTIONS ON INFORMATION THEORY 1", "chunk_text": "The solution of these equations is used\nto predict the TEP decoder performance in both the asymptotic\nregime and the ﬁnite-length regime over the BEC. While the\nasymptotic threshold of the TEP decoder is the same as the\nBP decoder for regular and optimized codes, we propose a\nscaling law (SL) for ﬁnite-length LDPC codes, which accurately\napproximates the TEP improved performance and facilitates its\noptimization. I.", "found_variables": ["rate", "TEP"], "variable_count": 2, "chunk_length": 418}, "score": 0.4948905408382416, "query": "Prod Sep Underflow and Stripper Level"}, {"chunk": {"chunk_id": "pdf_6811_chunk_5", "paper_id": "pdf_6811", "paper_title": "GENERATIVE ADVERSARIAL WAVELET NEURAL OPERATOR :", "chunk_text": "In the second stage, a reconstruction error-based threshold approach using the trained\nGAWNO is employed to detect and isolate faults based on the discrepancy values. We validate the\nproposed approach using the Tennessee Eastman Process (TEP) dataset and Avedore wastewater\ntreatment plant (WWTP) and N2Oemissions nam.", "found_variables": ["TEP", "Tennessee Eastman"], "variable_count": 2, "chunk_length": 318}, "score": 0.4944345951080322, "query": "Prod Sep Underflow and Stripper Level"}, {"chunk": {"chunk_id": "2301.05537v4_chunk_2", "paper_id": "2301.05537v4", "paper_title": "Almost Surely $\\sqrt{T}$ Regret for Adaptive LQR", "chunk_text": "The\ncontroller features a circuit-breaking mechanism, which circumvents potential\nsafety breach and guarantees the convergence of the system parameter estimate,\nbut is shown to be triggered only finitely often and hence has negligible\neffect on the asymptotic performance of the controller. The proposed controller\nis also validated via simulation on Tennessee Eastman Process~(TEP), a commonly\nused industrial process example.", "found_variables": ["vent", "TEP", "Tennessee Eastman"], "variable_count": 3, "chunk_length": 427}, "score": 0.49170786142349243, "query": "Prod Sep Underflow and Stripper Level"}, {"chunk": {"chunk_id": "2401.04004v1_chunk_3", "paper_id": "2401.04004v1", "paper_title": "Generative adversarial wavelet neural operator: Application to fault\ndetection and isolation of multivariate time series data", "chunk_text": "In the second stage, a reconstruction error-based threshold\napproach using the trained GAWNO is employed to detect and isolate faults based\non the discrepancy values. We validate the proposed approach using the\nTennessee Eastman Process (TEP) dataset and Avedore wastewater treatment plant\n(WWTP) and N2O emissions named as WWTPN2O datasets.", "found_variables": ["TEP", "Tennessee Eastman"], "variable_count": 2, "chunk_length": 341}, "score": 0.4912039041519165, "query": "Prod Sep Underflow and Stripper Level"}, {"chunk": {"chunk_id": "2103.12222v1_chunk_1", "paper_id": "2103.12222v1", "paper_title": "Explainability: Relevance based Dynamic Deep Learning Algorithm for\nFault Detection and Diagnosis in Chemical Processes", "chunk_text": "The focus of this work is on Statistical Process Control (SPC) of a\nmanufacturing process based on available measurements. Two important\napplications of SPC in industrial settings are fault detection and diagnosis\n(FDD). In this work a deep learning (DL) based methodology is proposed for FDD. We investigate the application of an explainability concept to enhance the FDD\naccuracy of a deep neural network model trained with a data set of relatively\nsmall number of samples.", "found_variables": ["process control", "work", "fault detection"], "variable_count": 3, "chunk_length": 475}, "score": 0.4891773760318756, "query": "Prod Sep Underflow and Stripper Level"}, {"chunk": {"chunk_id": "pdf_2039_chunk_3", "paper_id": "pdf_2039", "paper_title": "Reduced chemistry for butanol isomers at", "chunk_text": "During the reduction process, issues encountered with pressure-dependent\nreactions formulated using the logarithmic pressure interpolation approach were dis-\ncussed, with recommendations made to avoid ambiguity in its future implementation\nin mechanism development.", "found_variables": ["press", "pressure"], "variable_count": 2, "chunk_length": 265}, "score": 0.48896345496177673, "query": "Prod Sep Underflow Stripper Level interaction"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}