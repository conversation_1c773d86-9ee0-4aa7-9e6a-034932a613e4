{"variable_pair": ["Stripper Level", "Compressor Work"], "results": [{"chunk": {"chunk_id": "pdf_3256_chunk_3", "paper_id": "pdf_3256", "paper_title": "Two-stage Hydrogen Compression Using Zr-based Metal Hydrides ", "chunk_text": "The purpos e of this study is to find a series of alloys promptly forming \nmetal hydrides (MH) with suitable properties in  order to build a MH-based hydrogen compressor, \nworking in the same way between 20 and ~100 oC.", "found_variables": ["work", "compressor", "press"], "variable_count": 3, "chunk_length": 219}, "score": 0.5222818851470947, "query": "Stripper Level and Compressor Work"}, {"chunk": {"chunk_id": "1207.3541v1_chunk_2", "paper_id": "1207.3541v1", "paper_title": "Two-stage Hydrogen Compression Using Zr-based Metal Hydrides", "chunk_text": "The purpose of this study\nis to find a series of alloys promptly forming metal hydrides (MH) with\nsuitable properties in order to build a MH-based hydrogen compressor, working\nin the same way between 20 and ~100 oC.", "found_variables": ["work", "compressor", "press"], "variable_count": 3, "chunk_length": 215}, "score": 0.5201537609100342, "query": "Stripper Level and Compressor Work"}, {"chunk": {"chunk_id": "pdf_8410_chunk_4", "paper_id": "pdf_8410", "paper_title": "arXiv:1703.08935v1  [math.OC]  27 Mar 2017PREPRINT OF DOI: 10.1109/TPWRS.2017.2671786, IEEE TRANSA CTIONS ON POWER SYSTEMS 1", "chunk_text": "Although\nthe proposed decomposition approach cannot guarantee glob al\noptimality, a high level picture of how the network can be pla nned\nreliably and economically considering CVSR is achieved. Index Terms —Continuously variable series reactor, transmis-\nsion expansion planning, power ﬂow control, N−1security,\nmixed integer linear programming. NOMENCLATURE\nIndices\ni, j Index of buses. k Index of transmission elements. n Index of generators. m Index of loads.", "found_variables": ["composition", "work", "level", "reactor"], "variable_count": 4, "chunk_length": 462}, "score": 0.5187764763832092, "query": "Stripper Level and Compressor Work"}, {"chunk": {"chunk_id": "2108.11662v1_chunk_2", "paper_id": "2108.11662v1", "paper_title": "Robust AC Transmission Expansion Planning with Load and Renewable\nGeneration Uncertainties", "chunk_text": "It utilizes a well-known convex relaxation and is solved by Benders\nDecomposition (BD), where the master determines the robust AC TEP plan. A novel\ndual slave model is developed for the second level of BD, which circumvents the\nissues in using the Conic Duality Theory (CDT), and aids in the worst-case\nrealization of uncertainties using a novel set of constraints. The developed\ndual slave is not a mixed-integer problem and is solved using the Primal-Dual\nInterior Point Method (PDIPM).", "found_variables": ["composition", "vent", "level", "TEP"], "variable_count": 4, "chunk_length": 488}, "score": 0.518008828163147, "query": "Stripper Level and Compressor Work"}, {"chunk": {"chunk_id": "pdf_4523_chunk_3", "paper_id": "pdf_4523", "paper_title": "Accelerated Methods with Compression for Horizontal", "chunk_text": "Therefore, it is crucial\nto enhance current methods with strategies that minimize the amount of data transmitted\nduring training while still achieving a model of similar quality. This paper introduces two\naccelerated algorithms with various compressors, working in the regime of horizontal and\nvertical data division.", "found_variables": ["rate", "work", "compressor", "press"], "variable_count": 4, "chunk_length": 317}, "score": 0.5135794878005981, "query": "Stripper Level and Compressor Work"}, {"chunk": {"chunk_id": "pdf_4523_chunk_0", "paper_id": "pdf_4523", "paper_title": "Accelerated Methods with Compression for Horizontal", "chunk_text": "Accelerated Methods with Compression for Horizontal", "found_variables": ["rate", "compression", "press"], "variable_count": 3, "chunk_length": 51}, "score": 0.513485312461853, "query": "Stripper Level and Compressor Work"}, {"chunk": {"chunk_id": "1201.0715v3_chunk_4", "paper_id": "1201.0715v3", "paper_title": "Tree-Structure Expectation Propagation for LDPC Decoding over the BEC", "chunk_text": "The\nsolution of these equations is used to predict the TEP decoder performance in\nboth the asymptotic regime and the finite-length regime over the BEC. While the\nasymptotic threshold of the TEP decoder is the same as the BP decoder for\nregular and optimized codes, we propose a scaling law (SL) for finite-length\nLDPC codes, which accurately approximates the TEP improved performance and\nfacilitates its optimization.", "found_variables": ["rate", "TEP"], "variable_count": 2, "chunk_length": 417}, "score": 0.5123798251152039, "query": "Stripper Level and Compressor Work"}, {"chunk": {"chunk_id": "pdf_9794_chunk_4", "paper_id": "pdf_9794", "paper_title": "IEEE TRANSACTIONS ON INFORMATION THEORY 1", "chunk_text": "The solution of these equations is used\nto predict the TEP decoder performance in both the asymptotic\nregime and the ﬁnite-length regime over the BEC. While the\nasymptotic threshold of the TEP decoder is the same as the\nBP decoder for regular and optimized codes, we propose a\nscaling law (SL) for ﬁnite-length LDPC codes, which accurately\napproximates the TEP improved performance and facilitates its\noptimization. I.", "found_variables": ["rate", "TEP"], "variable_count": 2, "chunk_length": 418}, "score": 0.5105420351028442, "query": "Stripper Level and Compressor Work"}, {"chunk": {"chunk_id": "1806.00935v1_chunk_0", "paper_id": "1806.00935v1", "paper_title": "Cooperative reliable response from sloppy gene-expression dynamics", "chunk_text": "Cooperative reliable response from sloppy gene-expression dynamics", "found_variables": ["press"], "variable_count": 1, "chunk_length": 66}, "score": 0.5008324384689331, "query": "Stripper Level Compressor Work interaction"}, {"chunk": {"chunk_id": "pdf_2281_chunk_2", "paper_id": "pdf_2281", "paper_title": "1", "chunk_text": "In addition, time-varying operational conditions that generate\nnonstationary process faults and the correlation information in\nthe process require to consider for accurate fault diagnosis in\nthe manufacturing systems. This article proposes a novel fault\ndiagnosis method: clustering spatially correlated sparse Bayesian\nlearning (CSSBL), and explicitly demonstrates its applicability\nin a multistation assembly system that is vulnerable to the\nabove challenges.", "found_variables": ["rate"], "variable_count": 1, "chunk_length": 461}, "score": 0.49898409843444824, "query": "correlation Stripper Level Compressor Work"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}