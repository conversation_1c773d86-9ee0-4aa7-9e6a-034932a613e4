{"variable_pair": ["Compressor Work", "Component E Purge Gas"], "results": [{"chunk": {"chunk_id": "pdf_3256_chunk_3", "paper_id": "pdf_3256", "paper_title": "Two-stage Hydrogen Compression Using Zr-based Metal Hydrides ", "chunk_text": "The purpos e of this study is to find a series of alloys promptly forming \nmetal hydrides (MH) with suitable properties in  order to build a MH-based hydrogen compressor, \nworking in the same way between 20 and ~100 oC.", "found_variables": ["work", "compressor", "press"], "variable_count": 3, "chunk_length": 219}, "score": 0.5304625034332275, "query": "Compressor Work and Component E Purge Gas"}, {"chunk": {"chunk_id": "2101.06993v1_chunk_0", "paper_id": "2101.06993v1", "paper_title": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "found_variables": ["fault detection", "press", "work", "compression", "chemical process"], "variable_count": 5, "chunk_length": 95}, "score": 0.5300863981246948, "query": "Compressor Work and Component E Purge Gas"}, {"chunk": {"chunk_id": "pdf_9912_chunk_0", "paper_id": "pdf_9912", "paper_title": "Enhanced Fault Detection and Cause Identification Using ", "chunk_text": "Enhanced Fault Detection and Cause Identification Using", "found_variables": ["fault detection"], "variable_count": 1, "chunk_length": 55}, "score": 0.5222852826118469, "query": "Compressor Work and Component E Purge Gas"}, {"chunk": {"chunk_id": "pdf_6057_chunk_3", "paper_id": "pdf_6057", "paper_title": "A Neural Network Approach to ECG Denoising", "chunk_text": "1 Introduction\nThe ECG is often corrupted by di\u000berent types of noise, namely, power line inter-\nference, electrode contact and motion artifacts, respiration, electrical activity\nof muscles in the vicinity of the electrodes and interference from other elec-\ntronic devices. Analysis of noisy ECGs is di\u000ecult for humans and for computer\nprograms. In this work we place ourselves in context of automatic and semi\nautomatic ECG analysis: denoising should facilitate automatic ECG analysis.", "found_variables": ["work"], "variable_count": 1, "chunk_length": 485}, "score": 0.5157126188278198, "query": "Compressor Work and Component E Purge Gas"}, {"chunk": {"chunk_id": "1712.04118v1_chunk_0", "paper_id": "1712.04118v1", "paper_title": "Neural Component Analysis for Fault Detection", "chunk_text": "Neural Component Analysis for Fault Detection", "found_variables": ["component", "fault detection"], "variable_count": 2, "chunk_length": 45}, "score": 0.5097321271896362, "query": "Compressor Work and Component E Purge Gas"}, {"chunk": {"chunk_id": "1207.3541v1_chunk_2", "paper_id": "1207.3541v1", "paper_title": "Two-stage Hydrogen Compression Using Zr-based Metal Hydrides", "chunk_text": "The purpose of this study\nis to find a series of alloys promptly forming metal hydrides (MH) with\nsuitable properties in order to build a MH-based hydrogen compressor, working\nin the same way between 20 and ~100 oC.", "found_variables": ["work", "compressor", "press"], "variable_count": 3, "chunk_length": 215}, "score": 0.5011872053146362, "query": "Compressor Work and Component E Purge Gas"}, {"chunk": {"chunk_id": "pdf_8762_chunk_0", "paper_id": "pdf_8762", "paper_title": "PREDICTIVE NETWORKING AND OPTIMIZATION", "chunk_text": "PREDICTIVE NETWORKING AND OPTIMIZATION", "found_variables": ["work"], "variable_count": 1, "chunk_length": 38}, "score": 0.5007818937301636, "query": "Compressor Work and Component E Purge Gas"}, {"chunk": {"chunk_id": "pdf_4024_chunk_4", "paper_id": "pdf_4024", "paper_title": "Root-KGD: A Novel Framework for Root Cause", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "found_variables": ["TEP", "flow", "Tennessee Eastman"], "variable_count": 3, "chunk_length": 469}, "score": 0.49733686447143555, "query": "Compressor Work and Component E Purge Gas"}, {"chunk": {"chunk_id": "pdf_4308_chunk_2", "paper_id": "pdf_4308", "paper_title": "Fault Detection and Identi\fcation Using", "chunk_text": "Braatza\naDepartment of Chemical Engineering, Massachusetts Institute of Technology, Cambridge, MA, USA\nbCorporate Strategic Research, ExxonMobil Research and Engineering, Annandale, NJ, USA\nAbstract\nIn the processing and manufacturing industries, there has been a large push to produce higher\nquality products and ensure maximum e\u000eciency of processes, which requires approaches to e\u000bectively\ndetect and resolve disturbances to ensure optimal operations.", "found_variables": ["rate"], "variable_count": 1, "chunk_length": 453}, "score": 0.4940856993198395, "query": "Compressor Work and Component E Purge Gas"}, {"chunk": {"chunk_id": "pdf_7430_chunk_5", "paper_id": "pdf_7430", "paper_title": "arXiv:2205.05250v2  [cs.LG]  5 Oct 2023Spatial-temporal associations representation and applic ation for process", "chunk_text": "Finally, these networks corresponding to process sta tes at diﬀerent times are fed into a graph convolutional\nneural network to implement graph classiﬁcation to achieve process monitoring. A benchmark experiment (Ten-\nnessee Eastman chemical process) and one application study (cobalt puriﬁcation from zinc solution) are employed to\ndemonstrate the feasibility and applicability of this pape r. Keywords: Spatial-Temporal Associations Representation; Process M onitoring; Stat.", "found_variables": ["rate", "temp", "work", "chemical process"], "variable_count": 4, "chunk_length": 477}, "score": 0.4929167926311493, "query": "correlation Compressor Work Component E Purge Gas"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}