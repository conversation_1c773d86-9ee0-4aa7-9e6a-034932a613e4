#!/usr/bin/env python3
"""
步骤1：文档预处理 - 分块和过滤

将收集的论文进行分块处理，只保留包含Tennessee Eastman变量的文档块
"""

import os
import pandas as pd
import json
from pathlib import Path
import re
import glob
import PyPDF2
from datetime import datetime
from tqdm import tqdm
import sys

class DocumentPreprocessor:
    def __init__(self, literature_dir="tep_literature"):
        """
        初始化文档预处理器

        Args:
            literature_dir (str): 文献目录路径
        """
        self.literature_dir = literature_dir
        self.output_dir = os.path.join(literature_dir, "processed")
        os.makedirs(self.output_dir, exist_ok=True)

        # Tennessee Eastman过程变量列表（按照官方XMEAS定义）
        self.te_variables = [
            "A Feed", "D Feed", "E Feed", "A and C Feed",
            "Recycle Flow", "Reactor Feed Rate", "Reactor Pressure",
            "Reactor Level", "Reactor Temperature", "Purge Rate",
            "Product Sep Temp", "Product Sep Level", "Prod Sep Pressure",
            "Prod Sep Underflow", "Stripper Level", "Stripper Pressure",
            "Stripper Underflow", "Stripper Temperature", "Stripper Steam Flow",
            "Compressor Work", "Reactor CW Outlet Temp", "Separator CW Outlet Temp",
            "Component A Reactor Feed", "Component B Reactor Feed",
            "Component C Reactor Feed", "Component D Reactor Feed",
            "Component F Reactor Feed", "Component A Purge Gas",
            "Component B Purge Gas", "Component D Purge Gas", "Component E Purge Gas"
        ]

        # 扩展的关键词列表（包含常见的表述方式）
        self.extended_keywords = []
        self._build_extended_keywords()

    def _build_extended_keywords(self):
        """构建扩展的关键词列表"""
        # 基础变量名
        self.extended_keywords.extend(self.te_variables)
        
        # 添加常见的变体和同义词
        keyword_variants = {
            "reactor": ["reactor", "CSTR", "continuous stirred tank", "reaction vessel"],
            "temperature": ["temperature", "temp", "thermal", "heat"],
            "pressure": ["pressure", "press", "kPa", "gauge pressure"],
            "level": ["level", "height", "liquid level"],
            "flow": ["flow", "rate", "flowrate", "flow rate"],
            "feed": ["feed", "inlet", "input", "supply"],
            "separator": ["separator", "separation", "sep"],
            "stripper": ["stripper", "stripping", "distillation"],
            "component": ["component", "composition", "concentration", "mole"],
            "purge": ["purge", "vent", "outlet"],
            "compressor": ["compressor", "compression", "work"],
            "cooling": ["cooling", "coolant", "CW", "cooling water"]
        }
        
        # 生成组合关键词
        for base, variants in keyword_variants.items():
            self.extended_keywords.extend(variants)
        
        # Tennessee Eastman相关术语
        te_terms = [
            "Tennessee Eastman", "TE process", "TEP", "XMEAS", "XMV",
            "fault detection", "process control", "chemical process"
        ]
        self.extended_keywords.extend(te_terms)
    
    def load_literature_data(self):
        """从指定的txt和pdf文件加载文献数据"""
        print("正在从指定的txt和pdf文件加载文献数据...")

        papers = []

        # 1. 加载摘要txt文件
        abstracts_file = os.path.join(self.literature_dir, "te_abstracts.txt")
        if os.path.exists(abstracts_file):
            abstracts_papers = self.load_abstracts_txt(abstracts_file)
            papers.extend(abstracts_papers)
            print(f"从 {abstracts_file} 加载了 {len(abstracts_papers)} 篇论文")
        else:
            print(f"警告：找不到摘要文件 {abstracts_file}")

        # 2. 加载PDF文件夹中的文件
        pdfs_dir = os.path.join(self.literature_dir, "pdfs")
        if os.path.exists(pdfs_dir):
            pdf_papers = self.load_pdfs_from_directory(pdfs_dir)
            papers.extend(pdf_papers)
            print(f"从 {pdfs_dir} 加载了 {len(pdf_papers)} 篇论文")
        else:
            print(f"警告：找不到PDF目录 {pdfs_dir}")

        if papers:
            print(f"总共成功加载 {len(papers)} 篇论文")
            return papers
        else:
            print("错误：没有找到任何文献文件")
            return None

    def load_abstracts_txt(self, file_path):
        """加载te_abstracts.txt文件"""
        papers = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 按照"=== 论文 X ==="分割
            paper_sections = re.split(r'=== 论文 \d+ ===', content)

            for i, section in enumerate(paper_sections[1:], 1):  # 跳过第一个空部分
                if section.strip():
                    paper_data = self.parse_abstract_section(section, i)
                    if paper_data:
                        papers.append(paper_data)

            return papers

        except Exception as e:
            print(f"加载摘要文件 {file_path} 失败: {e}")
            return []

    def parse_abstract_section(self, section, paper_id):
        """解析摘要文件中的单个论文部分"""
        try:
            lines = section.strip().split('\n')
            paper_data = {
                'arxiv_id': f'abstract_{paper_id}',
                'title': '',
                'abstract': '',
                'authors': '',
                'published_date': '',
                'source_file': 'te_abstracts.txt',
                'file_type': 'abstract'
            }

            current_field = None
            current_content = []

            for line in lines:
                line = line.strip()
                if line.startswith('标题: '):
                    if current_field:
                        paper_data[current_field] = '\n'.join(current_content).strip()
                    current_field = 'title'
                    current_content = [line[3:]]  # 去掉"标题: "
                elif line.startswith('作者: '):
                    if current_field:
                        paper_data[current_field] = '\n'.join(current_content).strip()
                    current_field = 'authors'
                    current_content = [line[3:]]  # 去掉"作者: "
                elif line.startswith('arXiv ID: '):
                    if current_field:
                        paper_data[current_field] = '\n'.join(current_content).strip()
                    paper_data['arxiv_id'] = line[10:]  # 去掉"arXiv ID: "
                    current_field = None
                    current_content = []
                elif line.startswith('发布日期: '):
                    if current_field:
                        paper_data[current_field] = '\n'.join(current_content).strip()
                    paper_data['published_date'] = line[5:]  # 去掉"发布日期: "
                    current_field = None
                    current_content = []
                elif line.startswith('摘要: '):
                    if current_field:
                        paper_data[current_field] = '\n'.join(current_content).strip()
                    current_field = 'abstract'
                    current_content = [line[3:]]  # 去掉"摘要: "
                elif line.startswith('PDF链接: ') or line.startswith('分类: '):
                    if current_field:
                        paper_data[current_field] = '\n'.join(current_content).strip()
                    current_field = None
                    current_content = []
                elif current_field and line:
                    current_content.append(line)

            # 处理最后一个字段
            if current_field:
                paper_data[current_field] = '\n'.join(current_content).strip()

            return paper_data if paper_data['title'] or paper_data['abstract'] else None

        except Exception as e:
            print(f"解析论文部分失败: {e}")
            return None

    def load_pdfs_from_directory(self, pdfs_dir):
        """从PDF目录加载所有PDF文件"""
        papers = []
        pdf_files = glob.glob(os.path.join(pdfs_dir, "*.pdf"))

        print(f"找到 {len(pdf_files)} 个PDF文件，开始处理...")

        if not pdf_files:
            print("PDF目录中没有找到PDF文件")
            return papers

        # 使用自定义进度显示，每次都在新行
        for file_idx, pdf_file in enumerate(pdf_files, 1):
            filename = os.path.basename(pdf_file)

            # 显示当前进度（每次都在新行，固定长度对齐）
            filename_display = filename[:35].ljust(35)  # 固定35字符长度

            # 使用百分比显示进度
            progress_percent = (file_idx / len(pdf_files)) * 100
            progress_chars = int(progress_percent / 2.5)  # 40字符长度的进度条
            progress_bar = '█' * progress_chars + '-' * (40 - progress_chars)

            print(f"处理: {filename_display}: {progress_percent:3.0f}%|{progress_bar}| {file_idx}/{len(pdf_files)}")

            try:
                paper_data = self.load_pdf_file(pdf_file, show_progress=False)
                if paper_data:
                    papers.append(paper_data)
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
                continue

        print(f"\n✅ PDF处理完成，成功加载 {len(papers)} 篇论文")
        return papers

    def find_txt_files(self):
        """查找txt文件"""
        txt_patterns = [
            os.path.join(self.literature_dir, "*.txt"),
            os.path.join(self.literature_dir, "**", "*.txt"),
            "*.txt",
            "**/*.txt"
        ]

        txt_files = []
        for pattern in txt_patterns:
            txt_files.extend(glob.glob(pattern, recursive=True))

        print(f"找到 {len(txt_files)} 个txt文件")
        return txt_files

    def find_pdf_files(self):
        """查找pdf文件"""
        pdf_patterns = [
            os.path.join(self.literature_dir, "pdfs", "*.pdf"),
            os.path.join(self.literature_dir, "**", "*.pdf"),
            "*.pdf",
            "**/*.pdf"
        ]

        pdf_files = []
        for pattern in pdf_patterns:
            pdf_files.extend(glob.glob(pattern, recursive=True))

        print(f"找到 {len(pdf_files)} 个pdf文件")
        return pdf_files

    def load_txt_file(self, file_path):
        """加载txt文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 提取文件名作为标题
            title = os.path.splitext(os.path.basename(file_path))[0]

            paper_data = {
                'title': title,
                'abstract': content,
                'authors': 'Unknown',
                'arxiv_id': f'txt_{hash(file_path) % 10000}',
                'published_date': datetime.now().strftime('%Y-%m-%d'),
                'source_file': file_path,
                'file_type': 'txt'
            }

            return paper_data

        except Exception as e:
            print(f"加载txt文件 {file_path} 失败: {e}")
            return None

    def load_pdf_file(self, file_path, show_progress=True):
        """加载pdf文件"""
        try:
            # 检查文件大小
            file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
            if file_size > 50:  # 如果文件大于50MB，跳过
                return None

            with open(file_path, 'rb') as f:
                pdf_reader = PyPDF2.PdfReader(f)

                # 检查页数
                num_pages = len(pdf_reader.pages)

                if num_pages > 100:  # 如果页数过多，只处理前100页
                    pages_to_process = pdf_reader.pages[:100]
                else:
                    pages_to_process = pdf_reader.pages

                # 提取文本
                text = ""
                for page in pages_to_process:
                    try:
                        page_text = page.extract_text()
                        text += page_text + "\n"
                    except Exception:
                        continue

                if not text.strip():
                    return None

                # 提取文件名作为标题
                title = os.path.splitext(os.path.basename(file_path))[0]

                # 尝试从文本中提取标题（取前100个字符）
                if text.strip():
                    first_line = text.strip().split('\n')[0]
                    if len(first_line) < 200:
                        title = first_line

                paper_data = {
                    'title': title,
                    'abstract': text[:2000] if len(text) > 2000 else text,  # 限制长度
                    'authors': 'Unknown',
                    'arxiv_id': f'pdf_{hash(file_path) % 10000}',
                    'published_date': datetime.now().strftime('%Y-%m-%d'),
                    'source_file': file_path,
                    'file_type': 'pdf',
                    'text_length': len(text),
                    'num_pages': num_pages
                }

                return paper_data

        except Exception:
            return None

    def split_into_chunks(self, text, chunk_size=500, overlap=50):
        """
        将文本分割成块
        
        Args:
            text (str): 输入文本
            chunk_size (int): 块大小（字符数）
            overlap (int): 重叠字符数
            
        Returns:
            list: 文本块列表
        """
        if not text or len(text.strip()) == 0:
            return []
        
        chunks = []
        text = text.strip()
        
        # 首先按段落分割
        paragraphs = re.split(r'\n\s*\n', text)
        
        current_chunk = ""
        for para in paragraphs:
            para = para.strip()
            if not para:
                continue
            
            # 如果当前块加上新段落不超过限制，则添加
            if len(current_chunk) + len(para) + 2 <= chunk_size:
                if current_chunk:
                    current_chunk += "\n\n" + para
                else:
                    current_chunk = para
            else:
                # 保存当前块
                if current_chunk:
                    chunks.append(current_chunk)
                
                # 如果段落本身太长，需要进一步分割
                if len(para) > chunk_size:
                    # 按句子分割
                    sentences = re.split(r'[.!?]+', para)
                    temp_chunk = ""
                    
                    for sentence in sentences:
                        sentence = sentence.strip()
                        if not sentence:
                            continue
                        
                        if len(temp_chunk) + len(sentence) + 2 <= chunk_size:
                            if temp_chunk:
                                temp_chunk += ". " + sentence
                            else:
                                temp_chunk = sentence
                        else:
                            if temp_chunk:
                                chunks.append(temp_chunk + ".")
                            temp_chunk = sentence
                    
                    if temp_chunk:
                        current_chunk = temp_chunk + "."
                    else:
                        current_chunk = ""
                else:
                    current_chunk = para
        
        # 添加最后一个块
        if current_chunk:
            chunks.append(current_chunk)
        
        return chunks
    
    def contains_te_variables(self, text, min_variables=1):
        """
        检查文本是否包含Tennessee Eastman变量
        
        Args:
            text (str): 文本内容
            min_variables (int): 最少包含的变量数
            
        Returns:
            tuple: (是否包含, 找到的变量列表)
        """
        text_lower = text.lower()
        found_variables = []
        
        for keyword in self.extended_keywords:
            if keyword.lower() in text_lower:
                found_variables.append(keyword)
        
        # 去重
        found_variables = list(set(found_variables))
        
        return len(found_variables) >= min_variables, found_variables
    
    def process_papers(self, papers_list):
        """
        处理论文数据，生成文档块

        Args:
            papers_list (list): 论文数据列表

        Returns:
            list: 处理后的文档块列表
        """
        all_chunks = []
        processed_count = 0

        for idx, paper in enumerate(papers_list):
            paper_id = paper.get('arxiv_id', f'paper_{idx}')
            title = paper.get('title', '')
            abstract = paper.get('abstract', '')

            # 合并标题和摘要
            full_text = f"{title}\n\n{abstract}"
            
            # 分块
            chunks = self.split_into_chunks(full_text)
            
            # 过滤包含TE变量的块
            for chunk_idx, chunk in enumerate(chunks):
                contains_vars, found_vars = self.contains_te_variables(chunk)
                
                if contains_vars:
                    chunk_data = {
                        'chunk_id': f"{paper_id}_chunk_{chunk_idx}",
                        'paper_id': paper_id,
                        'paper_title': title,
                        'chunk_text': chunk,
                        'found_variables': found_vars,
                        'variable_count': len(found_vars),
                        'chunk_length': len(chunk)
                    }
                    all_chunks.append(chunk_data)
            
            processed_count += 1
            if processed_count % 10 == 0:
                print(f"已处理 {processed_count}/{len(papers_list)} 篇论文")
        
        print(f"处理完成！共生成 {len(all_chunks)} 个相关文档块")
        return all_chunks
    
    def save_chunks(self, chunks):
        """保存处理后的文档块"""
        # 保存为JSON格式
        json_path = os.path.join(self.output_dir, "document_chunks.json")
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(chunks, f, ensure_ascii=False, indent=2)
        
        # 保存为CSV格式（便于查看）
        csv_path = os.path.join(self.output_dir, "document_chunks.csv")
        chunks_df = pd.DataFrame(chunks)
        chunks_df.to_csv(csv_path, index=False, encoding='utf-8-sig', escapechar='\\', quoting=1)
        
        print(f"文档块已保存到:")
        print(f"- JSON格式: {json_path}")
        print(f"- CSV格式: {csv_path}")
        
        return json_path, csv_path
    
    def print_statistics(self, chunks):
        """打印统计信息"""
        if not chunks:
            print("没有找到相关的文档块")
            return
        
        print(f"\n=== 文档预处理统计 ===")
        print(f"总文档块数: {len(chunks)}")
        
        # 按变量数量统计
        var_counts = {}
        for chunk in chunks:
            count = chunk['variable_count']
            var_counts[count] = var_counts.get(count, 0) + 1
        
        print(f"\n按包含变量数量分布:")
        for count in sorted(var_counts.keys()):
            print(f"  包含{count}个变量: {var_counts[count]}个块")
        
        # 平均块长度
        avg_length = sum(chunk['chunk_length'] for chunk in chunks) / len(chunks)
        print(f"\n平均块长度: {avg_length:.1f} 字符")
        
        # 最常见的变量
        all_vars = []
        for chunk in chunks:
            all_vars.extend(chunk['found_variables'])
        
        var_freq = {}
        for var in all_vars:
            var_freq[var] = var_freq.get(var, 0) + 1
        
        print(f"\n最常见的变量 (前10个):")
        sorted_vars = sorted(var_freq.items(), key=lambda x: x[1], reverse=True)
        for var, freq in sorted_vars[:10]:
            print(f"  {var}: {freq}次")


def main():
    """主函数"""
    print("Tennessee Eastman过程 - 文档预处理")
    print("=" * 50)
    
    # 初始化预处理器
    preprocessor = DocumentPreprocessor()
    
    # 加载文献数据
    papers_list = preprocessor.load_literature_data()
    if papers_list is None:
        return

    # 处理论文
    print(f"\n开始处理论文...")
    chunks = preprocessor.process_papers(papers_list)
    
    if not chunks:
        print("没有找到包含Tennessee Eastman变量的文档块")
        return
    
    # 保存结果
    json_path, csv_path = preprocessor.save_chunks(chunks)
    
    # 打印统计信息
    preprocessor.print_statistics(chunks)
    
    print(f"\n✅ 文档预处理完成！")
    print(f"下一步可以使用这些文档块进行向量化处理")


if __name__ == "__main__":
    main()
