{"variable_pair": ["Stripper Underflow", "Compressor Work"], "results": [{"chunk": {"chunk_id": "2103.05025v1_chunk_1", "paper_id": "2103.05025v1", "paper_title": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate", "chunk_text": "The variations in feedstock characteristics such as moisture and particle\nsize distribution lead to an inconsistent flow of feedstock from the biomass\npre-processing system to the reactor in-feed system. These inconsistencies\nresult in low on-stream times at the reactor in-feed equipment. This research\ndevelops an optimal process control method for a biomass pre-processing system\ncomprised of milling and densification operations to provide the consistent\nflow of feedstock to a reactor's throat.", "found_variables": ["feed", "process control", "flow", "reactor"], "variable_count": 4, "chunk_length": 499}, "score": 0.5356235504150391, "query": "Stripper Underflow and Compressor Work"}, {"chunk": {"chunk_id": "pdf_4523_chunk_0", "paper_id": "pdf_4523", "paper_title": "Accelerated Methods with Compression for Horizontal", "chunk_text": "Accelerated Methods with Compression for Horizontal", "found_variables": ["rate", "compression", "press"], "variable_count": 3, "chunk_length": 51}, "score": 0.5305911898612976, "query": "Stripper Underflow and Compressor Work"}, {"chunk": {"chunk_id": "pdf_6040_chunk_0", "paper_id": "pdf_6040", "paper_title": "Transmission Expansion Planning Using Cycle Flows", "chunk_text": "Transmission Expansion Planning Using Cycle Flows", "found_variables": ["flow"], "variable_count": 1, "chunk_length": 49}, "score": 0.530498743057251, "query": "Stripper Underflow and Compressor Work"}, {"chunk": {"chunk_id": "2004.08702v1_chunk_0", "paper_id": "2004.08702v1", "paper_title": "Transmission Expansion Planning Using Cycle Flows", "chunk_text": "Transmission Expansion Planning Using Cycle Flows", "found_variables": ["flow"], "variable_count": 1, "chunk_length": 49}, "score": 0.530498743057251, "query": "Stripper Underflow and Compressor Work"}, {"chunk": {"chunk_id": "pdf_8178_chunk_3", "paper_id": "pdf_8178", "paper_title": "Optimal Control to Handle Variations in Biomass Feedstock", "chunk_text": "These inconsistencies result in low on-stream times at the reactor in-feed\nequipment. This research develops an optimal process control method for a biomass pre-\nprocessing system comprised of milling and densiﬁcation operations to provide the consistent\nﬂow of feedstock to a reactor’s throat. This method uses a mixed-integer optimization\nmodel to identify optimal bale sequencing, equipment in-feed rate, and buﬀer location and\nsize in the biomass pre-processing system.", "found_variables": ["feed", "rate", "process control", "reactor"], "variable_count": 4, "chunk_length": 473}, "score": 0.526593804359436, "query": "Stripper Underflow and Compressor Work"}, {"chunk": {"chunk_id": "2403.11722v2_chunk_0", "paper_id": "2403.11722v2", "paper_title": "Time Series Compression using Quaternion Valued Neural Networks and\nQuaternion Backpropagation", "chunk_text": "Time Series Compression using Quaternion Valued Neural Networks and\nQuaternion Backpropagation", "found_variables": ["work", "compression", "press"], "variable_count": 3, "chunk_length": 94}, "score": 0.524127185344696, "query": "Stripper Underflow and Compressor Work"}, {"chunk": {"chunk_id": "2101.06993v1_chunk_0", "paper_id": "2101.06993v1", "paper_title": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "found_variables": ["fault detection", "press", "work", "compression", "chemical process"], "variable_count": 5, "chunk_length": 95}, "score": 0.5234438180923462, "query": "Stripper Underflow and Compressor Work"}, {"chunk": {"chunk_id": "pdf_9649_chunk_3", "paper_id": "pdf_9649", "paper_title": "PREPRINT 1", "chunk_text": "We apply our proposed compression\nmethod on the Tennessee Eastman Dataset, where we perform\nfault classification using the compressed data in two settings:\na fully supervised one and in a semi supervised, contrastive\nlearning setting. Both times, we were able to outperform real\nvalued counterparts as well as two baseline models: one with\nthe uncompressed time-series as the input and the other with a\nregular downsampling using the mean.", "found_variables": ["compression", "input", "press", "Tennessee Eastman"], "variable_count": 4, "chunk_length": 439}, "score": 0.5187268257141113, "query": "Stripper Underflow and Compressor Work"}, {"chunk": {"chunk_id": "2403.11722v2_chunk_3", "paper_id": "2403.11722v2", "paper_title": "Time Series Compression using Quaternion Valued Neural Networks and\nQuaternion Backpropagation", "chunk_text": "We\napply our proposed compression method on the Tennessee Eastman Dataset, where\nwe perform fault classification using the compressed data in two settings: a\nfully supervised one and in a semi supervised, contrastive learning setting. Both times, we were able to outperform real valued counterparts as well as two\nbaseline models: one with the uncompressed time-series as the input and the\nother with a regular downsampling using the mean.", "found_variables": ["compression", "input", "press", "Tennessee Eastman"], "variable_count": 4, "chunk_length": 439}, "score": 0.5187268257141113, "query": "Stripper Underflow and Compressor Work"}, {"chunk": {"chunk_id": "1706.02043v1_chunk_2", "paper_id": "1706.02043v1", "paper_title": "Reduced chemistry for butanol isomers at engine-relevant conditions", "chunk_text": "During the reduction\nprocess, issues were encountered with pressure-dependent reactions formulated\nusing the logarithmic pressure interpolation approach; these issues are\ndiscussed and recommendations made to avoid ambiguity in its future\nimplementation in mechanism development.", "found_variables": ["press", "pressure"], "variable_count": 2, "chunk_length": 279}, "score": 0.5187140703201294, "query": "Stripper Underflow and Compressor Work"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}