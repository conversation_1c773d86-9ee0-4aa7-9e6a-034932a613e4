{"variable_pair": ["Purge Rate", "Compressor Work"], "results": [{"chunk": {"chunk_id": "2107.08078v1_chunk_4", "paper_id": "2107.08078v1", "paper_title": "A Multi-stage Stochastic Programming Model for Adaptive Biomass\nProcessing Operation under Uncertainty", "chunk_text": "Our sensitivity\nanalysis indicates that updating the infeed rate of the system, the processing\nspeed of equipment, and bale sequencing based on the moisture level of biomass\nimproves the processing rate of the reactor and reduces operating costs.", "found_variables": ["feed", "rate", "level", "reactor"], "variable_count": 4, "chunk_length": 246}, "score": 0.5878627300262451, "query": "Purge Rate and Compressor Work"}, {"chunk": {"chunk_id": "pdf_3256_chunk_3", "paper_id": "pdf_3256", "paper_title": "Two-stage Hydrogen Compression Using Zr-based Metal Hydrides ", "chunk_text": "The purpos e of this study is to find a series of alloys promptly forming \nmetal hydrides (MH) with suitable properties in  order to build a MH-based hydrogen compressor, \nworking in the same way between 20 and ~100 oC.", "found_variables": ["work", "compressor", "press"], "variable_count": 3, "chunk_length": 219}, "score": 0.5754985809326172, "query": "Purge Rate and Compressor Work"}, {"chunk": {"chunk_id": "2101.06993v1_chunk_2", "paper_id": "2101.06993v1", "paper_title": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "chunk_text": "We have\nextensively studied 7 different combinations of compression techniques, all\nmethods achieve high model compression rates over 64% while maintain high fault\ndetection accuracy. The best result is applying all three techniques, which\nreduces the model sizes by 91. 5% and remains a high accuracy over 94%. This\nresult leads to a smaller storage requirement in production environments, and\nmakes the deployment smoother in real world.", "found_variables": ["rate", "compression", "press"], "variable_count": 3, "chunk_length": 439}, "score": 0.5668189525604248, "query": "Purge Rate and Compressor Work"}, {"chunk": {"chunk_id": "pdf_8762_chunk_0", "paper_id": "pdf_8762", "paper_title": "PREDICTIVE NETWORKING AND OPTIMIZATION", "chunk_text": "PREDICTIVE NETWORKING AND OPTIMIZATION", "found_variables": ["work"], "variable_count": 1, "chunk_length": 38}, "score": 0.5601128935813904, "query": "Purge Rate and Compressor Work"}, {"chunk": {"chunk_id": "1207.3541v1_chunk_2", "paper_id": "1207.3541v1", "paper_title": "Two-stage Hydrogen Compression Using Zr-based Metal Hydrides", "chunk_text": "The purpose of this study\nis to find a series of alloys promptly forming metal hydrides (MH) with\nsuitable properties in order to build a MH-based hydrogen compressor, working\nin the same way between 20 and ~100 oC.", "found_variables": ["work", "compressor", "press"], "variable_count": 3, "chunk_length": 215}, "score": 0.5545708537101746, "query": "Purge Rate and Compressor Work"}, {"chunk": {"chunk_id": "pdf_7213_chunk_2", "paper_id": "pdf_7213", "paper_title": "Deep Compression of Neural Networks for Fault", "chunk_text": "In order to implement online real-time fault detection, three\ndeep compression techniques (pruning, clustering, and quantization) are applied to reduce the computational burden. We have\nextensively studied 7 different combinations of compression techniques, all methods achieve high model compression rates over\n64% while maintain high fault detection accuracy. The best result is applying all three techniques, which reduces the model sizes\nby 91. 5% and remains a high accuracy over 94%.", "found_variables": ["rate", "fault detection", "compression", "press"], "variable_count": 4, "chunk_length": 489}, "score": 0.5504575967788696, "query": "Purge Rate and Compressor Work"}, {"chunk": {"chunk_id": "pdf_8178_chunk_3", "paper_id": "pdf_8178", "paper_title": "Optimal Control to Handle Variations in Biomass Feedstock", "chunk_text": "These inconsistencies result in low on-stream times at the reactor in-feed\nequipment. This research develops an optimal process control method for a biomass pre-\nprocessing system comprised of milling and densiﬁcation operations to provide the consistent\nﬂow of feedstock to a reactor’s throat. This method uses a mixed-integer optimization\nmodel to identify optimal bale sequencing, equipment in-feed rate, and buﬀer location and\nsize in the biomass pre-processing system.", "found_variables": ["feed", "rate", "process control", "reactor"], "variable_count": 4, "chunk_length": 473}, "score": 0.536246657371521, "query": "Purge Rate and Compressor Work"}, {"chunk": {"chunk_id": "pdf_4762_chunk_5", "paper_id": "pdf_4762", "paper_title": "A Multi-stage Stochastic Programming Model for Adaptive Biomass Processing", "chunk_text": "We show\nthe value of multi-stage stochastic programming from an extensive computational experiment. Our\nsensitivity analysis indicates that updating the infeed rate of the system, the processing speed of\nequipment, and bale sequencing based on the moisture level of biomass improves the processing rate\nof the reactor and reduces operating costs. KEYWORDS\nbioenergy, biomass, biomass processing system, multi-stage stochastic programming\n1.", "found_variables": ["feed", "rate", "level", "reactor"], "variable_count": 4, "chunk_length": 440}, "score": 0.5336458683013916, "query": "Purge Rate and Compressor Work"}, {"chunk": {"chunk_id": "2103.05025v1_chunk_3", "paper_id": "2103.05025v1", "paper_title": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate", "chunk_text": "Our\ncase study based on switchgrass finds that HPC reduces the variation of a\nreactor's feeding rate by 100\\% without increasing the operating cost of the\nbiomass pre-processing system for biomass with moisture ranging from 10 to\n25\\%. A biorefinery can adapt HPC to achieve its design capacity.", "found_variables": ["feed", "rate", "reactor"], "variable_count": 3, "chunk_length": 295}, "score": 0.5292282700538635, "query": "Purge Rate and Compressor Work"}, {"chunk": {"chunk_id": "pdf_9723_chunk_5", "paper_id": "pdf_9723", "paper_title": "Draft version July 10, 2024", "chunk_text": "Otherwise, the gas-to-core mass ratio (GCR) reaches above ∼10% which is too\nlarge to explain the measured properties of mini-Neptunes, necessitating other gas-limiting processes\nsuch as late-time core assembly. The effect of entropy advection on gas accretion weakens even further\nbeyond 0. 1 AU. We present an updated scaling relation between GCR and the penetration depth of the\nadvective flows which varies non-trivially with orbital distances, core masses and dusty vs. dust-free\nopacity.", "found_variables": ["flow"], "variable_count": 1, "chunk_length": 492}, "score": 0.5284587144851685, "query": "Purge Rate and Compressor Work"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}