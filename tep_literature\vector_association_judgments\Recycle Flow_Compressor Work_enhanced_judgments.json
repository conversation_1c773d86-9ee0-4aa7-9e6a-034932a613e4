{"variable_pair": ["Recycle Flow", "Compressor Work"], "aggregation": {"has_causal_relationship": true, "final_association": "?", "final_direction": "Recycle Flow → Compressor Work", "association_votes": {"A": 1, "?": 7, "⊥": 2, "IA": 5}, "direction_votes": {"Recycle Flow → Compressor Work": 5, "Recycle Flow - Compressor Work": 1}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.65715092420578, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Compressor Work"}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.6264016628265381, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Compressor Work"}, {"chunk_id": "2403.10842v4_chunk_0", "chunk_text": "Twin Transformer using Gated Dynamic Learnable Attention mechanism for\nFault Detection and Diagnosis in the Tennessee Eastman Process", "retrieval_score": 0.6257443428039551, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2004.08702v1_chunk_0", "chunk_text": "Transmission Expansion Planning Using Cycle Flows", "retrieval_score": 0.6204330325126648, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_750_chunk_0", "chunk_text": "Transmission Expansion Planning Using Cycle Flows", "retrieval_score": 0.6204329133033752, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2308.11247v2_chunk_0", "chunk_text": "Benchmarking Domain Adaptation for Chemical Processes on the Tennessee\nEastman Process", "retrieval_score": 0.6125491857528687, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Compressor Work"}, {"chunk_id": "2403.11722v2_chunk_3", "chunk_text": "We\napply our proposed compression method on the Tennessee Eastman Dataset, where\nwe perform fault classification using the compressed data in two settings: a\nfully supervised one and in a semi supervised, contrastive learning setting. Both times, we were able to outperform real valued counterparts as well as two\nbaseline models: one with the uncompressed time-series as the input and the\nother with a regular downsampling using the mean.", "retrieval_score": 0.5983295440673828, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Recycle Flow → Compressor Work"}, {"chunk_id": "pdf_2572_chunk_3", "chunk_text": "We apply our proposed compression\nmethod on the Tennessee Eastman Dataset, where we perform\nfault classification using the compressed data in two settings:\na fully supervised one and in a semi supervised, contrastive\nlearning setting. Both times, we were able to outperform real\nvalued counterparts as well as two baseline models: one with\nthe uncompressed time-series as the input and the other with a\nregular downsampling using the mean.", "retrieval_score": 0.598329484462738, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2403.13502v4_chunk_0", "chunk_text": "Adversarial Attacks and Defenses in Fault Detection and Diagnosis: A\nComprehensive Benchmark on the Tennessee Eastman Process", "retrieval_score": 0.5956705808639526, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow - Compressor Work"}, {"chunk_id": "pdf_3805_chunk_2", "chunk_text": "Abstract: Fault detection and diagnosis (FDD) is a crucial task for ensuring the safety and \nefficiency of industrial processes. We propose a novel FDD methodology for the Tennessee \nEastman Process (TEP), a widely used benchmark for chemical process control. The model \nemploys two separate Transformer branches, enabling independent processing of input data and \npotential extraction of diverse information.", "retrieval_score": 0.5915943384170532, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_2569_chunk_0", "chunk_text": "PREDICTIVE NETWORKING AND OPTIMIZATION", "retrieval_score": 0.5895078182220459, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_131_chunk_0", "chunk_text": "arXiv:2303.05904v1  [cs.LG]  10 Mar 2023Deep Anomaly Detection on Tennessee Eastman Process Data", "retrieval_score": 0.5893466472625732, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2403.10842v4_chunk_1", "chunk_text": "Fault detection and diagnosis (FDD) is a crucial task for ensuring the safety\nand efficiency of industrial processes. We propose a novel FDD methodology for\nthe Tennessee Eastman Process (TEP), a widely used benchmark for chemical\nprocess control. The model employs two separate Transformer branches, enabling\nindependent processing of input data and potential extraction of diverse\ninformation.", "retrieval_score": 0.5851752758026123, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_8440_chunk_1", "chunk_text": "Deep Compression of Neural Networks for Fault\nDetection on Tennessee Eastman Chemical\nProcesses\nMingxuan Li\nDepartment of Computer Science\nUniversity of North Carolina at Chapel Hill\nChapel Hill, NC, USA\nmingxuan li@unc. edu* <PERSON><PERSON>un Shao\nNuro Inc. Mountain View, CA, USA\nyuanxun@gatech. edu\nAbstract\nArtiﬁcial neural network has achieved the state-of-art performance in fault detection on the Tennessee Eastman process, but\nit often requires enormous memory to fund its massive parameters.", "retrieval_score": 0.5834131240844727, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Compressor Work"}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5811416506767273, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}], "original_retrieval_results": 15}