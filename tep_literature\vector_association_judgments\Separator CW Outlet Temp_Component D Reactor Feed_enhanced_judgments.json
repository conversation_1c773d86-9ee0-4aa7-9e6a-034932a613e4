{"variable_pair": ["Separator CW Outlet Temp", "Component D Reactor Feed"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Separator CW Outlet Temp → Component D Reactor Feed", "association_votes": {"A": 4, "⊥": 2, "IA": 6, "?": 3}, "direction_votes": {"Separator CW Outlet Temp → Component D Reactor Feed": 8, "Separator CW Outlet Temp - Component D Reactor Feed": 2}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "1807.03623v1_chunk_0", "chunk_text": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "retrieval_score": 0.5659719705581665, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "1807.03623v1_chunk_1", "chunk_text": "This paper reports on experimental measurements of the gas temperature in a\ndielectric barrier discharge reactor powered by a high voltage pulsed signal. It is shown that the thermal behavior of the reactor follows a first order\nmodel. However, an unexpected runaway phenomenon was observed at a frequency of\n300Hz. A sudden increase in the power source and consequently in reactor\ntemperature which reaches 170{\\deg}C is observed.", "retrieval_score": 0.5528349280357361, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Separator CW Outlet Temp → Component D Reactor Feed"}, {"chunk_id": "pdf_927_chunk_1", "chunk_text": "This paper reports on experimental measurements of the gas temperature in a dielectric barrier \ndischarge reactor powered by a high voltage pulsed signal. It is shown that the thermal \nbehavior of the reactor follows a first order model. However, an unexpected runaway \nphenomenon was observed at a frequency of 300Hz. A sudden increase in the power source \nand consequently in reactor temperature which reach es 170°C is observed.", "retrieval_score": 0.5484391450881958, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Separator CW Outlet Temp → Component D Reactor Feed"}, {"chunk_id": "pdf_6635_chunk_3", "chunk_text": "The\nframework is illustrated via a computational model of the deposition of Cu\nin a Chemical Vapor Deposition (CVD) reactor, where the reactor pressure,\nthe deposition temperature and feed mass \row rate are important process\nparameters that determine the outcome of the process. The sampled ob-\nservations are high-dimensional vectors containing the outputs of a detailed\nCFD steady-state model of the process, i. e.", "retrieval_score": 0.5451005101203918, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Separator CW Outlet Temp - Component D Reactor Feed"}, {"chunk_id": "pdf_2177_chunk_2", "chunk_text": "Eksioglub, <PERSON>ushi <PERSON>c,\naEnergy and Environment Science & Technology Department, Idaho National Laboratory, Idaho Falls, ID\nbIndustrial Engineering Department, University of Arkansas, Fayetteville, AR\ncGlenn Department of Civil Engineering, Clemson University, Clemson, SC\nAbstract\nThe variations in feedstock characteristics such as moisture and particle size distribution\nlead to an inconsistent ﬂow of feedstock from the biomass pre-processing system to the reactor\nin-feed system.", "retrieval_score": 0.5169017314910889, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Separator CW Outlet Temp - Component D Reactor Feed"}, {"chunk_id": "2403.10842v4_chunk_0", "chunk_text": "Twin Transformer using Gated Dynamic Learnable Attention mechanism for\nFault Detection and Diagnosis in the Tennessee Eastman Process", "retrieval_score": 0.5153778791427612, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Separator CW Outlet Temp → Component D Reactor Feed"}, {"chunk_id": "1807.03623v1_chunk_2", "chunk_text": "This behavior is discussed in\nterms of input power variation with temperature, possibly due to a resonance\nphenomenon.", "retrieval_score": 0.5149240493774414, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2212.01432v1_chunk_1", "chunk_text": "Tungsten (W) is a material of choice for the divertor material due to its\nhigh melting temperature, thermal conductivity, and sputtering threshold. However, W has a very high brittle-to-ductile transition temperature and at\nfusion reactor temperatures ($\\geq$1000K) may undergo recrystallization and\ngrain growth.", "retrieval_score": 0.5148805379867554, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Separator CW Outlet Temp → Component D Reactor Feed"}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5140441060066223, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_1764_chunk_3", "chunk_text": "However, W has a very high brittle-to-ductile transition temperature\nand at fusion reactor temperatures ( \u00151000 K) may undergo recrystallization and grain growth. Dispersion-\nstrengthening W with zirconium carbide (ZrC) can improve ductility and limit grain growth, but much of the\neffects of the dispersoids on microstructural evolution and thermomechanical properties at high temperature are\nstill unknown.", "retrieval_score": 0.514004111289978, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_8956_chunk_5", "chunk_text": "(ii)Sis negative in electron-doped compounds\nlike Nd 2−xCexCuO4(NCCO) and LaPr 1−xCexCuO4\n(PCCO), and dS/dT > 0 in the under-doped compound\nat higher temperatures T>∼200 K [2,3]. In both cases,\n|S|increases drastically as the doping decreases. Thus,\na conventional Fermi liquid type behavior, S∝T, is\ntotally violated in high- Tccuprates for a wide range of\ntemperatures. In particular, a qualitative particle-hole\nsymmetric behavior of the TEP, i. e.", "retrieval_score": 0.5118066668510437, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Separator CW Outlet Temp → Component D Reactor Feed"}, {"chunk_id": "0402435v1_chunk_0", "chunk_text": "The Indo-U.S. Library of Coude Feed Stellar Spectra", "retrieval_score": 0.5109553337097168, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Separator CW Outlet Temp → Component D Reactor Feed"}, {"chunk_id": "pdf_5293_chunk_3", "chunk_text": "As the applied gate voltage changes, the TEP shows dist inctly diﬀerent behaviors while the\nelectrical conductance exhibits the turn-oﬀ, subthreshol d, and saturation regimes respectively. At\nroom temperature, peak TEP value of ∼300µV/K is observed in the subthreshold regime of the Si\ndevices. The temperature dependence of the saturated TEP va lues are used to estimate the carrier\ndoping of Si nanowires.", "retrieval_score": 0.5082793831825256, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "9902259v1_chunk_1", "chunk_text": "We start with revisiting our previous results on thermoelectric response of\nSNS configuration in a C-shaped Bi(x)Pb(1-x)Sr(2)CaCu(2)O(y) sample in order to\ninclude strong fluctuation effects. Then, by appropriate generalization of the\nGinzburg-Landau theory based on admixture of s-wave and d-wave superconductors,\nwe consider a differential thermoelectric power (TEP) of SND junction.", "retrieval_score": 0.5081693530082703, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Separator CW Outlet Temp → Component D Reactor Feed"}, {"chunk_id": "pdf_7737_chunk_1", "chunk_text": "Abstract:  A sequential trajectory linearized adaptive model based predictive controller is designed \nusing the DMC algorithm to control the temperature of a batch MMA polymerization process. Using the mechanistic model of the polymerization, a parametric transfer function is derived to \nrelate the reactor temperature to the power of the heaters. Then, a multiple model predictive control approach is taken in to track a desired temperature trajec tory.", "retrieval_score": 0.5079770088195801, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Separator CW Outlet Temp → Component D Reactor Feed"}], "original_retrieval_results": 15}