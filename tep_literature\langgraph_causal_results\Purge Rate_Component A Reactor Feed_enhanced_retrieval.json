{"variable_pair": ["Purge Rate", "Component A Reactor Feed"], "results": [{"chunk": {"chunk_id": "2107.08078v1_chunk_4", "paper_id": "2107.08078v1", "paper_title": "A Multi-stage Stochastic Programming Model for Adaptive Biomass\nProcessing Operation under Uncertainty", "chunk_text": "Our sensitivity\nanalysis indicates that updating the infeed rate of the system, the processing\nspeed of equipment, and bale sequencing based on the moisture level of biomass\nimproves the processing rate of the reactor and reduces operating costs.", "found_variables": ["feed", "rate", "level", "reactor"], "variable_count": 4, "chunk_length": 246}, "score": 0.5982422232627869, "query": "Purge Rate influence Component A Reactor Feed"}, {"chunk": {"chunk_id": "pdf_6362_chunk_5", "paper_id": "pdf_6362", "paper_title": "Correlation between radiation damage and magnetic properties in reactor vessel", "chunk_text": "Key Word s: reactorpressure vessel (RPV), neutron irradiation, dose -rate effects,\nmagnetic properties of steels. 1. Introduction\nTheintegrity of the components of power plant reactors throughout their service life is\naffected by the degradation suffered by t.", "found_variables": ["pressure", "rate", "press", "reactor", "component"], "variable_count": 5, "chunk_length": 260}, "score": 0.574211061000824, "query": "Purge Rate influence Component A Reactor Feed"}, {"chunk": {"chunk_id": "pdf_6362_chunk_0", "paper_id": "pdf_6362", "paper_title": "Correlation between radiation damage and magnetic properties in reactor vessel", "chunk_text": "Correlation between radiation damage and magnetic properties in reactor vessel", "found_variables": ["reactor"], "variable_count": 1, "chunk_length": 78}, "score": 0.5674751996994019, "query": "relationship between Purge Rate and Component A Reactor Feed"}, {"chunk": {"chunk_id": "pdf_8178_chunk_2", "paper_id": "pdf_8178", "paper_title": "Optimal Control to Handle Variations in Biomass Feedstock", "chunk_text": "Eksioglub, <PERSON>ushi <PERSON>c,\naEnergy and Environment Science & Technology Department, Idaho National Laboratory, Idaho Falls, ID\nbIndustrial Engineering Department, University of Arkansas, Fayetteville, AR\ncGlenn Department of Civil Engineering, Clemson University, Clemson, SC\nAbstract\nThe variations in feedstock characteristics such as moisture and particle size distribution\nlead to an inconsistent ﬂow of feedstock from the biomass pre-processing system to the reactor\nin-feed system.", "found_variables": ["feed", "reactor"], "variable_count": 2, "chunk_length": 485}, "score": 0.566478967666626, "query": "Purge Rate and Component A Reactor Feed"}, {"chunk": {"chunk_id": "2103.05025v1_chunk_0", "paper_id": "2103.05025v1", "paper_title": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate", "chunk_text": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate", "found_variables": ["feed", "rate", "reactor"], "variable_count": 3, "chunk_length": 98}, "score": 0.5574374794960022, "query": "Purge Rate and Component A Reactor Feed"}, {"chunk": {"chunk_id": "pdf_8086_chunk_5", "paper_id": "pdf_8086", "paper_title": "1 Predictions and Uncertainty Estimates of Reactor Pressure Vessel Steel Embrittlement Using  ", "chunk_text": "Furthermore, our detailed \ncomparisons show our approach improves on the leading American Society for Testing and \nMaterials (ASTM) E900 -15 standard model for RPV embrittlement on every metric we assessed, \ndemonstrating th e efficacy of machine learning approaches for this type of highly demanding \nmaterials property prediction.\n\n1. Introduction:  \nNuclear power is a key component of global clean energy production, producing roughly \n20% of the power", "found_variables": ["component"], "variable_count": 1, "chunk_length": 456}, "score": 0.5463501214981079, "query": "Purge Rate influence Component A Reactor Feed"}, {"chunk": {"chunk_id": "2103.05025v1_chunk_3", "paper_id": "2103.05025v1", "paper_title": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate", "chunk_text": "Our\ncase study based on switchgrass finds that HPC reduces the variation of a\nreactor's feeding rate by 100\\% without increasing the operating cost of the\nbiomass pre-processing system for biomass with moisture ranging from 10 to\n25\\%. A biorefinery can adapt HPC to achieve its design capacity.", "found_variables": ["feed", "rate", "reactor"], "variable_count": 3, "chunk_length": 295}, "score": 0.5424131751060486, "query": "effect of Purge Rate on Component A Reactor Feed"}, {"chunk": {"chunk_id": "1701.06551v1_chunk_2", "paper_id": "1701.06551v1", "paper_title": "On the Parametric Study of Lubricating Oil Production using an\nArtificial Neural Network (ANN) Approach", "chunk_text": "A feed-forward Multi-Layer Perceptron Neural Network was\nsuccessfully applied to capture the relationship between inputs and output\nparameters.", "found_variables": ["work", "A Feed", "input", "feed"], "variable_count": 4, "chunk_length": 143}, "score": 0.5410027503967285, "query": "Purge Rate Component A Reactor Feed interaction"}, {"chunk": {"chunk_id": "pdf_9386_chunk_2", "paper_id": "pdf_9386", "paper_title": "On the Parametric Study of Lubricating Oil Production using an ", "chunk_text": "A feed-forward Multi -Layer Perceptron Neural Network was successfully \napplied to capture the relationship b etween  inputs and output parameters.\n\nKeywords: Solvent extraction,  Lubricating oil, Machine Learning, Artificial Neural Network  \n(ANN)", "found_variables": ["input", "work", "vent", "A Feed", "feed"], "variable_count": 5, "chunk_length": 248}, "score": 0.5390709042549133, "query": "Purge Rate Component A Reactor Feed interaction"}, {"chunk": {"chunk_id": "pdf_8178_chunk_3", "paper_id": "pdf_8178", "paper_title": "Optimal Control to Handle Variations in Biomass Feedstock", "chunk_text": "These inconsistencies result in low on-stream times at the reactor in-feed\nequipment. This research develops an optimal process control method for a biomass pre-\nprocessing system comprised of milling and densiﬁcation operations to provide the consistent\nﬂow of feedstock to a reactor’s throat. This method uses a mixed-integer optimization\nmodel to identify optimal bale sequencing, equipment in-feed rate, and buﬀer location and\nsize in the biomass pre-processing system.", "found_variables": ["feed", "rate", "process control", "reactor"], "variable_count": 4, "chunk_length": 473}, "score": 0.5364989042282104, "query": "Purge Rate Component A Reactor Feed interaction"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}