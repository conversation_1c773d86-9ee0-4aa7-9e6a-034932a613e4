{"variable_pair": ["<PERSON> Feed", "Product Sep Level"], "results": [{"chunk": {"chunk_id": "pdf_729_chunk_0", "paper_id": "pdf_729", "paper_title": "Geometric Decomposition of Feed Forward", "chunk_text": "Geometric Decomposition of Feed Forward", "found_variables": ["composition", "feed"], "variable_count": 2, "chunk_length": 39}, "score": 0.5226064920425415, "query": "D Feed and Product Sep Level"}, {"chunk": {"chunk_id": "pdf_6655_chunk_0", "paper_id": "pdf_6655", "paper_title": "Feed-forward neural network unfolding", "chunk_text": "Feed-forward neural network unfolding", "found_variables": ["work", "feed"], "variable_count": 2, "chunk_length": 37}, "score": 0.5213103890419006, "query": "D Feed and Product Sep Level"}, {"chunk": {"chunk_id": "2112.08180v1_chunk_0", "paper_id": "2112.08180v1", "paper_title": "Feed-forward neural network unfolding", "chunk_text": "Feed-forward neural network unfolding", "found_variables": ["work", "feed"], "variable_count": 2, "chunk_length": 37}, "score": 0.5213103890419006, "query": "D Feed and Product Sep Level"}, {"chunk": {"chunk_id": "2211.13000v1_chunk_3", "paper_id": "2211.13000v1", "paper_title": "A Network Classification Method based on Density Time Evolution Patterns\nExtracted from Network Automata", "chunk_text": "Therefore, in this\npaper, we propose alternate sources of information to use as descriptor for the\nclassification task, which we denominate as density time-evolution pattern\n(D-TEP) and state density time-evolution pattern (SD-TEP). We explore the\ndensity of alive neighbors of each node, which is a continuous value, and\ncompute feature vectors based on histograms of the TEPs.", "found_variables": ["TEP"], "variable_count": 1, "chunk_length": 378}, "score": 0.5186243653297424, "query": "D Feed and Product Sep Level"}, {"chunk": {"chunk_id": "pdf_3147_chunk_0", "paper_id": "pdf_3147", "paper_title": "On the space of coefﬁcients of a Feed Forward", "chunk_text": "On the space of coefﬁcients of a Feed Forward", "found_variables": ["A Feed", "feed"], "variable_count": 2, "chunk_length": 45}, "score": 0.5174028873443604, "query": "D Feed and Product Sep Level"}, {"chunk": {"chunk_id": "pdf_6712_chunk_0", "paper_id": "pdf_6712", "paper_title": "Development of a Feeding Assistive Robot Using a Six Degree of", "chunk_text": "Development of a Feeding Assistive Robot Using a Six Degree of", "found_variables": ["A Feed", "feed"], "variable_count": 2, "chunk_length": 62}, "score": 0.5066658854484558, "query": "D Feed and Product Sep Level"}, {"chunk": {"chunk_id": "pdf_5820_chunk_0", "paper_id": "pdf_5820", "paper_title": "Bilevel Optimization Based Transmission Expansion", "chunk_text": "Bilevel Optimization Based Transmission Expansion", "found_variables": ["level"], "variable_count": 1, "chunk_length": 49}, "score": 0.5052056908607483, "query": "D Feed and Product Sep Level"}, {"chunk": {"chunk_id": "pdf_9055_chunk_4", "paper_id": "pdf_9055", "paper_title": "arXiv:2211.13000v1  [cs.SI]  18 Nov 2022A Network Classiﬁcation Method based on Density", "chunk_text": "However, the TEPs e xplored by\nprevious studies are composed of binary values, which does not rep resent\ndetailed information on the network analyzed. Therefore, in this pa per, we\npropose alternate sources of information to use as descriptor fo r the classiﬁ-\ncationtask, whichwe denominateasdensity time-evolutionpattern (D-TEP)\nPreprint submitted to Pattern Recognition November 24, 202 2\nand state density time-evolution pattern (SD-TEP).", "found_variables": ["work", "TEP"], "variable_count": 2, "chunk_length": 442}, "score": 0.5042204856872559, "query": "D Feed and Product Sep Level"}, {"chunk": {"chunk_id": "pdf_7507_chunk_5", "paper_id": "pdf_7507", "paper_title": "arXiv:cond-mat/0107360v2  [cond-mat.str-el]  31 Jul 2001Theory of Thermoelectric Power in High- TcSuperconductors", "chunk_text": "(ii)Sis negative in electron-doped compounds\nlike Nd 2−xCexCuO4(NCCO) and LaPr 1−xCexCuO4\n(PCCO), and dS/dT > 0 in the under-doped compound\nat higher temperatures T>∼200 K [2,3]. In both cases,\n|S|increases drastically as the doping decreases. Thus,\na conventional Fermi liquid type behavior, S∝T, is\ntotally violated in high- Tccuprates for a wide range of\ntemperatures. In particular, a qualitative particle-hole\nsymmetric behavior of the TEP, i. e.", "found_variables": ["temperature", "rate", "temp", "TEP", "vent"], "variable_count": 5, "chunk_length": 451}, "score": 0.5037518739700317, "query": "D Feed and Product Sep Level"}, {"chunk": {"chunk_id": "2001.01687v1_chunk_0", "paper_id": "2001.01687v1", "paper_title": "A Supervised Modified Hebbian Learning Method On Feed-forward Neural\nNetworks", "chunk_text": "A Supervised Modified Hebbian Learning Method On Feed-forward Neural\nNetworks", "found_variables": ["work", "feed"], "variable_count": 2, "chunk_length": 77}, "score": 0.5001524686813354, "query": "D Feed and Product Sep Level"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}