{"variable_pair": ["Stripper Temperature", "Component A Purge Gas"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Stripper Temperature → Component A Purge Gas", "association_votes": {"A": 5, "⊥": 1, "IA": 7, "?": 2}, "direction_votes": {"Stripper Temperature → Component A Purge Gas": 7, "Stripper Temperature - Component A Purge Gas": 5}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "1911.04386v2_chunk_4", "chunk_text": "The\noutstanding performance of this method is demonstrated and contrasted to\n(dynamic) principal component analysis, which are widely applied in the\nindustry, in the benchmark Tennessee Eastman process~(TEP) and a real chemical\nmanufacturing dataset.", "retrieval_score": 0.542499303817749, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Temperature - Component A Purge Gas"}, {"chunk_id": "pdf_927_chunk_4", "chunk_text": "A \ngreat deal of experimental, theoretical and numeric al works has been conducted so far and \nhelped us to better understand these processes [6]. Whatever the concerned application, gas \ntemperature has a great influence on the electrical  parameters and on the nature and \nproduction of primary species formed by plasma  and must then play an important role. The increase of temperature leads to modify current  and voltage shapes as it has been shown \nin [7].", "retrieval_score": 0.5358977913856506, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Temperature → Component A Purge Gas"}, {"chunk_id": "pdf_9735_chunk_4", "chunk_text": "We evaluate our approach using the\nTennessee-Eastman process. Results show that our approach\ncan be used to distinguish disturbances from intrusions to a\ncertain extent and we conclude that the proposed approach can\nbe extended with other sources of data for improving results. Keywords -Process control systems, Multivariate Statistical\nProcess Control, Tennessee-Eastman,\nI.", "retrieval_score": 0.5354777574539185, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Temperature → Component A Purge Gas"}, {"chunk_id": "pdf_9936_chunk_3", "chunk_text": "The purpos e of this study is to find a series of alloys promptly forming \nmetal hydrides (MH) with suitable properties in  order to build a MH-based hydrogen compressor, \nworking in the same way between 20 and ~100 oC.", "retrieval_score": 0.534145712852478, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Temperature → Component A Purge Gas"}, {"chunk_id": "pdf_9406_chunk_5", "chunk_text": "The performance of the method is demonstrated\nand contrasted to (dynamic) principal component analysis, which is widely applied in the industry,\nin the benchmark Tennessee Eastman process (TEP) and a real chemical manufacturing dataset. Keywords: fault detection, fault identi\fcation, recurrent neural networks, variational dropout,\nBayesian inference, Tennessee Eastman process. 1.", "retrieval_score": 0.5335231423377991, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5283735990524292, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Temperature → Component A Purge Gas"}, {"chunk_id": "2203.11321v1_chunk_4", "chunk_text": "Finally, as a case study, the proposed\nmodel is implemented in the well-known Tennessee Eastman process, and the\nresults are presented.", "retrieval_score": 0.5270835757255554, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Temperature → Component A Purge Gas"}, {"chunk_id": "2308.11247v2_chunk_0", "chunk_text": "Benchmarking Domain Adaptation for Chemical Processes on the Tennessee\nEastman Process", "retrieval_score": 0.5220601558685303, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Temperature - Component A Purge Gas"}, {"chunk_id": "2212.04140v2_chunk_3", "chunk_text": "The effectiveness of the switching strategy is\nalso demonstrated via numerical simulation on the Tennessee Eastman Process.", "retrieval_score": 0.5186819434165955, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_5519_chunk_3", "chunk_text": "The temperature dependence of\nthe TEP shows diffusive linear metallic TEP becoming temperature independent\nbelow 40K. Unlike the others who used Cu(ClO4)2 for the ClO4- doping, the initial\ndoping material we used is anhydrous Fe(ClO4)3 which is crucial to obtain the\npositive TCR from T=1. 5K to 300K.", "retrieval_score": 0.5157538652420044, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Temperature → Component A Purge Gas"}, {"chunk_id": "pdf_9212_chunk_1", "chunk_text": "Ther mopower meas urements have been carried out on the Ni subst ituted samples of Na0. 75CoO2in the\ntemper ature r ange 4. 2K to 300K. The roo m temperature TEP i ncreases by 20µV/K even with 1% Ni\nsubstitu tion and systema tically in creases with  increasing Ni con tent upto 5%. The in crease in TEP is\naccomp anied by a  decrea se in ρ thus increasing the ratio of S2/ρ on Ni substitu tion. At low T, t he TEP sh ows\nan anomaly in the substituted samples, sho wing a p eak at T~ 20 K.", "retrieval_score": 0.5149815082550049, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Temperature - Component A Purge Gas"}, {"chunk_id": "pdf_927_chunk_3", "chunk_text": "Non thermal plasmas which can be generated at atmos pheric or lower pressures are in non-\nequilibrium state so that the gas temperature is co nsidered to be at a temperature only  slightly \nhigher than the ambient temperature while electrons  temperature is about 10000K. In these \nplasmas, free radicals (O°, HO°. ) are formed  and can be used in many applications such as \npollution abatement [1-3], surface treatment [4] or  chemical vapor deposition (CVD) [5].", "retrieval_score": 0.5141443014144897, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2210.14595v1_chunk_4", "chunk_text": "Finally, we demonstrate the\neffectiveness of the proposed switching strategy via numerical simulation on\nthe Tennessee Eastman Process.", "retrieval_score": 0.5129923224449158, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Temperature → Component A Purge Gas"}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.5122178792953491, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Temperature - Component A Purge Gas"}, {"chunk_id": "pdf_3805_chunk_2", "chunk_text": "Abstract: Fault detection and diagnosis (FDD) is a crucial task for ensuring the safety and \nefficiency of industrial processes. We propose a novel FDD methodology for the Tennessee \nEastman Process (TEP), a widely used benchmark for chemical process control. The model \nemploys two separate Transformer branches, enabling independent processing of input data and \npotential extraction of diverse information.", "retrieval_score": 0.5111501216888428, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Temperature - Component A Purge Gas"}], "original_retrieval_results": 15}