{"variable_pair": ["Recycle Flow", "Reactor Feed Rate"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Recycle Flow → Reactor Feed Rate", "association_votes": {"A": 3, "⊥": 1, "?": 5, "IA": 6}, "direction_votes": {"Recycle Flow - Reactor Feed Rate": 1, "Recycle Flow → Reactor Feed Rate": 8}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "pdf_2177_chunk_2", "chunk_text": "Eksioglub, <PERSON>ushi <PERSON>c,\naEnergy and Environment Science & Technology Department, Idaho National Laboratory, Idaho Falls, ID\nbIndustrial Engineering Department, University of Arkansas, Fayetteville, AR\ncGlenn Department of Civil Engineering, Clemson University, Clemson, SC\nAbstract\nThe variations in feedstock characteristics such as moisture and particle size distribution\nlead to an inconsistent ﬂow of feedstock from the biomass pre-processing system to the reactor\nin-feed system.", "retrieval_score": 0.6410871744155884, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Reactor Feed Rate"}, {"chunk_id": "2103.05025v1_chunk_0", "chunk_text": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate", "retrieval_score": 0.636081337928772, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow - Reactor Feed Rate"}, {"chunk_id": "2107.08078v1_chunk_4", "chunk_text": "Our sensitivity\nanalysis indicates that updating the infeed rate of the system, the processing\nspeed of equipment, and bale sequencing based on the moisture level of biomass\nimproves the processing rate of the reactor and reduces operating costs.", "retrieval_score": 0.6110060214996338, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Reactor Feed Rate"}, {"chunk_id": "2103.05025v1_chunk_1", "chunk_text": "The variations in feedstock characteristics such as moisture and particle\nsize distribution lead to an inconsistent flow of feedstock from the biomass\npre-processing system to the reactor in-feed system. These inconsistencies\nresult in low on-stream times at the reactor in-feed equipment. This research\ndevelops an optimal process control method for a biomass pre-processing system\ncomprised of milling and densification operations to provide the consistent\nflow of feedstock to a reactor's throat.", "retrieval_score": 0.5897605419158936, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Reactor Feed Rate"}, {"chunk_id": "pdf_2177_chunk_3", "chunk_text": "These inconsistencies result in low on-stream times at the reactor in-feed\nequipment. This research develops an optimal process control method for a biomass pre-\nprocessing system comprised of milling and densiﬁcation operations to provide the consistent\nﬂow of feedstock to a reactor’s throat. This method uses a mixed-integer optimization\nmodel to identify optimal bale sequencing, equipment in-feed rate, and buﬀer location and\nsize in the biomass pre-processing system.", "retrieval_score": 0.5651724934577942, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Reactor Feed Rate"}, {"chunk_id": "pdf_2177_chunk_1", "chunk_text": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate\n<PERSON><PERSON><PERSON>, <PERSON>,1, <PERSON>.", "retrieval_score": 0.5590246915817261, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2103.05025v1_chunk_3", "chunk_text": "Our\ncase study based on switchgrass finds that HPC reduces the variation of a\nreactor's feeding rate by 100\\% without increasing the operating cost of the\nbiomass pre-processing system for biomass with moisture ranging from 10 to\n25\\%. A biorefinery can adapt HPC to achieve its design capacity.", "retrieval_score": 0.5582305192947388, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_7578_chunk_0", "chunk_text": "Correlation between radiation damage and magnetic properties in reactor vessel", "retrieval_score": 0.555517315864563, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2406.09186v2_chunk_0", "chunk_text": "A formation pathway for terrestrial planets with moderate water content\ninvolving atmospheric-volatile recycling", "retrieval_score": 0.5478694438934326, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "1807.03623v1_chunk_0", "chunk_text": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "retrieval_score": 0.5435152053833008, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Reactor Feed Rate"}, {"chunk_id": "pdf_1999_chunk_5", "chunk_text": "We show\nthe value of multi-stage stochastic programming from an extensive computational experiment. Our\nsensitivity analysis indicates that updating the infeed rate of the system, the processing speed of\nequipment, and bale sequencing based on the moisture level of biomass improves the processing rate\nof the reactor and reduces operating costs. KEYWORDS\nbioenergy, biomass, biomass processing system, multi-stage stochastic programming\n1.", "retrieval_score": 0.5335620641708374, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Recycle Flow → Reactor Feed Rate"}, {"chunk_id": "pdf_750_chunk_0", "chunk_text": "Transmission Expansion Planning Using Cycle Flows", "retrieval_score": 0.5334359407424927, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Recycle Flow → Reactor Feed Rate"}, {"chunk_id": "2004.08702v1_chunk_0", "chunk_text": "Transmission Expansion Planning Using Cycle Flows", "retrieval_score": 0.5334359407424927, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_819_chunk_0", "chunk_text": "Geometric Decomposition of Feed Forward", "retrieval_score": 0.5298240780830383, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2310.03117v1_chunk_3", "chunk_text": "We incorporate\nthese 3D hydrodynamic effects into an extensible 1D framework with a physically\nmotivated three-layer recycling parameterization. Specializing to the case of\nJupiter, recycling produces minimal changes to the growth rate with the planet\nstill entering runaway accretion and becoming a gas giant in ~1 Myr. Even in\nthe inner disk (0. 1 AU), our 1D models suggest that recycling is not so robust\nand ubiquitous as to stop all cores from becoming giants.", "retrieval_score": 0.5280134677886963, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Recycle Flow → Reactor Feed Rate"}], "original_retrieval_results": 15}