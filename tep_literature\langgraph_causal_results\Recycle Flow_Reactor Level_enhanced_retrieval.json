{"variable_pair": ["Recycle Flow", "Reactor Level"], "results": [{"chunk": {"chunk_id": "pdf_6362_chunk_0", "paper_id": "pdf_6362", "paper_title": "Correlation between radiation damage and magnetic properties in reactor vessel", "chunk_text": "Correlation between radiation damage and magnetic properties in reactor vessel", "found_variables": ["reactor"], "variable_count": 1, "chunk_length": 78}, "score": 0.5683375000953674, "query": "correlation Recycle Flow Reactor Level"}, {"chunk": {"chunk_id": "2103.05025v1_chunk_0", "paper_id": "2103.05025v1", "paper_title": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate", "chunk_text": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate", "found_variables": ["feed", "rate", "reactor"], "variable_count": 3, "chunk_length": 98}, "score": 0.5629375576972961, "query": "Recycle Flow and Reactor Level"}, {"chunk": {"chunk_id": "2406.09186v2_chunk_0", "paper_id": "2406.09186v2", "paper_title": "A formation pathway for terrestrial planets with moderate water content\ninvolving atmospheric-volatile recycling", "chunk_text": "A formation pathway for terrestrial planets with moderate water content\ninvolving atmospheric-volatile recycling", "found_variables": ["rate"], "variable_count": 1, "chunk_length": 112}, "score": 0.5623984932899475, "query": "Recycle Flow and Reactor Level"}, {"chunk": {"chunk_id": "pdf_8178_chunk_2", "paper_id": "pdf_8178", "paper_title": "Optimal Control to Handle Variations in Biomass Feedstock", "chunk_text": "Eksioglub, <PERSON>ushi <PERSON>c,\naEnergy and Environment Science & Technology Department, Idaho National Laboratory, Idaho Falls, ID\nbIndustrial Engineering Department, University of Arkansas, Fayetteville, AR\ncGlenn Department of Civil Engineering, Clemson University, Clemson, SC\nAbstract\nThe variations in feedstock characteristics such as moisture and particle size distribution\nlead to an inconsistent ﬂow of feedstock from the biomass pre-processing system to the reactor\nin-feed system.", "found_variables": ["feed", "reactor"], "variable_count": 2, "chunk_length": 485}, "score": 0.5484956502914429, "query": "Recycle Flow and Reactor Level"}, {"chunk": {"chunk_id": "2310.03117v1_chunk_3", "paper_id": "2310.03117v1", "paper_title": "Growing Planet Envelopes in Spite of Recycling Flows", "chunk_text": "We incorporate\nthese 3D hydrodynamic effects into an extensible 1D framework with a physically\nmotivated three-layer recycling parameterization. Specializing to the case of\nJupiter, recycling produces minimal changes to the growth rate with the planet\nstill entering runaway accretion and becoming a gas giant in ~1 Myr. Even in\nthe inner disk (0. 1 AU), our 1D models suggest that recycling is not so robust\nand ubiquitous as to stop all cores from becoming giants.", "found_variables": ["rate", "work"], "variable_count": 2, "chunk_length": 466}, "score": 0.54493647813797, "query": "effect of Recycle Flow on Reactor Level"}, {"chunk": {"chunk_id": "2107.08078v1_chunk_4", "paper_id": "2107.08078v1", "paper_title": "A Multi-stage Stochastic Programming Model for Adaptive Biomass\nProcessing Operation under Uncertainty", "chunk_text": "Our sensitivity\nanalysis indicates that updating the infeed rate of the system, the processing\nspeed of equipment, and bale sequencing based on the moisture level of biomass\nimproves the processing rate of the reactor and reduces operating costs.", "found_variables": ["feed", "rate", "level", "reactor"], "variable_count": 4, "chunk_length": 246}, "score": 0.5439715385437012, "query": "Recycle Flow and Reactor Level"}, {"chunk": {"chunk_id": "1807.03623v1_chunk_0", "paper_id": "1807.03623v1", "paper_title": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "chunk_text": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "found_variables": ["temperature", "temp", "reactor"], "variable_count": 3, "chunk_length": 68}, "score": 0.5396299362182617, "query": "Recycle Flow and Reactor Level"}, {"chunk": {"chunk_id": "pdf_1850_chunk_5", "paper_id": "pdf_1850", "paper_title": "Typeset using L ATEXtwocolumn style in AASTeX631", "chunk_text": "We incor-\nporate these 3D hydrodynamic effects into an extensible 1D framework with a physically motivated\nthree-layer recycling parameterization. Specializing to the case of Jupiter, recycling produces minimal\nchanges to the growth rate with the planet still entering runaway accretion and becoming a gas giant\nin∼1 Myr. Even in the inner disk (0. 1 AU), our 1D models suggest that recycling is not so robust\nand ubiquitous as to stop all cores from becoming giants.", "found_variables": ["rate", "work"], "variable_count": 2, "chunk_length": 467}, "score": 0.5388652086257935, "query": "effect of Recycle Flow on Reactor Level"}, {"chunk": {"chunk_id": "pdf_9723_chunk_4", "paper_id": "pdf_9723", "paper_title": "Draft version July 10, 2024", "chunk_text": "Here, we reevaluate this line of reasoning by incorporating recycling flows of gas\ninto a numerical one-dimensional thermodynamic model with more realistic equation of state and\nopacities and the thermal state of the advective flow. At 0. 1 AU, we find that advective flows are\nonly able to produce mini-Neptunes if they can penetrate below ∼0. 25 of the planet’s gravitational\nsphere of influence.", "found_variables": ["rate", "thermal", "flow"], "variable_count": 3, "chunk_length": 398}, "score": 0.5297408103942871, "query": "Recycle Flow and Reactor Level"}, {"chunk": {"chunk_id": "pdf_6206_chunk_4", "paper_id": "pdf_6206", "paper_title": "MNRAS 000, 1{24 (2021) Preprint 3 September 2021 Compiled using MNRAS L ATEX style \fle v3.0", "chunk_text": "Using a passive scalar, we observe sig-\nni\fcant mass recycling on the orbital timescale. For a radiative envelope, recycling\ncan only penetrate from the disc surface until \u00180. 1-0. 2 planetary Hill radii, while for\na convective envelope, the convective motion can \\dredge up\" the deeper part of the\nenvelope so that the entire convective envelope is recycled e\u000eciently. This recycling,\nhowever, has only limited e\u000bects on the envelopes' thermal structure.", "found_variables": ["rate", "thermal"], "variable_count": 2, "chunk_length": 455}, "score": 0.5262715816497803, "query": "Recycle Flow and Reactor Level"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}