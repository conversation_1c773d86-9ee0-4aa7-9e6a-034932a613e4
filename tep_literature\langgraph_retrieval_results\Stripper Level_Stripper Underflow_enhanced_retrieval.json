{"variable_pair": ["Stripper Level", "Stripper Underflow"], "results": [{"chunk": {"chunk_id": "pdf_5520_chunk_3", "paper_id": "pdf_5520", "paper_title": "IEEE TRANSACTIONS ON NEURAL NETWORKS AND LEARNING SYSTEMS 1", "chunk_text": "Finally, the model decision is biased toward the new classes\ndue to the class imbalance. The problems can consequently\nlead to performance degradation of fault diagnosis models. Accordingly, we introduce a supervised contrastive knowledge\ndistillation for incremental fault diagnosis under limited fault\ndata (SCLIFD) framework to address these issues, which extends\nthe classical incremental classiﬁer and representation learning\n(iCaRL) framework from three perspectives.", "found_variables": ["distillation", "work"], "variable_count": 2, "chunk_length": 473}, "score": 0.5112118124961853, "query": "correlation Stripper Level Stripper Underflow"}, {"chunk": {"chunk_id": "2302.05929v1_chunk_0", "paper_id": "2302.05929v1", "paper_title": "SCLIFD:Supervised Contrastive Knowledge Distillation for Incremental\nFault Diagnosis under Limited Fault Data", "chunk_text": "SCLIFD:Supervised Contrastive Knowledge Distillation for Incremental\nFault Diagnosis under Limited Fault Data", "found_variables": ["distillation"], "variable_count": 1, "chunk_length": 109}, "score": 0.506989061832428, "query": "correlation Stripper Level Stripper Underflow"}, {"chunk": {"chunk_id": "pdf_9863_chunk_4", "paper_id": "pdf_9863", "paper_title": "arXiv:0810.4342v1  [cond-mat.mes-hall]  24 Oct 2008Slow imbalance relaxation and thermoelectric transport in graphene", "chunk_text": "For specimens shorter than lQ, the population imbalance extends throughout the sample; κandα\nasymptote toward their zero imbalance relaxation limits. I n the opposite limit of a graphene slab\nlonger than lQ, at non-zero doping κandαapproach intrinsic values characteristic of the inﬁnite\nimbalance relaxation limit. Samples of intermediate (long ) length in the doped (undoped) case are\npredicted to exhibit an inhomogeneous temperature proﬁle, whilstκandαgrow linearly with the\nsystemsize.", "found_variables": ["temperature", "temp"], "variable_count": 2, "chunk_length": 490}, "score": 0.****************, "query": "Stripper Level and Stripper Underflow"}, {"chunk": {"chunk_id": "pdf_4923_chunk_2", "paper_id": "pdf_4923", "paper_title": "Bayesian Sparsiﬁcation of Recurrent Neural Networks", "chunk_text": "We\napply this technique to sparsify recurrent neu-\nral networks. To account for recurrent speciﬁcs\nwe also rely on Binary Variational Dropout for\nRNN (Gal & Gha<PERSON>, 2016b). We report\n99. 5% sparsity level on sentiment analysis task\nwithout a quality drop and up to 87% sparsity\nlevel on language modeling task with slight loss\nof accuracy. 1.", "found_variables": ["work", "level"], "variable_count": 2, "chunk_length": 346}, "score": 0.***************, "query": "correlation Stripper Level Stripper Underflow"}, {"chunk": {"chunk_id": "pdf_2902_chunk_0", "paper_id": "pdf_2902", "paper_title": "A N OVEL DEEPPARALLEL TIME-SERIES RELATION NETWORK", "chunk_text": "A N OVEL DEEPPARALLEL TIME-SERIES RELATION NETWORK", "found_variables": ["work"], "variable_count": 1, "chunk_length": 50}, "score": 0.****************, "query": "Stripper Level Stripper Underflow interaction"}, {"chunk": {"chunk_id": "0810.4342v1_chunk_4", "paper_id": "0810.4342v1", "paper_title": "Slow imbalance relaxation and thermoelectric transport in graphene", "chunk_text": "For specimens shorter than l_Q, the population imbalance extends throughout the\nsample; the TC and TEP asymptote toward their zero imbalance relaxation limits. In the opposite limit of a graphene slab longer than l_Q, at non-zero doping\nthe TC and TEP approach intrinsic values characteristic of the infinite\nimbalance relaxation limit.", "found_variables": ["TEP"], "variable_count": 1, "chunk_length": 336}, "score": 0.49973535537719727, "query": "Stripper Level and Stripper Underflow"}, {"chunk": {"chunk_id": "pdf_5520_chunk_1", "paper_id": "pdf_5520", "paper_title": "IEEE TRANSACTIONS ON NEURAL NETWORKS AND LEARNING SYSTEMS 1", "chunk_text": "IEEE TRANSACTIONS ON NEURAL NETWORKS AND LEARNING SYSTEMS 1\nSCLIFD:Supervised Contrastive Knowledge\nDistillation for Incremental Fault Diagnosis under\nLimited Fault Data\nPeng <PERSON>,Member, IEEE , <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>\u0003,Member, IEEE ,\nand <PERSON><PERSON>\u0003,Fellow, IEEE\nAbstract —Intelligent fault diagnosis has made extraordinary\nadvancements currently. Nonetheless, few works tackle class-\nincremental learning for fault diagnosis under limited fault data,\ni. e.", "found_variables": ["distillation", "work"], "variable_count": 2, "chunk_length": 496}, "score": 0.49942532181739807, "query": "correlation Stripper Level Stripper Underflow"}, {"chunk": {"chunk_id": "1701.06551v1_chunk_2", "paper_id": "1701.06551v1", "paper_title": "On the Parametric Study of Lubricating Oil Production using an\nArtificial Neural Network (ANN) Approach", "chunk_text": "A feed-forward Multi-Layer Perceptron Neural Network was\nsuccessfully applied to capture the relationship between inputs and output\nparameters.", "found_variables": ["feed", "work", "A Feed", "input"], "variable_count": 4, "chunk_length": 143}, "score": 0.49536362290382385, "query": "Stripper Level Stripper Underflow interaction"}, {"chunk": {"chunk_id": "2302.05929v1_chunk_3", "paper_id": "2302.05929v1", "paper_title": "SCLIFD:Supervised Contrastive Knowledge Distillation for Incremental\nFault Diagnosis under Limited Fault Data", "chunk_text": "Accordingly, we introduce a supervised\ncontrastive knowledge distillation for incremental fault diagnosis under\nlimited fault data (SCLIFD) framework to address these issues, which extends\nthe classical incremental classifier and representation learning (iCaRL)\nframework from three perspectives. Primarily, we adopt supervised contrastive\nknowledge distillation (KD) to enhance its representation learning capability\nunder limited fault data.", "found_variables": ["distillation", "work"], "variable_count": 2, "chunk_length": 443}, "score": 0.49197107553482056, "query": "correlation Stripper Level Stripper Underflow"}, {"chunk": {"chunk_id": "1706.02043v1_chunk_2", "paper_id": "1706.02043v1", "paper_title": "Reduced chemistry for butanol isomers at engine-relevant conditions", "chunk_text": "During the reduction\nprocess, issues were encountered with pressure-dependent reactions formulated\nusing the logarithmic pressure interpolation approach; these issues are\ndiscussed and recommendations made to avoid ambiguity in its future\nimplementation in mechanism development.", "found_variables": ["press", "pressure"], "variable_count": 2, "chunk_length": 279}, "score": 0.49069544672966003, "query": "correlation Stripper Level Stripper Underflow"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}