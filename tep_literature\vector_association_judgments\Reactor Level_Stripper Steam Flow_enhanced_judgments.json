{"variable_pair": ["Reactor Level", "Stripper Steam Flow"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Reactor Level → Stripper Steam Flow", "association_votes": {"A": 4, "⊥": 1, "?": 4, "IA": 6}, "direction_votes": {"Stripper Steam Flow → Reactor Level": 2, "Reactor Level → Stripper Steam Flow": 5, "Reactor Level - Stripper Steam Flow": 3}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "1807.03623v1_chunk_0", "chunk_text": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "retrieval_score": 0.5853169560432434, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "1807.03623v1_chunk_1", "chunk_text": "This paper reports on experimental measurements of the gas temperature in a\ndielectric barrier discharge reactor powered by a high voltage pulsed signal. It is shown that the thermal behavior of the reactor follows a first order\nmodel. However, an unexpected runaway phenomenon was observed at a frequency of\n300Hz. A sudden increase in the power source and consequently in reactor\ntemperature which reaches 170{\\deg}C is observed.", "retrieval_score": 0.550312876701355, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Steam Flow → Reactor Level"}, {"chunk_id": "pdf_927_chunk_1", "chunk_text": "This paper reports on experimental measurements of the gas temperature in a dielectric barrier \ndischarge reactor powered by a high voltage pulsed signal. It is shown that the thermal \nbehavior of the reactor follows a first order model. However, an unexpected runaway \nphenomenon was observed at a frequency of 300Hz. A sudden increase in the power source \nand consequently in reactor temperature which reach es 170°C is observed.", "retrieval_score": 0.5464174747467041, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2308.11247v2_chunk_0", "chunk_text": "Benchmarking Domain Adaptation for Chemical Processes on the Tennessee\nEastman Process", "retrieval_score": 0.539905309677124, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.5389729738235474, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_7578_chunk_0", "chunk_text": "Correlation between radiation damage and magnetic properties in reactor vessel", "retrieval_score": 0.5364813804626465, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Level → Stripper Steam Flow"}, {"chunk_id": "pdf_1917_chunk_3", "chunk_text": "Stadium\nAve, West Lafayette, 47907, IN, USA\nAbstract\nMachine learning algorithms are increasingly being applied to fault detection\nand diagnosis (FDD) in chemical processes. However, existing data-driven\nFDD platforms often lack interpretability for process operators and strug-\ngle to identify root causes of previously unseen faults. This paper presents\nFaultExplainer , an interactive tool designed to improve fault detection, diag-\nnosis, and explanation in the Tennessee Eastman Process (TEP).", "retrieval_score": 0.5360981822013855, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Steam Flow → Reactor Level"}, {"chunk_id": "pdf_7900_chunk_1", "chunk_text": "Keywords:  reactor pressure vessel, embrittlement, tran sition temperature shift, machine \nlearning, neural network", "retrieval_score": 0.5307116508483887, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Level - Stripper Steam Flow"}, {"chunk_id": "1703.08935v1_chunk_0", "chunk_text": "Security Constrained Multi-Stage Transmission Expansion Planning\nConsidering a Continuously Variable Series Reactor", "retrieval_score": 0.5279923677444458, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Level → Stripper Steam Flow"}, {"chunk_id": "2212.04140v2_chunk_3", "chunk_text": "The effectiveness of the switching strategy is\nalso demonstrated via numerical simulation on the Tennessee Eastman Process.", "retrieval_score": 0.5272244811058044, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Level → Stripper Steam Flow"}, {"chunk_id": "2210.14595v1_chunk_4", "chunk_text": "Finally, we demonstrate the\neffectiveness of the proposed switching strategy via numerical simulation on\nthe Tennessee Eastman Process.", "retrieval_score": 0.5253508687019348, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_8972_chunk_3", "chunk_text": ") is standard neutron source , the uncertaint ies connected with the source neutron spectr um can be even neglected. The \npaper refers on  validation efforts of neutron leakage from stainless steel block  ~50×50×50 cm in Resea rch Center Rez. Both  \nthe neutron leakage flux at a distance of 1 m from  the center of the cubical assembly using stilbene spectrometry and the \nactivation rates at different  positions of the assembly  were evaluated.", "retrieval_score": 0.5232068300247192, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Level → Stripper Steam Flow"}, {"chunk_id": "2309.02362v1_chunk_1", "chunk_text": "An essential aspect of extending safe operation of the active nuclear\nreactors is understanding and predicting the embrittlement that occurs in the\nsteels that make up the Reactor pressure vessel (RPV). In this work we\nintegrate state of the art machine learning methods using ensembles of neural\nnetworks with unprecedented data collection and integration to develop a new\nmodel for RPV steel embrittlement.", "retrieval_score": 0.5228890180587769, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Level - Stripper Steam Flow"}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5227006673812866, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Level - Stripper Steam Flow"}, {"chunk_id": "1709.02232v1_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5227005481719971, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Level → Stripper Steam Flow"}], "original_retrieval_results": 15}