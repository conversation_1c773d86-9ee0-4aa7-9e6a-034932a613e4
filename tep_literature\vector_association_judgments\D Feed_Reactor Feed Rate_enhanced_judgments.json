{"variable_pair": ["<PERSON> Feed", "Reactor Feed Rate"], "aggregation": {"has_causal_relationship": true, "final_association": "A", "final_direction": "D Feed → Reactor Feed Rate", "association_votes": {"A": 5, "⊥": 2, "IA": 5, "?": 3}, "direction_votes": {"D Feed → Reactor Feed Rate": 9, "D Feed - Reactor Feed Rate": 1}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "2103.05025v1_chunk_0", "chunk_text": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate", "retrieval_score": 0.5885013341903687, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed → Reactor Feed Rate"}, {"chunk_id": "pdf_2177_chunk_2", "chunk_text": "Eksioglub, <PERSON>ushi <PERSON>c,\naEnergy and Environment Science & Technology Department, Idaho National Laboratory, Idaho Falls, ID\nbIndustrial Engineering Department, University of Arkansas, Fayetteville, AR\ncGlenn Department of Civil Engineering, Clemson University, Clemson, SC\nAbstract\nThe variations in feedstock characteristics such as moisture and particle size distribution\nlead to an inconsistent ﬂow of feedstock from the biomass pre-processing system to the reactor\nin-feed system.", "retrieval_score": 0.5798105001449585, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "D Feed → Reactor Feed Rate"}, {"chunk_id": "1807.03623v1_chunk_0", "chunk_text": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "retrieval_score": 0.5676928758621216, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2107.08078v1_chunk_4", "chunk_text": "Our sensitivity\nanalysis indicates that updating the infeed rate of the system, the processing\nspeed of equipment, and bale sequencing based on the moisture level of biomass\nimproves the processing rate of the reactor and reduces operating costs.", "retrieval_score": 0.557822048664093, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed → Reactor Feed Rate"}, {"chunk_id": "1807.03623v1_chunk_1", "chunk_text": "This paper reports on experimental measurements of the gas temperature in a\ndielectric barrier discharge reactor powered by a high voltage pulsed signal. It is shown that the thermal behavior of the reactor follows a first order\nmodel. However, an unexpected runaway phenomenon was observed at a frequency of\n300Hz. A sudden increase in the power source and consequently in reactor\ntemperature which reaches 170{\\deg}C is observed.", "retrieval_score": 0.5569754242897034, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed → Reactor Feed Rate"}, {"chunk_id": "pdf_927_chunk_1", "chunk_text": "This paper reports on experimental measurements of the gas temperature in a dielectric barrier \ndischarge reactor powered by a high voltage pulsed signal. It is shown that the thermal \nbehavior of the reactor follows a first order model. However, an unexpected runaway \nphenomenon was observed at a frequency of 300Hz. A sudden increase in the power source \nand consequently in reactor temperature which reach es 170°C is observed.", "retrieval_score": 0.5521224737167358, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_2177_chunk_1", "chunk_text": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate\n<PERSON><PERSON><PERSON>, <PERSON>,1, <PERSON>.", "retrieval_score": 0.551422655582428, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "D Feed - Reactor Feed Rate"}, {"chunk_id": "pdf_819_chunk_0", "chunk_text": "Geometric Decomposition of Feed Forward", "retrieval_score": 0.5393604636192322, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_7578_chunk_0", "chunk_text": "Correlation between radiation damage and magnetic properties in reactor vessel", "retrieval_score": 0.5357447862625122, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_1999_chunk_4", "chunk_text": "These policies take the sensory information data and the current biomass inventory\nlevel as inputs to dynamically adjust inventory levels and equipment settings according to the changes\nin the biomass' characteristics. We ensure that a prescribed target reactor utilization is consistently\nachieved by penalizing the violation of the target reactor feeding rate. A case study is developed\nusing real-world data collected at Idaho National Laboratory's biomass processing facility.", "retrieval_score": 0.5306097269058228, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed → Reactor Feed Rate"}, {"chunk_id": "pdf_6125_chunk_5", "chunk_text": "The LLRF uses four approximate programme d curves to control the acceleration \nprocess: FREQ, Bias, ROF, and RAG. With  the input of the phase error from phase FERMILAB-TM-2271-AD \n 2feedback of LLRF, VXI LLRF generates the LO -Freq using the FREQ program in such a \nway that the LO-Freq has the same frequenc y and a constant phase difference with the \nCB through a cycle. The radial feedba.", "retrieval_score": 0.5264447331428528, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "D Feed → Reactor Feed Rate"}, {"chunk_id": "2107.08078v1_chunk_3", "chunk_text": "We\nensure that a prescribed target reactor utilization is consistently achieved by\npenalizing the violation of the target reactor feeding rate. A case study is\ndeveloped using real-world data collected at Idaho National Laboratory's\nbiomass processing facility. We show the value of multi-stage stochastic\nprogramming from an extensive computational experiment.", "retrieval_score": 0.5218726396560669, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "D Feed → Reactor Feed Rate"}, {"chunk_id": "2103.05025v1_chunk_1", "chunk_text": "The variations in feedstock characteristics such as moisture and particle\nsize distribution lead to an inconsistent flow of feedstock from the biomass\npre-processing system to the reactor in-feed system. These inconsistencies\nresult in low on-stream times at the reactor in-feed equipment. This research\ndevelops an optimal process control method for a biomass pre-processing system\ncomprised of milling and densification operations to provide the consistent\nflow of feedstock to a reactor's throat.", "retrieval_score": 0.5214298367500305, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_2177_chunk_3", "chunk_text": "These inconsistencies result in low on-stream times at the reactor in-feed\nequipment. This research develops an optimal process control method for a biomass pre-\nprocessing system comprised of milling and densiﬁcation operations to provide the consistent\nﬂow of feedstock to a reactor’s throat. This method uses a mixed-integer optimization\nmodel to identify optimal bale sequencing, equipment in-feed rate, and buﬀer location and\nsize in the biomass pre-processing system.", "retrieval_score": 0.5201934576034546, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed → Reactor Feed Rate"}, {"chunk_id": "pdf_6125_chunk_3", "chunk_text": "Introduction \nThe proton beam is accelerated from 400 MeV to  8 GeV in Booster in a time of 33. 3 ms, \nwhile the RF frequency sweeps from 37. 8 MHz to 52. 9 MHz. The low-level RF system (LLRF) together with the bias running clos ed loop maintains the correct phase relation \nbetween the circulating beam bunches (CB) a nd the accelerating gap vo ltage. [1]  The RF \nphase angle is continuously adjusted to mainta in the required rate of energy gain.", "retrieval_score": 0.5195925831794739, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "D Feed → Reactor Feed Rate"}], "original_retrieval_results": 15}