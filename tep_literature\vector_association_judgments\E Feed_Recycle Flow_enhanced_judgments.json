{"variable_pair": ["<PERSON>ed", "Recycle Flow"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "E Feed → Recycle Flow", "association_votes": {"A": 2, "?": 6, "IA": 7}, "direction_votes": {"E Feed → Recycle Flow": 8, "E Feed - Recycle Flow": 1}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "pdf_2859_chunk_0", "chunk_text": "Feed-forward neural network unfolding", "retrieval_score": 0.6046237945556641, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "E Feed - Recycle Flow"}, {"chunk_id": "2112.08180v1_chunk_0", "chunk_text": "Feed-forward neural network unfolding", "retrieval_score": 0.6046237945556641, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "E Feed → Recycle Flow"}, {"chunk_id": "pdf_819_chunk_0", "chunk_text": "Geometric Decomposition of Feed Forward", "retrieval_score": 0.5990390777587891, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.59716796875, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2004.08702v1_chunk_0", "chunk_text": "Transmission Expansion Planning Using Cycle Flows", "retrieval_score": 0.5942027568817139, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed → Recycle Flow"}, {"chunk_id": "pdf_750_chunk_0", "chunk_text": "Transmission Expansion Planning Using Cycle Flows", "retrieval_score": 0.5942026376724243, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2403.10842v4_chunk_0", "chunk_text": "Twin Transformer using Gated Dynamic Learnable Attention mechanism for\nFault Detection and Diagnosis in the Tennessee Eastman Process", "retrieval_score": 0.5906225442886353, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed → Recycle Flow"}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5789217948913574, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "1709.02232v1_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5789217948913574, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed → Recycle Flow"}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5778170824050903, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed → Recycle Flow"}, {"chunk_id": "pdf_9441_chunk_0", "chunk_text": "On the space of coefﬁcients of a Feed Forward", "retrieval_score": 0.5729610919952393, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2203.11321v1_chunk_4", "chunk_text": "Finally, as a case study, the proposed\nmodel is implemented in the well-known Tennessee Eastman process, and the\nresults are presented.", "retrieval_score": 0.5702389478683472, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed → Recycle Flow"}, {"chunk_id": "1607.07423v3_chunk_5", "chunk_text": "We illustrate the\nsuccessful use of $K_T$ chart using the Tennessee Eastman process data.", "retrieval_score": 0.5698018074035645, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed → Recycle Flow"}, {"chunk_id": "1712.01580v1_chunk_0", "chunk_text": "The Lifting Bifurcation Problem on Feed-Forward Networks", "retrieval_score": 0.5674934387207031, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed → Recycle Flow"}, {"chunk_id": "pdf_6373_chunk_0", "chunk_text": "Linear Time Algorithm for Optimal Feed-link Placement", "retrieval_score": 0.5661476254463196, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}], "original_retrieval_results": 15}