{"variable_pair": ["<PERSON> Feed", "Stripper Level"], "aggregation": {"has_causal_relationship": true, "final_association": "?", "final_direction": "D Feed → Stripper Level", "association_votes": {"A": 2, "?": 6, "IA": 5, "⊥": 2}, "direction_votes": {"Stripper Level → D Feed": 1, "D Feed - Stripper Level": 1, "D Feed → Stripper Level": 5}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5538177490234375, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_2859_chunk_0", "chunk_text": "Feed-forward neural network unfolding", "retrieval_score": 0.5530064702033997, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2112.08180v1_chunk_0", "chunk_text": "Feed-forward neural network unfolding", "retrieval_score": 0.5530064702033997, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "D Feed → Stripper Level"}, {"chunk_id": "pdf_1428_chunk_0", "chunk_text": "Development of a Feeding Assistive Robot Using a Six Degree of", "retrieval_score": 0.5519474744796753, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed → Stripper Level"}, {"chunk_id": "2403.10842v4_chunk_0", "chunk_text": "Twin Transformer using Gated Dynamic Learnable Attention mechanism for\nFault Detection and Diagnosis in the Tennessee Eastman Process", "retrieval_score": 0.5312572121620178, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_819_chunk_0", "chunk_text": "Geometric Decomposition of Feed Forward", "retrieval_score": 0.5287628173828125, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2309.11594v1_chunk_0", "chunk_text": "Development of a Feeding Assistive Robot Using a Six Degree of Freedom\nRobotic Arm", "retrieval_score": 0.5223513841629028, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed → Stripper Level"}, {"chunk_id": "2309.00157v1_chunk_3", "chunk_text": "We validate the approach\nusing the Benchmark Tennessee Eastman while doing an ablation study of the\nmodel update parameters.", "retrieval_score": 0.5183202624320984, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "1712.01580v1_chunk_0", "chunk_text": "The Lifting Bifurcation Problem on Feed-Forward Networks", "retrieval_score": 0.5133969783782959, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_131_chunk_0", "chunk_text": "arXiv:2303.05904v1  [cs.LG]  10 Mar 2023Deep Anomaly Detection on Tennessee Eastman Process Data", "retrieval_score": 0.5114051103591919, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed → Stripper Level"}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.508439302444458, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "1709.02232v1_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5084391832351685, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Level → D Feed"}, {"chunk_id": "pdf_3937_chunk_3", "chunk_text": "We demon-\nstrate our model’s performance on two publicly available datasets of the Tennessee\nEastman Process with various faults. The results show that our method significantly\noutperforms existing approaches (+0. 2-0. 3 TPR for a fixed FPR) and effectively de-\ntects most of the process faults without expert annotation. Moreover, we show that the\nmodel fine-tuned on a small fraction of labeled data nearly reaches the performance of\na SOTA model trained on the full dataset.", "retrieval_score": 0.507194995880127, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed → Stripper Level"}, {"chunk_id": "2403.11722v2_chunk_3", "chunk_text": "We\napply our proposed compression method on the Tennessee Eastman Dataset, where\nwe perform fault classification using the compressed data in two settings: a\nfully supervised one and in a semi supervised, contrastive learning setting. Both times, we were able to outperform real valued counterparts as well as two\nbaseline models: one with the uncompressed time-series as the input and the\nother with a regular downsampling using the mean.", "retrieval_score": 0.5027645826339722, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed - Stripper Level"}, {"chunk_id": "pdf_2572_chunk_3", "chunk_text": "We apply our proposed compression\nmethod on the Tennessee Eastman Dataset, where we perform\nfault classification using the compressed data in two settings:\na fully supervised one and in a semi supervised, contrastive\nlearning setting. Both times, we were able to outperform real\nvalued counterparts as well as two baseline models: one with\nthe uncompressed time-series as the input and the other with a\nregular downsampling using the mean.", "retrieval_score": 0.5027644634246826, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}], "original_retrieval_results": 15}