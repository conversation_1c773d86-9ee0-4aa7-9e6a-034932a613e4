{"variable_pair": ["Purge Rate", "Stripper Temperature"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Purge Rate → Stripper Temperature", "association_votes": {"A": 6, "⊥": 2, "IA": 7}, "direction_votes": {"Stripper Temperature → Purge Rate": 3, "Purge Rate - Stripper Temperature": 2, "Purge Rate → Stripper Temperature": 8}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "1003.0921v1_chunk_2", "chunk_text": "High density data\nof temperature sweeps from 2 to 350 K can be acquired in under 16 hours and\nhigh density data of isothermal field sweeps from 0 to 140 kOe can be obtained\nin under 2 hours. Calibrations for the system have been performed on a platinum\nwire and Bi$_{2}$Sr$_{2}$CaCu$_{2}$O$_{8+\\delta}$ high $T_{c}$ superconductors. The measured TEP of phosphor-bronze (voltage lead wire) turns to be very small,\nwhere the absolute TEP value of phosphor-bronze wire is much less than 0.", "retrieval_score": 0.5422951579093933, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Temperature → Purge Rate"}, {"chunk_id": "pdf_3937_chunk_3", "chunk_text": "We demon-\nstrate our model’s performance on two publicly available datasets of the Tennessee\nEastman Process with various faults. The results show that our method significantly\noutperforms existing approaches (+0. 2-0. 3 TPR for a fixed FPR) and effectively de-\ntects most of the process faults without expert annotation. Moreover, we show that the\nmodel fine-tuned on a small fraction of labeled data nearly reaches the performance of\na SOTA model trained on the full dataset.", "retrieval_score": 0.5374606251716614, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Purge Rate → Stripper Temperature"}, {"chunk_id": "pdf_3170_chunk_3", "chunk_text": "High density\ndata of temperature sweeps from 2 to 350 K can be acquired in unde r 16 hours and\nhigh density data of isothermal ﬁeld sweeps from 0 to 140 kOe can be obtained in\nunder 2 hours. Calibrations for the system have been performed o n a platinum wire\nand Bi 2Sr2CaCu2O8+δhighTcsuperconductors. The measured TEP of phosphor-\nbronze (voltage lead wire) turns to be very small, where the absolut e TEP value of\nphosphor-bronzewireismuch lessthan 0. 5 µV/Kbelow 80K.", "retrieval_score": 0.5368291139602661, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5367015600204468, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Stripper Temperature"}, {"chunk_id": "pdf_6349_chunk_5", "chunk_text": "The temperature dependence  of the etch \nrate reveals two distinct regimes  characterized by markedly different apparent activation energies. The \nextracted apparent activation energies suggest that at temperatures below ~800 °C the etch rate is likely \nlimited by desorption of etch products. The relative etch rate s of heteroepitaxial (2̅01) and homoepitaxial \n(010) β-Ga2O3 were observed to scale by the ratio of the surface en.", "retrieval_score": 0.5270007252693176, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Purge Rate → Stripper Temperature"}, {"chunk_id": "2212.04140v2_chunk_3", "chunk_text": "The effectiveness of the switching strategy is\nalso demonstrated via numerical simulation on the Tennessee Eastman Process.", "retrieval_score": 0.5266581177711487, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Temperature → Purge Rate"}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.522318959236145, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Temperature → Purge Rate"}, {"chunk_id": "1709.02232v1_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.522318959236145, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Stripper Temperature"}, {"chunk_id": "2210.14595v1_chunk_4", "chunk_text": "Finally, we demonstrate the\neffectiveness of the proposed switching strategy via numerical simulation on\nthe Tennessee Eastman Process.", "retrieval_score": 0.5211796760559082, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate - Stripper Temperature"}, {"chunk_id": "pdf_5519_chunk_2", "chunk_text": "For the highly conducting samples ( /G56RT /G9E41000 S/cm), the temperature dependence\nof the 4-probe resistivity shows positive temperature coefficient of resistivity (TCR)\nfrom T=1. 5K to 300K. For the less conducting samples, the 4-probe resistivity data\nshow the crossover of TCR with a broad minimum peak at T=T* /G9E200K. For\nsamples of /G56RT /G9620000 S/cm, the /G55(1. 5K)/ /G55(300K)<1, i. e. , the resistivity at 1. 5K is\nlower than the room temperature resistivity value.", "retrieval_score": 0.5205310583114624, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Purge Rate → Stripper Temperature"}, {"chunk_id": "pdf_6959_chunk_5", "chunk_text": "Simulation results demonstrate the efficacy of this approach, showcasing superior performance in \naccuracy, false alarm rate, and misclassifica tion rate compared to existing methods. This \nmethodology provides a robust and interpretable solution for fault detection and diagnosis in the \nTEP, highlighting its potential for industrial applications. Keywords. Fault Detection, Tennessee Eastman Process, Supervised Feature Selection, Attention \nMechanism, Bidirectional Long Short -Term Memory.", "retrieval_score": 0.5198513865470886, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Purge Rate - Stripper Temperature"}, {"chunk_id": "pdf_9936_chunk_3", "chunk_text": "The purpos e of this study is to find a series of alloys promptly forming \nmetal hydrides (MH) with suitable properties in  order to build a MH-based hydrogen compressor, \nworking in the same way between 20 and ~100 oC.", "retrieval_score": 0.5181002020835876, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2210.08538v1_chunk_0", "chunk_text": "Advantages of OKID-ERA Identification in Control Systems. An Application\nto the Tennessee Eastman Plant", "retrieval_score": 0.5179014205932617, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Stripper Temperature"}, {"chunk_id": "0510342v1_chunk_1", "chunk_text": "Thermopower measurements have been carried out on the Ni substituted samples\nof Na0.75CoO2 in the temperature range 4.2K to 300K. The room temperature TEP\nincreases by 20microV/K even with 1% Ni substitution and systematically\nincreases with increasing Ni content upto 5%. The increase in TEP is\naccompanied by a decrease in rho, thus increasing the ratio of S^(2)/rho on Ni\nsubstitution. At low T, the TEP shows an anomaly in the substituted samples,\nshowing a peak at T ~ 20K.", "retrieval_score": 0.5160454511642456, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Purge Rate → Stripper Temperature"}, {"chunk_id": "2207.09874v5_chunk_4", "chunk_text": "The proposed approach is\nevaluated using numerical simulations and the Tennessee Eastman Process\nsimulator. The results confirm that selecting the examples suggested by the\nproposed algorithm allows for a faster reduction in the prediction error.", "retrieval_score": 0.515466034412384, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Stripper Temperature"}], "original_retrieval_results": 15}