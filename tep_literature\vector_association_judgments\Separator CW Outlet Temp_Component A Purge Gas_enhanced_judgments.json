{"variable_pair": ["Separator CW Outlet Temp", "Component A Purge Gas"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Separator CW Outlet Temp → Component A Purge Gas", "association_votes": {"A": 3, "⊥": 1, "IA": 10, "?": 1}, "direction_votes": {"Separator CW Outlet Temp → Component A Purge Gas": 12, "Separator CW Outlet Temp - Component A Purge Gas": 1}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "1911.04386v2_chunk_4", "chunk_text": "The\noutstanding performance of this method is demonstrated and contrasted to\n(dynamic) principal component analysis, which are widely applied in the\nindustry, in the benchmark Tennessee Eastman process~(TEP) and a real chemical\nmanufacturing dataset.", "retrieval_score": 0.5405553579330444, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Separator CW Outlet Temp → Component A Purge Gas"}, {"chunk_id": "2212.01432v1_chunk_1", "chunk_text": "Tungsten (W) is a material of choice for the divertor material due to its\nhigh melting temperature, thermal conductivity, and sputtering threshold. However, W has a very high brittle-to-ductile transition temperature and at\nfusion reactor temperatures ($\\geq$1000K) may undergo recrystallization and\ngrain growth.", "retrieval_score": 0.5374619960784912, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Separator CW Outlet Temp → Component A Purge Gas"}, {"chunk_id": "1807.03623v1_chunk_0", "chunk_text": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "retrieval_score": 0.5337133407592773, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Separator CW Outlet Temp → Component A Purge Gas"}, {"chunk_id": "pdf_9406_chunk_5", "chunk_text": "The performance of the method is demonstrated\nand contrasted to (dynamic) principal component analysis, which is widely applied in the industry,\nin the benchmark Tennessee Eastman process (TEP) and a real chemical manufacturing dataset. Keywords: fault detection, fault identi\fcation, recurrent neural networks, variational dropout,\nBayesian inference, Tennessee Eastman process. 1.", "retrieval_score": 0.5299059152603149, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Separator CW Outlet Temp → Component A Purge Gas"}, {"chunk_id": "pdf_927_chunk_5", "chunk_text": "Moreover, the increased number of gas colli sion can cause a large fraction of the input \npower to be dissipated in gas heati.", "retrieval_score": 0.5274513959884644, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_1764_chunk_2", "chunk_text": "Thompson1\n1Center for Computing Research, Sandia National Laboratories, Albuquerque, New Mexico 87185, USA\n2Material, Physical, and Chemical Science Center, Sandia National Laboratories, Albuquerque, New Mexico 87185, USA\n3CEA, DES/IRESNE/DEC 13018 Saint Paul L ˜A¨s Durance, France\n(Dated: December 6, 2022)\nTungsten (W) is a material of choice for the divertor material due to its high melting temperature, thermal\nconductivity, and sputtering threshold.", "retrieval_score": 0.5272667407989502, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Separator CW Outlet Temp → Component A Purge Gas"}, {"chunk_id": "1807.03623v1_chunk_1", "chunk_text": "This paper reports on experimental measurements of the gas temperature in a\ndielectric barrier discharge reactor powered by a high voltage pulsed signal. It is shown that the thermal behavior of the reactor follows a first order\nmodel. However, an unexpected runaway phenomenon was observed at a frequency of\n300Hz. A sudden increase in the power source and consequently in reactor\ntemperature which reaches 170{\\deg}C is observed.", "retrieval_score": 0.5247918367385864, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Separator CW Outlet Temp → Component A Purge Gas"}, {"chunk_id": "pdf_927_chunk_1", "chunk_text": "This paper reports on experimental measurements of the gas temperature in a dielectric barrier \ndischarge reactor powered by a high voltage pulsed signal. It is shown that the thermal \nbehavior of the reactor follows a first order model. However, an unexpected runaway \nphenomenon was observed at a frequency of 300Hz. A sudden increase in the power source \nand consequently in reactor temperature which reach es 170°C is observed.", "retrieval_score": 0.521971583366394, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Separator CW Outlet Temp - Component A Purge Gas"}, {"chunk_id": "pdf_927_chunk_4", "chunk_text": "A \ngreat deal of experimental, theoretical and numeric al works has been conducted so far and \nhelped us to better understand these processes [6]. Whatever the concerned application, gas \ntemperature has a great influence on the electrical  parameters and on the nature and \nproduction of primary species formed by plasma  and must then play an important role. The increase of temperature leads to modify current  and voltage shapes as it has been shown \nin [7].", "retrieval_score": 0.5217595100402832, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Separator CW Outlet Temp → Component A Purge Gas"}, {"chunk_id": "pdf_7856_chunk_5", "chunk_text": "In the second stage, a reconstruction error-based threshold approach using the trained\nGAWNO is employed to detect and isolate faults based on the discrepancy values. We validate the\nproposed approach using the Tennessee Eastman Process (TEP) dataset and Avedore wastewater\ntreatment plant (WWTP) and N2Oemissions nam.", "retrieval_score": 0.5203123688697815, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Separator CW Outlet Temp → Component A Purge Gas"}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.5201528072357178, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Separator CW Outlet Temp → Component A Purge Gas"}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5178823471069336, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Separator CW Outlet Temp → Component A Purge Gas"}, {"chunk_id": "2301.05537v4_chunk_2", "chunk_text": "The\ncontroller features a circuit-breaking mechanism, which circumvents potential\nsafety breach and guarantees the convergence of the system parameter estimate,\nbut is shown to be triggered only finitely often and hence has negligible\neffect on the asymptotic performance of the controller. The proposed controller\nis also validated via simulation on Tennessee Eastman Process~(TEP), a commonly\nused industrial process example.", "retrieval_score": 0.5176887512207031, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Separator CW Outlet Temp → Component A Purge Gas"}, {"chunk_id": "pdf_3805_chunk_2", "chunk_text": "Abstract: Fault detection and diagnosis (FDD) is a crucial task for ensuring the safety and \nefficiency of industrial processes. We propose a novel FDD methodology for the Tennessee \nEastman Process (TEP), a widely used benchmark for chemical process control. The model \nemploys two separate Transformer branches, enabling independent processing of input data and \npotential extraction of diverse information.", "retrieval_score": 0.5173331499099731, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Separator CW Outlet Temp → Component A Purge Gas"}, {"chunk_id": "pdf_9787_chunk_6", "chunk_text": "Experimental validation on both the Tennessee Eastman \nProcess (TEP) and the Secure Water Treatment (SWaT) \ntestbeds demonstrates the framework’s superior performan ce. A comprehensive ablation study, supported by extensive visualizations including Receiver Operatin.", "retrieval_score": 0.5162564516067505, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}], "original_retrieval_results": 15}