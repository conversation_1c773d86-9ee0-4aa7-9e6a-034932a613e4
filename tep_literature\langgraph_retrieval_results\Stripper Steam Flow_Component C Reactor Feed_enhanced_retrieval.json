{"variable_pair": ["Stripper Steam Flow", "Component C Reactor Feed"], "results": [{"chunk": {"chunk_id": "pdf_8896_chunk_2", "paper_id": "pdf_8896", "paper_title": "Optimal Control to Handle Variations in Biomass Feedstock", "chunk_text": "Eksioglub, <PERSON>ushi <PERSON>c,\naEnergy and Environment Science & Technology Department, Idaho National Laboratory, Idaho Falls, ID\nbIndustrial Engineering Department, University of Arkansas, Fayetteville, AR\ncGlenn Department of Civil Engineering, Clemson University, Clemson, SC\nAbstract\nThe variations in feedstock characteristics such as moisture and particle size distribution\nlead to an inconsistent ﬂow of feedstock from the biomass pre-processing system to the reactor\nin-feed system.", "found_variables": ["feed", "reactor"], "variable_count": 2, "chunk_length": 485}, "score": 0.5505789518356323, "query": "Stripper Steam Flow and Component C Reactor Feed"}, {"chunk": {"chunk_id": "pdf_6723_chunk_3", "paper_id": "pdf_6723", "paper_title": "From partial data to out-of-sample", "chunk_text": "The\nframework is illustrated via a computational model of the deposition of Cu\nin a Chemical Vapor Deposition (CVD) reactor, where the reactor pressure,\nthe deposition temperature and feed mass \row rate are important process\nparameters that determine the outcome of the process. The sampled ob-\nservations are high-dimensional vectors containing the outputs of a detailed\nCFD steady-state model of the process, i. e.", "found_variables": ["temperature", "pressure", "rate", "Reactor Pressure", "<PERSON> Feed", "reactor", "work", "press", "feed", "temp"], "variable_count": 10, "chunk_length": 416}, "score": 0.5364028215408325, "query": "Stripper Steam Flow and Component C Reactor Feed"}, {"chunk": {"chunk_id": "pdf_3282_chunk_0", "paper_id": "pdf_3282", "paper_title": "Geometric Decomposition of Feed Forward", "chunk_text": "Geometric Decomposition of Feed Forward", "found_variables": ["feed", "composition"], "variable_count": 2, "chunk_length": 39}, "score": 0.5355705618858337, "query": "Stripper Steam Flow and Component C Reactor Feed"}, {"chunk": {"chunk_id": "1807.03623v1_chunk_0", "paper_id": "1807.03623v1", "paper_title": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "chunk_text": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "found_variables": ["temperature", "reactor", "temp"], "variable_count": 3, "chunk_length": 68}, "score": 0.5262655019760132, "query": "Stripper Steam Flow and Component C Reactor Feed"}, {"chunk": {"chunk_id": "0402435v1_chunk_0", "paper_id": "0402435v1", "paper_title": "The Indo-U.S. Library of Coude Feed Stellar Spectra", "chunk_text": "The Indo-U.S. Library of Coude Feed Stellar Spectra", "found_variables": ["feed", "<PERSON>ed"], "variable_count": 2, "chunk_length": 51}, "score": 0.5253584980964661, "query": "Stripper Steam Flow and Component C Reactor Feed"}, {"chunk": {"chunk_id": "1703.08935v1_chunk_0", "paper_id": "1703.08935v1", "paper_title": "Security Constrained Multi-Stage Transmission Expansion Planning\nConsidering a Continuously Variable Series Reactor", "chunk_text": "Security Constrained Multi-Stage Transmission Expansion Planning\nConsidering a Continuously Variable Series Reactor", "found_variables": ["reactor"], "variable_count": 1, "chunk_length": 115}, "score": 0.5173776745796204, "query": "Stripper Steam Flow and Component C Reactor Feed"}, {"chunk": {"chunk_id": "2108.13381v1_chunk_1", "paper_id": "2108.13381v1", "paper_title": "Trustworthy AI for Process Automation on a Chylla-Haase Polymerization\nReactor", "chunk_text": "In this paper, genetic programming reinforcement learning (GPRL) is utilized\nto generate human-interpretable control policies for a Chylla-Haase\npolymerization reactor. Such continuously stirred tank reactors (CSTRs) with\njacket cooling are widely used in the chemical industry, in the production of\nfine chemicals, pigments, polymers, and medical products. Despite appearing\nrather simple, controlling CSTRs in real-world applications is quite a\nchallenging problem to tackle.", "found_variables": ["reactor", "rate", "cooling", "CSTR"], "variable_count": 4, "chunk_length": 477}, "score": 0.5102576017379761, "query": "Stripper Steam Flow and Component C Reactor Feed"}, {"chunk": {"chunk_id": "pdf_2244_chunk_2", "paper_id": "pdf_2244", "paper_title": "©2021 the authors. This is a preprint version. The \fnal version of this", "chunk_text": "August 31, 2021\nAbstract\nIn this paper, genetic programming reinforcement learning (GPRL) is\nutilized to generate human-interpretable control policies for a Chylla-\nHaase polymerization reactor. Such continuously stirred tank reactors\n(CSTRs) with jacket cooling are widely used in the chemical industry,\nin the production of \fne chemicals, pigments, polymers, and medi-\ncal products.", "found_variables": ["reactor", "rate", "cooling", "CSTR"], "variable_count": 4, "chunk_length": 384}, "score": 0.5066676735877991, "query": "Stripper Steam Flow and Component C Reactor Feed"}, {"chunk": {"chunk_id": "2301.11728v1_chunk_3", "paper_id": "2301.11728v1", "paper_title": "From partial data to out-of-sample parameter and observation estimation\nwith Diffusion Maps and Geometric Harmonics", "chunk_text": "inlet\ntemperature); (b) process parameters given observation data; and (c) partial\nobservations (e. g. temperature in the reactor) given other partial observations\n(e. g. mass fraction in the reactor). The proposed workflow relies on the\nmanifold learning schemes Diffusion Maps and the associated Geometric\nHarmonics. Diffusion Maps is used for discovering a reduced representation of\nthe available data, and Geometric Harmonics for extending functions defined on\nthe manifold.", "found_variables": ["temperature", "inlet", "flow", "reactor", "work", "temp"], "variable_count": 6, "chunk_length": 478}, "score": 0.5027539730072021, "query": "Stripper Steam Flow and Component C Reactor Feed"}, {"chunk": {"chunk_id": "pdf_664_chunk_0", "paper_id": "pdf_664", "paper_title": "Correlation between radiation damage and magnetic properties in reactor vessel", "chunk_text": "Correlation between radiation damage and magnetic properties in reactor vessel", "found_variables": ["reactor"], "variable_count": 1, "chunk_length": 78}, "score": 0.5017220973968506, "query": "relationship between Stripper Steam Flow and Component C Reactor Feed"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}