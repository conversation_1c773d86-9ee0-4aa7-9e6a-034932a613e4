{"variable_pair": ["Stripper Steam Flow", "Component A Purge Gas"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Stripper Steam Flow → Component A Purge Gas", "association_votes": {"A": 3, "⊥": 2, "?": 4, "IA": 6}, "direction_votes": {"Stripper Steam Flow → Component A Purge Gas": 6, "Stripper Steam Flow - Component A Purge Gas": 3}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "pdf_9406_chunk_5", "chunk_text": "The performance of the method is demonstrated\nand contrasted to (dynamic) principal component analysis, which is widely applied in the industry,\nin the benchmark Tennessee Eastman process (TEP) and a real chemical manufacturing dataset. Keywords: fault detection, fault identi\fcation, recurrent neural networks, variational dropout,\nBayesian inference, Tennessee Eastman process. 1.", "retrieval_score": 0.5384264588356018, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Steam Flow → Component A Purge Gas"}, {"chunk_id": "1911.04386v2_chunk_4", "chunk_text": "The\noutstanding performance of this method is demonstrated and contrasted to\n(dynamic) principal component analysis, which are widely applied in the\nindustry, in the benchmark Tennessee Eastman process~(TEP) and a real chemical\nmanufacturing dataset.", "retrieval_score": 0.528987467288971, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_9735_chunk_4", "chunk_text": "We evaluate our approach using the\nTennessee-Eastman process. Results show that our approach\ncan be used to distinguish disturbances from intrusions to a\ncertain extent and we conclude that the proposed approach can\nbe extended with other sources of data for improving results. Keywords -Process control systems, Multivariate Statistical\nProcess Control, Tennessee-Eastman,\nI.", "retrieval_score": 0.5284699201583862, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.52690589427948, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Steam Flow → Component A Purge Gas"}, {"chunk_id": "pdf_1917_chunk_3", "chunk_text": "Stadium\nAve, West Lafayette, 47907, IN, USA\nAbstract\nMachine learning algorithms are increasingly being applied to fault detection\nand diagnosis (FDD) in chemical processes. However, existing data-driven\nFDD platforms often lack interpretability for process operators and strug-\ngle to identify root causes of previously unseen faults. This paper presents\nFaultExplainer , an interactive tool designed to improve fault detection, diag-\nnosis, and explanation in the Tennessee Eastman Process (TEP).", "retrieval_score": 0.5253742933273315, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Steam Flow → Component A Purge Gas"}, {"chunk_id": "pdf_3805_chunk_2", "chunk_text": "Abstract: Fault detection and diagnosis (FDD) is a crucial task for ensuring the safety and \nefficiency of industrial processes. We propose a novel FDD methodology for the Tennessee \nEastman Process (TEP), a widely used benchmark for chemical process control. The model \nemploys two separate Transformer branches, enabling independent processing of input data and \npotential extraction of diverse information.", "retrieval_score": 0.5250550508499146, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2310.06013v3_chunk_0", "chunk_text": "The Not-So Dramatic Effect of Advective Flows on Gas Accretion", "retrieval_score": 0.5242356061935425, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Steam Flow - Component A Purge Gas"}, {"chunk_id": "2212.04140v2_chunk_3", "chunk_text": "The effectiveness of the switching strategy is\nalso demonstrated via numerical simulation on the Tennessee Eastman Process.", "retrieval_score": 0.5230110287666321, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Steam Flow → Component A Purge Gas"}, {"chunk_id": "2403.10842v4_chunk_1", "chunk_text": "Fault detection and diagnosis (FDD) is a crucial task for ensuring the safety\nand efficiency of industrial processes. We propose a novel FDD methodology for\nthe Tennessee Eastman Process (TEP), a widely used benchmark for chemical\nprocess control. The model employs two separate Transformer branches, enabling\nindependent processing of input data and potential extraction of diverse\ninformation.", "retrieval_score": 0.5183104276657104, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Steam Flow → Component A Purge Gas"}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5163397192955017, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Steam Flow - Component A Purge Gas"}, {"chunk_id": "2203.11321v1_chunk_4", "chunk_text": "Finally, as a case study, the proposed\nmodel is implemented in the well-known Tennessee Eastman process, and the\nresults are presented.", "retrieval_score": 0.5147132873535156, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2210.14595v1_chunk_4", "chunk_text": "Finally, we demonstrate the\neffectiveness of the proposed switching strategy via numerical simulation on\nthe Tennessee Eastman Process.", "retrieval_score": 0.5132045149803162, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Steam Flow → Component A Purge Gas"}, {"chunk_id": "pdf_2970_chunk_4", "chunk_text": "The theoretical analysis is verified through Monte\nCarlo simulations and Tennessee Eastman process (TEP) dataset. Keywords: Fault detection, Kalman filter, Residual design, Tennessee Eastman process\n1. Introduction\nFault detection, also known as change detection, is a very\nimportant and well researched topic in many engineering fields\ne. g. process industry, mechanical systems and biomedicine [1]. Its goal is to detect the abnormal events in system and related\nsignals.", "retrieval_score": 0.5101985931396484, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Steam Flow - Component A Purge Gas"}, {"chunk_id": "2308.11247v2_chunk_0", "chunk_text": "Benchmarking Domain Adaptation for Chemical Processes on the Tennessee\nEastman Process", "retrieval_score": 0.5079440474510193, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2403.13502v4_chunk_0", "chunk_text": "Adversarial Attacks and Defenses in Fault Detection and Diagnosis: A\nComprehensive Benchmark on the Tennessee Eastman Process", "retrieval_score": 0.5051419734954834, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}], "original_retrieval_results": 15}