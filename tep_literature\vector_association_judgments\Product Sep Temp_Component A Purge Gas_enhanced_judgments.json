{"variable_pair": ["Product Sep Temp", "Component A Purge Gas"], "aggregation": {"has_causal_relationship": true, "final_association": "?", "final_direction": "Product Sep Temp → Component A Purge Gas", "association_votes": {"A": 3, "?": 6, "IA": 6}, "direction_votes": {"Product Sep Temp → Component A Purge Gas": 7, "Product Sep Temp - Component A Purge Gas": 2}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "1911.04386v2_chunk_4", "chunk_text": "The\noutstanding performance of this method is demonstrated and contrasted to\n(dynamic) principal component analysis, which are widely applied in the\nindustry, in the benchmark Tennessee Eastman process~(TEP) and a real chemical\nmanufacturing dataset.", "retrieval_score": 0.5845456123352051, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5600728392601013, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_927_chunk_4", "chunk_text": "A \ngreat deal of experimental, theoretical and numeric al works has been conducted so far and \nhelped us to better understand these processes [6]. Whatever the concerned application, gas \ntemperature has a great influence on the electrical  parameters and on the nature and \nproduction of primary species formed by plasma  and must then play an important role. The increase of temperature leads to modify current  and voltage shapes as it has been shown \nin [7].", "retrieval_score": 0.5571008920669556, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp → Component A Purge Gas"}, {"chunk_id": "pdf_9406_chunk_5", "chunk_text": "The performance of the method is demonstrated\nand contrasted to (dynamic) principal component analysis, which is widely applied in the industry,\nin the benchmark Tennessee Eastman process (TEP) and a real chemical manufacturing dataset. Keywords: fault detection, fault identi\fcation, recurrent neural networks, variational dropout,\nBayesian inference, Tennessee Eastman process. 1.", "retrieval_score": 0.5554970502853394, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Product Sep Temp → Component A Purge Gas"}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.5478729009628296, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_3805_chunk_2", "chunk_text": "Abstract: Fault detection and diagnosis (FDD) is a crucial task for ensuring the safety and \nefficiency of industrial processes. We propose a novel FDD methodology for the Tennessee \nEastman Process (TEP), a widely used benchmark for chemical process control. The model \nemploys two separate Transformer branches, enabling independent processing of input data and \npotential extraction of diverse information.", "retrieval_score": 0.5430873036384583, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2308.11247v2_chunk_0", "chunk_text": "Benchmarking Domain Adaptation for Chemical Processes on the Tennessee\nEastman Process", "retrieval_score": 0.5429626107215881, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Product Sep Temp → Component A Purge Gas"}, {"chunk_id": "2203.11321v1_chunk_4", "chunk_text": "Finally, as a case study, the proposed\nmodel is implemented in the well-known Tennessee Eastman process, and the\nresults are presented.", "retrieval_score": 0.5419418811798096, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp → Component A Purge Gas"}, {"chunk_id": "1607.07423v3_chunk_5", "chunk_text": "We illustrate the\nsuccessful use of $K_T$ chart using the Tennessee Eastman process data.", "retrieval_score": 0.5396679043769836, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp → Component A Purge Gas"}, {"chunk_id": "2403.10842v4_chunk_1", "chunk_text": "Fault detection and diagnosis (FDD) is a crucial task for ensuring the safety\nand efficiency of industrial processes. We propose a novel FDD methodology for\nthe Tennessee Eastman Process (TEP), a widely used benchmark for chemical\nprocess control. The model employs two separate Transformer branches, enabling\nindependent processing of input data and potential extraction of diverse\ninformation.", "retrieval_score": 0.5390288233757019, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp - Component A Purge Gas"}, {"chunk_id": "2309.00157v1_chunk_3", "chunk_text": "We validate the approach\nusing the Benchmark Tennessee Eastman while doing an ablation study of the\nmodel update parameters.", "retrieval_score": 0.5381008386611938, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp → Component A Purge Gas"}, {"chunk_id": "2301.11573v3_chunk_3", "chunk_text": "The theoretical analysis is verified through\nMonte Carlo simulations and Tennessee Eastman process (TEP) dataset.", "retrieval_score": 0.5361868143081665, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_131_chunk_0", "chunk_text": "arXiv:2303.05904v1  [cs.LG]  10 Mar 2023Deep Anomaly Detection on Tennessee Eastman Process Data", "retrieval_score": 0.5355840921401978, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2212.12092v1_chunk_5", "chunk_text": "Finally, we use the benchmark Tennessee Eastman to\nperform experiments to test the ensemble classifier's prediction and anomaly\ndetection capabilities.", "retrieval_score": 0.5351711511611938, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp → Component A Purge Gas"}, {"chunk_id": "pdf_5519_chunk_5", "chunk_text": "From the quasi 1-dimensional metallic nature of PA, the room temperature\nconductivity ( /G56RT) was estimated to be greater than that of copper [5]. But even for\nthe most highly conducting samples, the temperature dependence of resistivity is\nSynthetic Metals 9.", "retrieval_score": 0.5299938917160034, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Product Sep Temp - Component A Purge Gas"}], "original_retrieval_results": 15}