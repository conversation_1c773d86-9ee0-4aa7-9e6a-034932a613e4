{"variable_pair": ["Reactor Temperature", "Prod Sep Underflow"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Reactor Temperature → Prod Sep Underflow", "association_votes": {"A": 2, "?": 3, "IA": 5, "⊥": 5}, "direction_votes": {"Reactor Temperature - Prod Sep Underflow": 1, "Reactor Temperature → Prod Sep Underflow": 6}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "1807.03623v1_chunk_0", "chunk_text": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "retrieval_score": 0.623507022857666, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Temperature → Prod Sep Underflow"}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5993459224700928, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Temperature → Prod Sep Underflow"}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.594231367111206, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_4254_chunk_5", "chunk_text": "Keywords: Tennessee Eastman Process, Chemical Processes, Graph neural\nnetworks, Fault diagnosis, Sensor dataarXiv:2210. 11164v1  [cs. AI]  20 Oct 2022\n1. Introduction\nDuring the production, equipment often stops due to the various faults. The process of \fndin.", "retrieval_score": 0.5800349116325378, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_131_chunk_0", "chunk_text": "arXiv:2303.05904v1  [cs.LG]  10 Mar 2023Deep Anomaly Detection on Tennessee Eastman Process Data", "retrieval_score": 0.5790427923202515, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2308.11247v2_chunk_0", "chunk_text": "Benchmarking Domain Adaptation for Chemical Processes on the Tennessee\nEastman Process", "retrieval_score": 0.575129508972168, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Temperature → Prod Sep Underflow"}, {"chunk_id": "pdf_1917_chunk_3", "chunk_text": "Stadium\nAve, West Lafayette, 47907, IN, USA\nAbstract\nMachine learning algorithms are increasingly being applied to fault detection\nand diagnosis (FDD) in chemical processes. However, existing data-driven\nFDD platforms often lack interpretability for process operators and strug-\ngle to identify root causes of previously unseen faults. This paper presents\nFaultExplainer , an interactive tool designed to improve fault detection, diag-\nnosis, and explanation in the Tennessee Eastman Process (TEP).", "retrieval_score": 0.5741007328033447, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Temperature → Prod Sep Underflow"}, {"chunk_id": "1807.03623v1_chunk_1", "chunk_text": "This paper reports on experimental measurements of the gas temperature in a\ndielectric barrier discharge reactor powered by a high voltage pulsed signal. It is shown that the thermal behavior of the reactor follows a first order\nmodel. However, an unexpected runaway phenomenon was observed at a frequency of\n300Hz. A sudden increase in the power source and consequently in reactor\ntemperature which reaches 170{\\deg}C is observed.", "retrieval_score": 0.5736549496650696, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Temperature → Prod Sep Underflow"}, {"chunk_id": "pdf_927_chunk_1", "chunk_text": "This paper reports on experimental measurements of the gas temperature in a dielectric barrier \ndischarge reactor powered by a high voltage pulsed signal. It is shown that the thermal \nbehavior of the reactor follows a first order model. However, an unexpected runaway \nphenomenon was observed at a frequency of 300Hz. A sudden increase in the power source \nand consequently in reactor temperature which reach es 170°C is observed.", "retrieval_score": 0.5732284784317017, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Temperature - Prod Sep Underflow"}, {"chunk_id": "2301.11573v3_chunk_3", "chunk_text": "The theoretical analysis is verified through\nMonte Carlo simulations and Tennessee Eastman process (TEP) dataset.", "retrieval_score": 0.5731416940689087, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2301.05537v4_chunk_2", "chunk_text": "The\ncontroller features a circuit-breaking mechanism, which circumvents potential\nsafety breach and guarantees the convergence of the system parameter estimate,\nbut is shown to be triggered only finitely often and hence has negligible\neffect on the asymptotic performance of the controller. The proposed controller\nis also validated via simulation on Tennessee Eastman Process~(TEP), a commonly\nused industrial process example.", "retrieval_score": 0.5710110664367676, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_3937_chunk_5", "chunk_text": "The stability of the production process is usually maintained by a closed-loop control system that\ncan automatically perform small corrections to the operation control parameters to keep the process\nvariables within the desired production range (e. g. , reactor temperature, flow veloci.", "retrieval_score": 0.5705945491790771, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Temperature → Prod Sep Underflow"}, {"chunk_id": "2301.11728v1_chunk_3", "chunk_text": "inlet\ntemperature); (b) process parameters given observation data; and (c) partial\nobservations (e. g. temperature in the reactor) given other partial observations\n(e. g. mass fraction in the reactor). The proposed workflow relies on the\nmanifold learning schemes Diffusion Maps and the associated Geometric\nHarmonics. Diffusion Maps is used for discovering a reduced representation of\nthe available data, and Geometric Harmonics for extending functions defined on\nthe manifold.", "retrieval_score": 0.5695403218269348, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_2177_chunk_2", "chunk_text": "Eksioglub, <PERSON>ushi <PERSON>c,\naEnergy and Environment Science & Technology Department, Idaho National Laboratory, Idaho Falls, ID\nbIndustrial Engineering Department, University of Arkansas, Fayetteville, AR\ncGlenn Department of Civil Engineering, Clemson University, Clemson, SC\nAbstract\nThe variations in feedstock characteristics such as moisture and particle size distribution\nlead to an inconsistent ﬂow of feedstock from the biomass pre-processing system to the reactor\nin-feed system.", "retrieval_score": 0.56795334815979, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_6959_chunk_5", "chunk_text": "Simulation results demonstrate the efficacy of this approach, showcasing superior performance in \naccuracy, false alarm rate, and misclassifica tion rate compared to existing methods. This \nmethodology provides a robust and interpretable solution for fault detection and diagnosis in the \nTEP, highlighting its potential for industrial applications. Keywords. Fault Detection, Tennessee Eastman Process, Supervised Feature Selection, Attention \nMechanism, Bidirectional Long Short -Term Memory.", "retrieval_score": 0.5667579174041748, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}], "original_retrieval_results": 15}