{"variable_pair": ["Product Sep Temp", "Component E Purge Gas"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Product Sep Temp → Component E Purge Gas", "association_votes": {"A": 4, "?": 4, "IA": 6, "⊥": 1}, "direction_votes": {"Product Sep Temp → Component E Purge Gas": 7, "Product Sep Temp - Component E Purge Gas": 3}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "1911.04386v2_chunk_4", "chunk_text": "The\noutstanding performance of this method is demonstrated and contrasted to\n(dynamic) principal component analysis, which are widely applied in the\nindustry, in the benchmark Tennessee Eastman process~(TEP) and a real chemical\nmanufacturing dataset.", "retrieval_score": 0.5705656409263611, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Product Sep Temp - Component E Purge Gas"}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5670390129089355, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.5557541847229004, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_9406_chunk_5", "chunk_text": "The performance of the method is demonstrated\nand contrasted to (dynamic) principal component analysis, which is widely applied in the industry,\nin the benchmark Tennessee Eastman process (TEP) and a real chemical manufacturing dataset. Keywords: fault detection, fault identi\fcation, recurrent neural networks, variational dropout,\nBayesian inference, Tennessee Eastman process. 1.", "retrieval_score": 0.5520144701004028, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp - Component E Purge Gas"}, {"chunk_id": "pdf_6959_chunk_5", "chunk_text": "Simulation results demonstrate the efficacy of this approach, showcasing superior performance in \naccuracy, false alarm rate, and misclassifica tion rate compared to existing methods. This \nmethodology provides a robust and interpretable solution for fault detection and diagnosis in the \nTEP, highlighting its potential for industrial applications. Keywords. Fault Detection, Tennessee Eastman Process, Supervised Feature Selection, Attention \nMechanism, Bidirectional Long Short -Term Memory.", "retrieval_score": 0.5409737825393677, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Product Sep Temp - Component E Purge Gas"}, {"chunk_id": "2308.11247v2_chunk_0", "chunk_text": "Benchmarking Domain Adaptation for Chemical Processes on the Tennessee\nEastman Process", "retrieval_score": 0.540162205696106, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp → Component E Purge Gas"}, {"chunk_id": "pdf_6233_chunk_3", "chunk_text": "For Co-dopingwe clearly see a change in thete mperaturedependent TEP andHall\ncoeﬃcient data when the sample is doped to suﬃcient e(the number of extra electrons associated\nwith the TMdoping) so as to stabilize low temperature superconductivi ty. Remarkably, a similar\nchange is found in the Cu-doped samples at comparable e-value, even though these compounds do\nnot superconduct.", "retrieval_score": 0.5397005081176758, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp → Component E Purge Gas"}, {"chunk_id": "pdf_3805_chunk_2", "chunk_text": "Abstract: Fault detection and diagnosis (FDD) is a crucial task for ensuring the safety and \nefficiency of industrial processes. We propose a novel FDD methodology for the Tennessee \nEastman Process (TEP), a widely used benchmark for chemical process control. The model \nemploys two separate Transformer branches, enabling independent processing of input data and \npotential extraction of diverse information.", "retrieval_score": 0.5386782884597778, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp → Component E Purge Gas"}, {"chunk_id": "1607.07423v3_chunk_5", "chunk_text": "We illustrate the\nsuccessful use of $K_T$ chart using the Tennessee Eastman process data.", "retrieval_score": 0.5380557775497437, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp → Component E Purge Gas"}, {"chunk_id": "pdf_927_chunk_4", "chunk_text": "A \ngreat deal of experimental, theoretical and numeric al works has been conducted so far and \nhelped us to better understand these processes [6]. Whatever the concerned application, gas \ntemperature has a great influence on the electrical  parameters and on the nature and \nproduction of primary species formed by plasma  and must then play an important role. The increase of temperature leads to modify current  and voltage shapes as it has been shown \nin [7].", "retrieval_score": 0.5373411178588867, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp → Component E Purge Gas"}, {"chunk_id": "2403.10842v4_chunk_1", "chunk_text": "Fault detection and diagnosis (FDD) is a crucial task for ensuring the safety\nand efficiency of industrial processes. We propose a novel FDD methodology for\nthe Tennessee Eastman Process (TEP), a widely used benchmark for chemical\nprocess control. The model employs two separate Transformer branches, enabling\nindependent processing of input data and potential extraction of diverse\ninformation.", "retrieval_score": 0.5362522602081299, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Product Sep Temp → Component E Purge Gas"}, {"chunk_id": "2403.13502v4_chunk_0", "chunk_text": "Adversarial Attacks and Defenses in Fault Detection and Diagnosis: A\nComprehensive Benchmark on the Tennessee Eastman Process", "retrieval_score": 0.5319397449493408, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2301.11573v3_chunk_3", "chunk_text": "The theoretical analysis is verified through\nMonte Carlo simulations and Tennessee Eastman process (TEP) dataset.", "retrieval_score": 0.5290380120277405, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_8956_chunk_4", "chunk_text": "In particular, the\nthermoelectric power (TEP), expressed by the Seebeck\ncoeﬃcient S, isknowntoshowcharacteristictemperature\nand doping dependences above Tc, namely (i)dS/dT <\n0 in hole-doped compounds such as YBa 2Cu3O7−x\n(YBCO) or La 2−xSrxCuO4(LSCO), and Sis posi-\ntive in the under-doped systems at room temperatures\n[1,2].", "retrieval_score": 0.5288940072059631, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_1917_chunk_3", "chunk_text": "Stadium\nAve, West Lafayette, 47907, IN, USA\nAbstract\nMachine learning algorithms are increasingly being applied to fault detection\nand diagnosis (FDD) in chemical processes. However, existing data-driven\nFDD platforms often lack interpretability for process operators and strug-\ngle to identify root causes of previously unseen faults. This paper presents\nFaultExplainer , an interactive tool designed to improve fault detection, diag-\nnosis, and explanation in the Tennessee Eastman Process (TEP).", "retrieval_score": 0.5271295309066772, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Product Sep Temp → Component E Purge Gas"}], "original_retrieval_results": 15}