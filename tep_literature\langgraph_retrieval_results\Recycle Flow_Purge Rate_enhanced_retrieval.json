{"variable_pair": ["Recycle Flow", "Purge Rate"], "results": [{"chunk": {"chunk_id": "2107.08078v1_chunk_4", "paper_id": "2107.08078v1", "paper_title": "A Multi-stage Stochastic Programming Model for Adaptive Biomass\nProcessing Operation under Uncertainty", "chunk_text": "Our sensitivity\nanalysis indicates that updating the infeed rate of the system, the processing\nspeed of equipment, and bale sequencing based on the moisture level of biomass\nimproves the processing rate of the reactor and reduces operating costs.", "found_variables": ["feed", "reactor", "rate", "level"], "variable_count": 4, "chunk_length": 246}, "score": 0.5290805101394653, "query": "Recycle Flow influence Purge Rate"}, {"chunk": {"chunk_id": "pdf_764_chunk_4", "paper_id": "pdf_764", "paper_title": "MNRAS 000, 1{24 (2021) Preprint 3 September 2021 Compiled using MNRAS L ATEX style \fle v3.0", "chunk_text": "Using a passive scalar, we observe sig-\nni\fcant mass recycling on the orbital timescale. For a radiative envelope, recycling\ncan only penetrate from the disc surface until \u00180. 1-0. 2 planetary Hill radii, while for\na convective envelope, the convective motion can \\dredge up\" the deeper part of the\nenvelope so that the entire convective envelope is recycled e\u000eciently. This recycling,\nhowever, has only limited e\u000bects on the envelopes' thermal structure.", "found_variables": ["rate", "thermal"], "variable_count": 2, "chunk_length": 455}, "score": 0.5274153351783752, "query": "Recycle Flow and Purge Rate"}, {"chunk": {"chunk_id": "2310.03117v1_chunk_3", "paper_id": "2310.03117v1", "paper_title": "Growing Planet Envelopes in Spite of Recycling Flows", "chunk_text": "We incorporate\nthese 3D hydrodynamic effects into an extensible 1D framework with a physically\nmotivated three-layer recycling parameterization. Specializing to the case of\nJupiter, recycling produces minimal changes to the growth rate with the planet\nstill entering runaway accretion and becoming a gas giant in ~1 Myr. Even in\nthe inner disk (0. 1 AU), our 1D models suggest that recycling is not so robust\nand ubiquitous as to stop all cores from becoming giants.", "found_variables": ["work", "rate"], "variable_count": 2, "chunk_length": 466}, "score": 0.5206902027130127, "query": "Recycle Flow and Purge Rate"}, {"chunk": {"chunk_id": "2406.09186v2_chunk_0", "paper_id": "2406.09186v2", "paper_title": "A formation pathway for terrestrial planets with moderate water content\ninvolving atmospheric-volatile recycling", "chunk_text": "A formation pathway for terrestrial planets with moderate water content\ninvolving atmospheric-volatile recycling", "found_variables": ["rate"], "variable_count": 1, "chunk_length": 112}, "score": 0.5157032012939453, "query": "Recycle Flow and Purge Rate"}, {"chunk": {"chunk_id": "pdf_8878_chunk_5", "paper_id": "pdf_8878", "paper_title": "Typeset using L ATEXtwocolumn style in AASTeX631", "chunk_text": "We incor-\nporate these 3D hydrodynamic effects into an extensible 1D framework with a physically motivated\nthree-layer recycling parameterization. Specializing to the case of Jupiter, recycling produces minimal\nchanges to the growth rate with the planet still entering runaway accretion and becoming a gas giant\nin∼1 Myr. Even in the inner disk (0. 1 AU), our 1D models suggest that recycling is not so robust\nand ubiquitous as to stop all cores from becoming giants.", "found_variables": ["work", "rate"], "variable_count": 2, "chunk_length": 467}, "score": 0.5135717391967773, "query": "Recycle Flow and Purge Rate"}, {"chunk": {"chunk_id": "2106.12003v2_chunk_2", "paper_id": "2106.12003v2", "paper_title": "Global 3D Radiation Hydrodynamic Simulations of Proto-Jupiter's\nConvective Envelope", "chunk_text": "The\nenvelopes are heated at given rates to achieve steady states, enabling\ncomparisons with 1D models. We vary envelope properties to obtain both\nradiative and convective solutions. Using a passive scalar, we observe\nsignificant mass recycling on the orbital timescale. For a radiative envelope,\nrecycling can only penetrate from the disc surface until $\\sim$0. 1-0.", "found_variables": ["rate", "heat"], "variable_count": 2, "chunk_length": 366}, "score": 0.5123536586761475, "query": "Recycle Flow and Purge Rate"}, {"chunk": {"chunk_id": "1807.03623v1_chunk_0", "paper_id": "1807.03623v1", "paper_title": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "chunk_text": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "found_variables": ["temperature", "reactor", "temp"], "variable_count": 3, "chunk_length": 68}, "score": 0.5083276033401489, "query": "Recycle Flow Purge Rate interaction"}, {"chunk": {"chunk_id": "2103.05025v1_chunk_3", "paper_id": "2103.05025v1", "paper_title": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate", "chunk_text": "Our\ncase study based on switchgrass finds that HPC reduces the variation of a\nreactor's feeding rate by 100\\% without increasing the operating cost of the\nbiomass pre-processing system for biomass with moisture ranging from 10 to\n25\\%. A biorefinery can adapt HPC to achieve its design capacity.", "found_variables": ["feed", "reactor", "rate"], "variable_count": 3, "chunk_length": 295}, "score": 0.5004725456237793, "query": "Recycle Flow influence Purge Rate"}, {"chunk": {"chunk_id": "pdf_3373_chunk_5", "paper_id": "pdf_3373", "paper_title": "1 Predictions and Uncertainty Estimates of Reactor Pressure Vessel Steel Embrittlement Using  ", "chunk_text": "Furthermore, our detailed \ncomparisons show our approach improves on the leading American Society for Testing and \nMaterials (ASTM) E900 -15 standard model for RPV embrittlement on every metric we assessed, \ndemonstrating th e efficacy of machine learning approaches for this type of highly demanding \nmaterials property prediction.\n\n1. Introduction:  \nNuclear power is a key component of global clean energy production, producing roughly \n20% of the power", "found_variables": ["component"], "variable_count": 1, "chunk_length": 456}, "score": 0.4950079917907715, "query": "Recycle Flow and Purge Rate"}, {"chunk": {"chunk_id": "pdf_4550_chunk_5", "paper_id": "pdf_4550", "paper_title": "A Multi-stage Stochastic Programming Model for Adaptive Biomass Processing", "chunk_text": "We show\nthe value of multi-stage stochastic programming from an extensive computational experiment. Our\nsensitivity analysis indicates that updating the infeed rate of the system, the processing speed of\nequipment, and bale sequencing based on the moisture level of biomass improves the processing rate\nof the reactor and reduces operating costs. KEYWORDS\nbioenergy, biomass, biomass processing system, multi-stage stochastic programming\n1.", "found_variables": ["feed", "reactor", "rate", "level"], "variable_count": 4, "chunk_length": 440}, "score": 0.4946760833263397, "query": "Recycle Flow influence Purge Rate"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}