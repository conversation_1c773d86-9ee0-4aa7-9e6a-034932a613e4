{"variable_pair": ["Reactor Level", "Component D Reactor Feed"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Reactor Level → Component D Reactor Feed", "association_votes": {"A": 2, "?": 5, "IA": 7, "⊥": 1}, "direction_votes": {"Reactor Level - Component D Reactor Feed": 1, "Reactor Level → Component D Reactor Feed": 7, "Component D Reactor Feed → Reactor Level": 1}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "0402435v1_chunk_0", "chunk_text": "The Indo-U.S. Library of Coude Feed Stellar Spectra", "retrieval_score": 0.5986202955245972, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_2177_chunk_2", "chunk_text": "Eksioglub, <PERSON>ushi <PERSON>c,\naEnergy and Environment Science & Technology Department, Idaho National Laboratory, Idaho Falls, ID\nbIndustrial Engineering Department, University of Arkansas, Fayetteville, AR\ncGlenn Department of Civil Engineering, Clemson University, Clemson, SC\nAbstract\nThe variations in feedstock characteristics such as moisture and particle size distribution\nlead to an inconsistent ﬂow of feedstock from the biomass pre-processing system to the reactor\nin-feed system.", "retrieval_score": 0.5604185461997986, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Component D Reactor Feed → Reactor Level"}, {"chunk_id": "pdf_819_chunk_0", "chunk_text": "Geometric Decomposition of Feed Forward", "retrieval_score": 0.5583454370498657, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Level → Component D Reactor Feed"}, {"chunk_id": "pdf_7578_chunk_0", "chunk_text": "Correlation between radiation damage and magnetic properties in reactor vessel", "retrieval_score": 0.5506003499031067, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_2177_chunk_1", "chunk_text": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate\n<PERSON><PERSON><PERSON>, <PERSON>,1, <PERSON>.", "retrieval_score": 0.5418946743011475, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Level → Component D Reactor Feed"}, {"chunk_id": "pdf_1500_chunk_0", "chunk_text": "arXiv:astro-ph/0402435v1  18 Feb 2004The Indo-U.S. Library of Coud´ e Feed Stellar Spectra", "retrieval_score": 0.5402564406394958, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2103.05025v1_chunk_0", "chunk_text": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate", "retrieval_score": 0.534967303276062, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Level → Component D Reactor Feed"}, {"chunk_id": "pdf_2859_chunk_0", "chunk_text": "Feed-forward neural network unfolding", "retrieval_score": 0.5342519283294678, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Level → Component D Reactor Feed"}, {"chunk_id": "2112.08180v1_chunk_0", "chunk_text": "Feed-forward neural network unfolding", "retrieval_score": 0.5342519283294678, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_1428_chunk_0", "chunk_text": "Development of a Feeding Assistive Robot Using a Six Degree of", "retrieval_score": 0.5324068069458008, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Level → Component D Reactor Feed"}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5303939580917358, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Level → Component D Reactor Feed"}, {"chunk_id": "pdf_6635_chunk_3", "chunk_text": "The\nframework is illustrated via a computational model of the deposition of Cu\nin a Chemical Vapor Deposition (CVD) reactor, where the reactor pressure,\nthe deposition temperature and feed mass \row rate are important process\nparameters that determine the outcome of the process. The sampled ob-\nservations are high-dimensional vectors containing the outputs of a detailed\nCFD steady-state model of the process, i. e.", "retrieval_score": 0.527230978012085, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Level → Component D Reactor Feed"}, {"chunk_id": "pdf_7578_chunk_5", "chunk_text": "Key Word s: reactorpressure vessel (RPV), neutron irradiation, dose -rate effects,\nmagnetic properties of steels. 1. Introduction\nTheintegrity of the components of power plant reactors throughout their service life is\naffected by the degradation suffered by t.", "retrieval_score": 0.5272042751312256, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_6125_chunk_5", "chunk_text": "The LLRF uses four approximate programme d curves to control the acceleration \nprocess: FREQ, Bias, ROF, and RAG. With  the input of the phase error from phase FERMILAB-TM-2271-AD \n 2feedback of LLRF, VXI LLRF generates the LO -Freq using the FREQ program in such a \nway that the LO-Freq has the same frequenc y and a constant phase difference with the \nCB through a cycle. The radial feedba.", "retrieval_score": 0.5247136354446411, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Level - Component D Reactor Feed"}, {"chunk_id": "pdf_6635_chunk_5", "chunk_text": "g. temperature in the reactor) given other par-\ntial observations (e. g. mass fraction in the reactor). The proposed work\row\nrelies on the manifold learning schemes Di\u000busion Maps and the associated\nGeometric Harmonics. Di\u000busion Maps is used for discovering a reduced rep-\nresent.", "retrieval_score": 0.5166019201278687, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}], "original_retrieval_results": 15}