{"variable_pair": ["Prod Sep Underflow", "Stripper Steam Flow"], "results": [{"chunk": {"chunk_id": "pdf_4024_chunk_4", "paper_id": "pdf_4024", "paper_title": "Root-KGD: A Novel Framework for Root Cause", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "found_variables": ["TEP", "flow", "Tennessee Eastman"], "variable_count": 3, "chunk_length": 469}, "score": 0.5208927392959595, "query": "Prod Sep Underflow and Stripper Steam Flow"}, {"chunk": {"chunk_id": "pdf_6040_chunk_0", "paper_id": "pdf_6040", "paper_title": "Transmission Expansion Planning Using Cycle Flows", "chunk_text": "Transmission Expansion Planning Using Cycle Flows", "found_variables": ["flow"], "variable_count": 1, "chunk_length": 49}, "score": 0.5158311724662781, "query": "Prod Sep Underflow and Stripper Steam Flow"}, {"chunk": {"chunk_id": "2004.08702v1_chunk_0", "paper_id": "2004.08702v1", "paper_title": "Transmission Expansion Planning Using Cycle Flows", "chunk_text": "Transmission Expansion Planning Using Cycle Flows", "found_variables": ["flow"], "variable_count": 1, "chunk_length": 49}, "score": 0.5158311128616333, "query": "Prod Sep Underflow and Stripper Steam Flow"}, {"chunk": {"chunk_id": "pdf_7861_chunk_0", "paper_id": "pdf_7861", "paper_title": "Online Fault Detection and Classification of Chemical Process", "chunk_text": "Online Fault Detection and Classification of Chemical Process", "found_variables": ["fault detection", "chemical process"], "variable_count": 2, "chunk_length": 61}, "score": 0.5076424479484558, "query": "Prod Sep Underflow and Stripper Steam Flow"}, {"chunk": {"chunk_id": "pdf_9723_chunk_4", "paper_id": "pdf_9723", "paper_title": "Draft version July 10, 2024", "chunk_text": "Here, we reevaluate this line of reasoning by incorporating recycling flows of gas\ninto a numerical one-dimensional thermodynamic model with more realistic equation of state and\nopacities and the thermal state of the advective flow. At 0. 1 AU, we find that advective flows are\nonly able to produce mini-Neptunes if they can penetrate below ∼0. 25 of the planet’s gravitational\nsphere of influence.", "found_variables": ["rate", "thermal", "flow"], "variable_count": 3, "chunk_length": 398}, "score": 0.5054295659065247, "query": "Prod Sep Underflow and Stripper Steam Flow"}, {"chunk": {"chunk_id": "pdf_5610_chunk_3", "paper_id": "pdf_5610", "paper_title": "Highlights", "chunk_text": "Stadium\nAve, West Lafayette, 47907, IN, USA\nAbstract\nMachine learning algorithms are increasingly being applied to fault detection\nand diagnosis (FDD) in chemical processes. However, existing data-driven\nFDD platforms often lack interpretability for process operators and strug-\ngle to identify root causes of previously unseen faults. This paper presents\nFaultExplainer , an interactive tool designed to improve fault detection, diag-\nnosis, and explanation in the Tennessee Eastman Process (TEP).", "found_variables": ["TEP", "fault detection", "chemical process", "Tennessee Eastman"], "variable_count": 4, "chunk_length": 498}, "score": 0.5004889369010925, "query": "Prod Sep Underflow and Stripper Steam Flow"}, {"chunk": {"chunk_id": "pdf_9160_chunk_4", "paper_id": "pdf_9160", "paper_title": "The dominant mechanisms for the formation of solute-rich clusters in  low-Cu steels under irradiatio [1912.06828v2]", "chunk_text": "The\tmechanisms\tdriving\ttheir\tformation\tare\tstill\tdebated. This\twork\tfocuses\ton\tlow-Cu\treactor\t pressure\t vessel\t (RPV)\tsteels,\t where\tsolute\t species\t are\tgenerally\t not\texpected\tto\tprecipitate. We\trationalize\tthe\tprocesses\tthat\ttake\tplace\tat\tthe\tnanometre\tscale\tunder\tirradiation,\trelying\ton\tthe\tlatest\ttheoretical\tand\texperimental\tevidence\ton\tatomic-level\tdiffusion\tand\ttransport\tprocesses. These\tare\tcompiled\tin\ta\tnew\tmodel,\tbased\ton\tthe\tobject\tkinetic\tMonte\tCarlo\t(OKMC)\ttechnique.", "found_variables": ["pressure", "press", "reactor", "work", "level"], "variable_count": 5, "chunk_length": 485}, "score": 0.4942322373390198, "query": "Prod Sep Underflow and Stripper Steam Flow"}, {"chunk": {"chunk_id": "2301.05537v4_chunk_2", "paper_id": "2301.05537v4", "paper_title": "Almost Surely $\\sqrt{T}$ Regret for Adaptive LQR", "chunk_text": "The\ncontroller features a circuit-breaking mechanism, which circumvents potential\nsafety breach and guarantees the convergence of the system parameter estimate,\nbut is shown to be triggered only finitely often and hence has negligible\neffect on the asymptotic performance of the controller. The proposed controller\nis also validated via simulation on Tennessee Eastman Process~(TEP), a commonly\nused industrial process example.", "found_variables": ["vent", "TEP", "Tennessee Eastman"], "variable_count": 3, "chunk_length": 427}, "score": 0.4934527277946472, "query": "Prod Sep Underflow and Stripper Steam Flow"}, {"chunk": {"chunk_id": "pdf_6811_chunk_5", "paper_id": "pdf_6811", "paper_title": "GENERATIVE ADVERSARIAL WAVELET NEURAL OPERATOR :", "chunk_text": "In the second stage, a reconstruction error-based threshold approach using the trained\nGAWNO is employed to detect and isolate faults based on the discrepancy values. We validate the\nproposed approach using the Tennessee Eastman Process (TEP) dataset and Avedore wastewater\ntreatment plant (WWTP) and N2Oemissions nam.", "found_variables": ["TEP", "Tennessee Eastman"], "variable_count": 2, "chunk_length": 318}, "score": 0.4918380379676819, "query": "Prod Sep Underflow and Stripper Steam Flow"}, {"chunk": {"chunk_id": "2103.12222v1_chunk_1", "paper_id": "2103.12222v1", "paper_title": "Explainability: Relevance based Dynamic Deep Learning Algorithm for\nFault Detection and Diagnosis in Chemical Processes", "chunk_text": "The focus of this work is on Statistical Process Control (SPC) of a\nmanufacturing process based on available measurements. Two important\napplications of SPC in industrial settings are fault detection and diagnosis\n(FDD). In this work a deep learning (DL) based methodology is proposed for FDD. We investigate the application of an explainability concept to enhance the FDD\naccuracy of a deep neural network model trained with a data set of relatively\nsmall number of samples.", "found_variables": ["process control", "work", "fault detection"], "variable_count": 3, "chunk_length": 475}, "score": 0.4911942183971405, "query": "Prod Sep Underflow and Stripper Steam Flow"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}