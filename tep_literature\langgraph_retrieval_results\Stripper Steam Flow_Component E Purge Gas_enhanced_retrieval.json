{"variable_pair": ["Stripper Steam Flow", "Component E Purge Gas"], "results": [{"chunk": {"chunk_id": "2310.06013v3_chunk_0", "paper_id": "2310.06013v3", "paper_title": "The Not-So Dramatic Effect of Advective Flows on Gas Accretion", "chunk_text": "The Not-So Dramatic Effect of Advective Flows on Gas Accretion", "found_variables": ["flow"], "variable_count": 1, "chunk_length": 62}, "score": 0.48284482955932617, "query": "effect of Stripper Steam Flow on Component E Purge Gas"}, {"chunk": {"chunk_id": "pdf_9901_chunk_4", "paper_id": "pdf_9901", "paper_title": "Root-KGD: A Novel Framework for Root Cause", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "found_variables": ["TEP", "flow", "Tennessee Eastman"], "variable_count": 3, "chunk_length": 469}, "score": 0.4796083867549896, "query": "Stripper Steam Flow and Component E Purge Gas"}, {"chunk": {"chunk_id": "pdf_184_chunk_3", "paper_id": "pdf_184", "paper_title": "Highlights", "chunk_text": "Stadium\nAve, West Lafayette, 47907, IN, USA\nAbstract\nMachine learning algorithms are increasingly being applied to fault detection\nand diagnosis (FDD) in chemical processes. However, existing data-driven\nFDD platforms often lack interpretability for process operators and strug-\ngle to identify root causes of previously unseen faults. This paper presents\nFaultExplainer , an interactive tool designed to improve fault detection, diag-\nnosis, and explanation in the Tennessee Eastman Process (TEP).", "found_variables": ["TEP", "chemical process", "Tennessee Eastman", "fault detection"], "variable_count": 4, "chunk_length": 498}, "score": 0.47456231713294983, "query": "Stripper Steam Flow and Component E Purge Gas"}, {"chunk": {"chunk_id": "pdf_668_chunk_5", "paper_id": "pdf_668", "paper_title": "arXiv:2205.05250v2  [cs.LG]  5 Oct 2023Spatial-temporal associations representation and applic ation for process", "chunk_text": "Finally, these networks corresponding to process sta tes at diﬀerent times are fed into a graph convolutional\nneural network to implement graph classiﬁcation to achieve process monitoring. A benchmark experiment (Ten-\nnessee Eastman chemical process) and one application study (cobalt puriﬁcation from zinc solution) are employed to\ndemonstrate the feasibility and applicability of this pape r. Keywords: Spatial-Temporal Associations Representation; Process M onitoring; Stat.", "found_variables": ["work", "chemical process", "rate", "temp"], "variable_count": 4, "chunk_length": 477}, "score": 0.470961332321167, "query": "correlation Stripper Steam Flow Component E Purge Gas"}, {"chunk": {"chunk_id": "pdf_2482_chunk_5", "paper_id": "pdf_2482", "paper_title": "Fault Detection and Identi\fcation Using", "chunk_text": "The performance of the method is demonstrated\nand contrasted to (dynamic) principal component analysis, which is widely applied in the industry,\nin the benchmark Tennessee Eastman process (TEP) and a real chemical manufacturing dataset. Keywords: fault detection, fault identi\fcation, recurrent neural networks, variational dropout,\nBayesian inference, Tennessee Eastman process. 1.", "found_variables": ["component", "Tennessee Eastman", "rate", "work", "TEP", "fault detection"], "variable_count": 6, "chunk_length": 382}, "score": 0.46866661310195923, "query": "Stripper Steam Flow and Component E Purge Gas"}, {"chunk": {"chunk_id": "pdf_6995_chunk_0", "paper_id": "pdf_6995", "paper_title": "Enhanced Fault Detection and Cause Identification Using ", "chunk_text": "Enhanced Fault Detection and Cause Identification Using", "found_variables": ["fault detection"], "variable_count": 1, "chunk_length": 55}, "score": 0.46718329191207886, "query": "Stripper Steam Flow and Component E Purge Gas"}, {"chunk": {"chunk_id": "pdf_1546_chunk_0", "paper_id": "pdf_1546", "paper_title": "Online Fault Detection and Classification of Chemical Process", "chunk_text": "Online Fault Detection and Classification of Chemical Process", "found_variables": ["chemical process", "fault detection"], "variable_count": 2, "chunk_length": 61}, "score": 0.46618640422821045, "query": "Stripper Steam Flow and Component E Purge Gas"}, {"chunk": {"chunk_id": "pdf_3373_chunk_4", "paper_id": "pdf_3373", "paper_title": "1 Predictions and Uncertainty Estimates of Reactor Pressure Vessel Steel Embrittlement Using  ", "chunk_text": "These improvements provide a model with \nsignificant new capabilities, including the ability to easily and accurately explore compositions, \nflux, and fluence effects on RPV stee l embrittlement for the first time.", "found_variables": ["rate", "composition"], "variable_count": 2, "chunk_length": 214}, "score": 0.4659319519996643, "query": "Stripper Steam Flow and Component E Purge Gas"}, {"chunk": {"chunk_id": "pdf_9435_chunk_3", "paper_id": "pdf_9435", "paper_title": "On the Parametric Study of Lubricating Oil Production using an ", "chunk_text": "1. Introduction  \nIt is necessary to remove aromatic compounds from heavy petroleum cuts to improve the \nquality of the produced  lubricating base oils [ 1-2]. Extraction o f aromatic compounds  from \nlubricating  oil cut is usually done through a  liquid -liquid (solvent) extraction process. Several \nworks have been done to find a suitabl e solvent for extraction of aromatic hydrocarbons fr om \nlube-oil cut [ 3-5].", "found_variables": ["work", "vent"], "variable_count": 2, "chunk_length": 419}, "score": 0.4649149179458618, "query": "Stripper Steam Flow and Component E Purge Gas"}, {"chunk": {"chunk_id": "1712.04118v1_chunk_0", "paper_id": "1712.04118v1", "paper_title": "Neural Component Analysis for Fault Detection", "chunk_text": "Neural Component Analysis for Fault Detection", "found_variables": ["component", "fault detection"], "variable_count": 2, "chunk_length": 45}, "score": 0.4645857810974121, "query": "Stripper Steam Flow and Component E Purge Gas"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}