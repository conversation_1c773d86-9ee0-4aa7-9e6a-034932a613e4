{"variable_pair": ["<PERSON>ed", "A and C Feed"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "E Feed → A and C Feed", "association_votes": {"A": 5, "⊥": 3, "IA": 7}, "direction_votes": {"E Feed → A and C Feed": 8, "E Feed - A and C Feed": 4}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "pdf_9441_chunk_0", "chunk_text": "On the space of coefﬁcients of a Feed Forward", "retrieval_score": 0.5912078619003296, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed → A and C Feed"}, {"chunk_id": "pdf_2859_chunk_0", "chunk_text": "Feed-forward neural network unfolding", "retrieval_score": 0.5884096026420593, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "E Feed - A and C Feed"}, {"chunk_id": "2112.08180v1_chunk_0", "chunk_text": "Feed-forward neural network unfolding", "retrieval_score": 0.5884096026420593, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed → A and C Feed"}, {"chunk_id": "pdf_819_chunk_0", "chunk_text": "Geometric Decomposition of Feed Forward", "retrieval_score": 0.5723037123680115, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2403.10842v4_chunk_0", "chunk_text": "Twin Transformer using Gated Dynamic Learnable Attention mechanism for\nFault Detection and Diagnosis in the Tennessee Eastman Process", "retrieval_score": 0.5570012331008911, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed - A and C Feed"}, {"chunk_id": "1712.01580v1_chunk_0", "chunk_text": "The Lifting Bifurcation Problem on Feed-Forward Networks", "retrieval_score": 0.5508896112442017, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed → A and C Feed"}, {"chunk_id": "2309.00157v1_chunk_3", "chunk_text": "We validate the approach\nusing the Benchmark Tennessee Eastman while doing an ablation study of the\nmodel update parameters.", "retrieval_score": 0.5438389778137207, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "E Feed → A and C Feed"}, {"chunk_id": "2301.11573v3_chunk_3", "chunk_text": "The theoretical analysis is verified through\nMonte Carlo simulations and Tennessee Eastman process (TEP) dataset.", "retrieval_score": 0.539580762386322, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed → A and C Feed"}, {"chunk_id": "2403.13502v4_chunk_0", "chunk_text": "Adversarial Attacks and Defenses in Fault Detection and Diagnosis: A\nComprehensive Benchmark on the Tennessee Eastman Process", "retrieval_score": 0.5372281074523926, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed → A and C Feed"}, {"chunk_id": "1607.07423v3_chunk_5", "chunk_text": "We illustrate the\nsuccessful use of $K_T$ chart using the Tennessee Eastman process data.", "retrieval_score": 0.5360596776008606, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "E Feed → A and C Feed"}, {"chunk_id": "2109.03362v1_chunk_0", "chunk_text": "On the space of coefficients of a Feed Forward Neural Network", "retrieval_score": 0.5352609157562256, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2203.11321v1_chunk_4", "chunk_text": "Finally, as a case study, the proposed\nmodel is implemented in the well-known Tennessee Eastman process, and the\nresults are presented.", "retrieval_score": 0.534190833568573, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "E Feed - A and C Feed"}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5332633256912231, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "1709.02232v1_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5291545391082764, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "E Feed - A and C Feed"}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5291544198989868, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed → A and C Feed"}], "original_retrieval_results": 15}