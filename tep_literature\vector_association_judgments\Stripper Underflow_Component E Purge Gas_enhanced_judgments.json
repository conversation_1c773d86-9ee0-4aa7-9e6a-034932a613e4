{"variable_pair": ["Stripper Underflow", "Component E Purge Gas"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Stripper Underflow → Component E Purge Gas", "association_votes": {"A": 2, "?": 2, "IA": 11}, "direction_votes": {"Stripper Underflow → Component E Purge Gas": 9, "Stripper Underflow - Component E Purge Gas": 4}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5458709597587585, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_9406_chunk_5", "chunk_text": "The performance of the method is demonstrated\nand contrasted to (dynamic) principal component analysis, which is widely applied in the industry,\nin the benchmark Tennessee Eastman process (TEP) and a real chemical manufacturing dataset. Keywords: fault detection, fault identi\fcation, recurrent neural networks, variational dropout,\nBayesian inference, Tennessee Eastman process. 1.", "retrieval_score": 0.5277849435806274, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_1917_chunk_3", "chunk_text": "Stadium\nAve, West Lafayette, 47907, IN, USA\nAbstract\nMachine learning algorithms are increasingly being applied to fault detection\nand diagnosis (FDD) in chemical processes. However, existing data-driven\nFDD platforms often lack interpretability for process operators and strug-\ngle to identify root causes of previously unseen faults. This paper presents\nFaultExplainer , an interactive tool designed to improve fault detection, diag-\nnosis, and explanation in the Tennessee Eastman Process (TEP).", "retrieval_score": 0.5274156928062439, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow → Component E Purge Gas"}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.517498791217804, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow - Component E Purge Gas"}, {"chunk_id": "2403.13502v4_chunk_0", "chunk_text": "Adversarial Attacks and Defenses in Fault Detection and Diagnosis: A\nComprehensive Benchmark on the Tennessee Eastman Process", "retrieval_score": 0.5169386863708496, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow - Component E Purge Gas"}, {"chunk_id": "pdf_7856_chunk_5", "chunk_text": "In the second stage, a reconstruction error-based threshold approach using the trained\nGAWNO is employed to detect and isolate faults based on the discrepancy values. We validate the\nproposed approach using the Tennessee Eastman Process (TEP) dataset and Avedore wastewater\ntreatment plant (WWTP) and N2Oemissions nam.", "retrieval_score": 0.5146217942237854, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow → Component E Purge Gas"}, {"chunk_id": "2403.10842v4_chunk_0", "chunk_text": "Twin Transformer using Gated Dynamic Learnable Attention mechanism for\nFault Detection and Diagnosis in the Tennessee Eastman Process", "retrieval_score": 0.5115950107574463, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow → Component E Purge Gas"}, {"chunk_id": "pdf_131_chunk_0", "chunk_text": "arXiv:2303.05904v1  [cs.LG]  10 Mar 2023Deep Anomaly Detection on Tennessee Eastman Process Data", "retrieval_score": 0.510215163230896, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow → Component E Purge Gas"}, {"chunk_id": "pdf_4254_chunk_5", "chunk_text": "Keywords: Tennessee Eastman Process, Chemical Processes, Graph neural\nnetworks, Fault diagnosis, Sensor dataarXiv:2210. 11164v1  [cs. AI]  20 Oct 2022\n1. Introduction\nDuring the production, equipment often stops due to the various faults. The process of \fndin.", "retrieval_score": 0.509310781955719, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Underflow - Component E Purge Gas"}, {"chunk_id": "pdf_9735_chunk_4", "chunk_text": "We evaluate our approach using the\nTennessee-Eastman process. Results show that our approach\ncan be used to distinguish disturbances from intrusions to a\ncertain extent and we conclude that the proposed approach can\nbe extended with other sources of data for improving results. Keywords -Process control systems, Multivariate Statistical\nProcess Control, Tennessee-Eastman,\nI.", "retrieval_score": 0.5072952508926392, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow → Component E Purge Gas"}, {"chunk_id": "1911.04386v2_chunk_4", "chunk_text": "The\noutstanding performance of this method is demonstrated and contrasted to\n(dynamic) principal component analysis, which are widely applied in the\nindustry, in the benchmark Tennessee Eastman process~(TEP) and a real chemical\nmanufacturing dataset.", "retrieval_score": 0.5050467252731323, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow → Component E Purge Gas"}, {"chunk_id": "2308.11247v2_chunk_0", "chunk_text": "Benchmarking Domain Adaptation for Chemical Processes on the Tennessee\nEastman Process", "retrieval_score": 0.5049192905426025, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow → Component E Purge Gas"}, {"chunk_id": "pdf_6959_chunk_5", "chunk_text": "Simulation results demonstrate the efficacy of this approach, showcasing superior performance in \naccuracy, false alarm rate, and misclassifica tion rate compared to existing methods. This \nmethodology provides a robust and interpretable solution for fault detection and diagnosis in the \nTEP, highlighting its potential for industrial applications. Keywords. Fault Detection, Tennessee Eastman Process, Supervised Feature Selection, Attention \nMechanism, Bidirectional Long Short -Term Memory.", "retrieval_score": 0.5048048496246338, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow → Component E Purge Gas"}, {"chunk_id": "1607.07423v3_chunk_5", "chunk_text": "We illustrate the\nsuccessful use of $K_T$ chart using the Tennessee Eastman process data.", "retrieval_score": 0.50295090675354, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow → Component E Purge Gas"}, {"chunk_id": "2203.11321v1_chunk_4", "chunk_text": "Finally, as a case study, the proposed\nmodel is implemented in the well-known Tennessee Eastman process, and the\nresults are presented.", "retrieval_score": 0.5028804540634155, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Underflow - Component E Purge Gas"}], "original_retrieval_results": 15}