{"variable_pair": ["<PERSON> Feed", "Stripper Underflow"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "D Feed → Stripper Underflow", "association_votes": {"A": 4, "?": 3, "IA": 7, "⊥": 1}, "direction_votes": {"D Feed - Stripper Underflow": 1, "D Feed → Stripper Underflow": 10}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "pdf_2859_chunk_0", "chunk_text": "Feed-forward neural network unfolding", "retrieval_score": 0.5757427215576172, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed → Stripper Underflow"}, {"chunk_id": "2112.08180v1_chunk_0", "chunk_text": "Feed-forward neural network unfolding", "retrieval_score": 0.5757427215576172, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.57293701171875, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "D Feed → Stripper Underflow"}, {"chunk_id": "pdf_819_chunk_0", "chunk_text": "Geometric Decomposition of Feed Forward", "retrieval_score": 0.5706335306167603, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed → Stripper Underflow"}, {"chunk_id": "2403.10842v4_chunk_0", "chunk_text": "Twin Transformer using Gated Dynamic Learnable Attention mechanism for\nFault Detection and Diagnosis in the Tennessee Eastman Process", "retrieval_score": 0.5482458472251892, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed → Stripper Underflow"}, {"chunk_id": "pdf_131_chunk_0", "chunk_text": "arXiv:2303.05904v1  [cs.LG]  10 Mar 2023Deep Anomaly Detection on Tennessee Eastman Process Data", "retrieval_score": 0.5415111780166626, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed → Stripper Underflow"}, {"chunk_id": "pdf_1917_chunk_3", "chunk_text": "Stadium\nAve, West Lafayette, 47907, IN, USA\nAbstract\nMachine learning algorithms are increasingly being applied to fault detection\nand diagnosis (FDD) in chemical processes. However, existing data-driven\nFDD platforms often lack interpretability for process operators and strug-\ngle to identify root causes of previously unseen faults. This paper presents\nFaultExplainer , an interactive tool designed to improve fault detection, diag-\nnosis, and explanation in the Tennessee Eastman Process (TEP).", "retrieval_score": 0.5327600240707397, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "D Feed → Stripper Underflow"}, {"chunk_id": "1712.01580v1_chunk_0", "chunk_text": "The Lifting Bifurcation Problem on Feed-Forward Networks", "retrieval_score": 0.5235461592674255, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_7856_chunk_5", "chunk_text": "In the second stage, a reconstruction error-based threshold approach using the trained\nGAWNO is employed to detect and isolate faults based on the discrepancy values. We validate the\nproposed approach using the Tennessee Eastman Process (TEP) dataset and Avedore wastewater\ntreatment plant (WWTP) and N2Oemissions nam.", "retrieval_score": 0.518520176410675, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "1709.02232v1_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5143647789955139, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "D Feed → Stripper Underflow"}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5143647193908691, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed → Stripper Underflow"}, {"chunk_id": "2403.13502v4_chunk_0", "chunk_text": "Adversarial Attacks and Defenses in Fault Detection and Diagnosis: A\nComprehensive Benchmark on the Tennessee Eastman Process", "retrieval_score": 0.5117302536964417, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2309.00157v1_chunk_3", "chunk_text": "We validate the approach\nusing the Benchmark Tennessee Eastman while doing an ablation study of the\nmodel update parameters.", "retrieval_score": 0.5109392404556274, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "D Feed → Stripper Underflow"}, {"chunk_id": "pdf_3937_chunk_3", "chunk_text": "We demon-\nstrate our model’s performance on two publicly available datasets of the Tennessee\nEastman Process with various faults. The results show that our method significantly\noutperforms existing approaches (+0. 2-0. 3 TPR for a fixed FPR) and effectively de-\ntects most of the process faults without expert annotation. Moreover, we show that the\nmodel fine-tuned on a small fraction of labeled data nearly reaches the performance of\na SOTA model trained on the full dataset.", "retrieval_score": 0.5099513530731201, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed - Stripper Underflow"}, {"chunk_id": "2403.11722v2_chunk_3", "chunk_text": "We\napply our proposed compression method on the Tennessee Eastman Dataset, where\nwe perform fault classification using the compressed data in two settings: a\nfully supervised one and in a semi supervised, contrastive learning setting. Both times, we were able to outperform real valued counterparts as well as two\nbaseline models: one with the uncompressed time-series as the input and the\nother with a regular downsampling using the mean.", "retrieval_score": 0.5088946223258972, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed → Stripper Underflow"}], "original_retrieval_results": 15}