{"variable_pair": ["Stripper Level", "Compressor Work"], "aggregation": {"has_causal_relationship": true, "final_association": "A", "final_direction": "Stripper Level → Compressor Work", "association_votes": {"A": 4, "⊥": 3, "IA": 4, "?": 4}, "direction_votes": {"Stripper Level - Compressor Work": 2, "Stripper Level → Compressor Work": 6}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.6044610738754272, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_2572_chunk_3", "chunk_text": "We apply our proposed compression\nmethod on the Tennessee Eastman Dataset, where we perform\nfault classification using the compressed data in two settings:\na fully supervised one and in a semi supervised, contrastive\nlearning setting. Both times, we were able to outperform real\nvalued counterparts as well as two baseline models: one with\nthe uncompressed time-series as the input and the other with a\nregular downsampling using the mean.", "retrieval_score": 0.5714600682258606, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Level → Compressor Work"}, {"chunk_id": "2403.11722v2_chunk_3", "chunk_text": "We\napply our proposed compression method on the Tennessee Eastman Dataset, where\nwe perform fault classification using the compressed data in two settings: a\nfully supervised one and in a semi supervised, contrastive learning setting. Both times, we were able to outperform real valued counterparts as well as two\nbaseline models: one with the uncompressed time-series as the input and the\nother with a regular downsampling using the mean.", "retrieval_score": 0.5714600086212158, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Level → Compressor Work"}, {"chunk_id": "2403.10842v4_chunk_0", "chunk_text": "Twin Transformer using Gated Dynamic Learnable Attention mechanism for\nFault Detection and Diagnosis in the Tennessee Eastman Process", "retrieval_score": 0.5683977603912354, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Level → Compressor Work"}, {"chunk_id": "2308.11247v2_chunk_0", "chunk_text": "Benchmarking Domain Adaptation for Chemical Processes on the Tennessee\nEastman Process", "retrieval_score": 0.5678892135620117, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_8440_chunk_1", "chunk_text": "Deep Compression of Neural Networks for Fault\nDetection on Tennessee Eastman Chemical\nProcesses\nMingxuan Li\nDepartment of Computer Science\nUniversity of North Carolina at Chapel Hill\nChapel Hill, NC, USA\nmingxuan li@unc. edu* <PERSON><PERSON>un Shao\nNuro Inc. Mountain View, CA, USA\nyuanxun@gatech. edu\nAbstract\nArtiﬁcial neural network has achieved the state-of-art performance in fault detection on the Tennessee Eastman process, but\nit often requires enormous memory to fund its massive parameters.", "retrieval_score": 0.5430945158004761, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2210.14595v1_chunk_4", "chunk_text": "Finally, we demonstrate the\neffectiveness of the proposed switching strategy via numerical simulation on\nthe Tennessee Eastman Process.", "retrieval_score": 0.5399880409240723, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Level → Compressor Work"}, {"chunk_id": "2212.04140v2_chunk_3", "chunk_text": "The effectiveness of the switching strategy is\nalso demonstrated via numerical simulation on the Tennessee Eastman Process.", "retrieval_score": 0.5397068858146667, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_6959_chunk_5", "chunk_text": "Simulation results demonstrate the efficacy of this approach, showcasing superior performance in \naccuracy, false alarm rate, and misclassifica tion rate compared to existing methods. This \nmethodology provides a robust and interpretable solution for fault detection and diagnosis in the \nTEP, highlighting its potential for industrial applications. Keywords. Fault Detection, Tennessee Eastman Process, Supervised Feature Selection, Attention \nMechanism, Bidirectional Long Short -Term Memory.", "retrieval_score": 0.5386256575584412, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Level - Compressor Work"}, {"chunk_id": "pdf_3937_chunk_3", "chunk_text": "We demon-\nstrate our model’s performance on two publicly available datasets of the Tennessee\nEastman Process with various faults. The results show that our method significantly\noutperforms existing approaches (+0. 2-0. 3 TPR for a fixed FPR) and effectively de-\ntects most of the process faults without expert annotation. Moreover, we show that the\nmodel fine-tuned on a small fraction of labeled data nearly reaches the performance of\na SOTA model trained on the full dataset.", "retrieval_score": 0.5382585525512695, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2301.11573v3_chunk_3", "chunk_text": "The theoretical analysis is verified through\nMonte Carlo simulations and Tennessee Eastman process (TEP) dataset.", "retrieval_score": 0.5371963977813721, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Level → Compressor Work"}, {"chunk_id": "2103.15186v1_chunk_2", "chunk_text": "The proposed approach is applied to an industrial\ncase study: Tennessee Eastman process. The results show that the proposed\napproach is successful in determining the probable cause of alarms generated\nwith high accuracy. The model was able to identify the cause accurately, even\nwhen tested with short alarm sub-sequences. This allows for early\nidentification of faults, providing more time to the operator to restore the\nsystem to normal operation.", "retrieval_score": 0.5350912809371948, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Level → Compressor Work"}, {"chunk_id": "2309.00157v1_chunk_3", "chunk_text": "We validate the approach\nusing the Benchmark Tennessee Eastman while doing an ablation study of the\nmodel update parameters.", "retrieval_score": 0.534859299659729, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_7276_chunk_4", "chunk_text": "These base classifiers based on broad learning system (BLS) are trained to\nensure maximum fault diagnosis accuracy. Furthermore, pseudo-label learning is used to update model parameters in real-time. The effectiveness of the proposed approach is demonstrated on the multi-mode Tennessee Eastman process dataset.", "retrieval_score": 0.5347207188606262, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Level - Compressor Work"}, {"chunk_id": "2305.00169v2_chunk_3", "chunk_text": "These base\nclassifiers based on broad learning system (BLS) are trained to ensure maximum\nfault diagnosis accuracy. Furthermore, pseudo-label learning is used to update\nmodel parameters in real-time. The effectiveness of the proposed approach is\ndemonstrated on the multi-mode Tennessee Eastman process dataset.", "retrieval_score": 0.5347207188606262, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}], "original_retrieval_results": 15}