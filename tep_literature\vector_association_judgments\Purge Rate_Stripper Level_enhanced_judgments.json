{"variable_pair": ["Purge Rate", "Stripper Level"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Stripper Level → Purge Rate", "association_votes": {"A": 2, "?": 3, "IA": 9, "⊥": 1}, "direction_votes": {"Purge Rate → Stripper Level": 4, "Stripper Level → Purge Rate": 5, "Purge Rate - Stripper Level": 2}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "pdf_3937_chunk_3", "chunk_text": "We demon-\nstrate our model’s performance on two publicly available datasets of the Tennessee\nEastman Process with various faults. The results show that our method significantly\noutperforms existing approaches (+0. 2-0. 3 TPR for a fixed FPR) and effectively de-\ntects most of the process faults without expert annotation. Moreover, we show that the\nmodel fine-tuned on a small fraction of labeled data nearly reaches the performance of\na SOTA model trained on the full dataset.", "retrieval_score": 0.5512079000473022, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.550265908241272, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Level → Purge Rate"}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5467702150344849, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Level → Purge Rate"}, {"chunk_id": "1709.02232v1_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5467701554298401, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate - Stripper Level"}, {"chunk_id": "1708.00077v1_chunk_2", "chunk_text": "5%\nsparsity level on sentiment analysis task without a quality drop and up to 87%\nsparsity level on language modeling task with slight loss of accuracy.", "retrieval_score": 0.5416725873947144, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Purge Rate → Stripper Level"}, {"chunk_id": "2309.00157v1_chunk_3", "chunk_text": "We validate the approach\nusing the Benchmark Tennessee Eastman while doing an ablation study of the\nmodel update parameters.", "retrieval_score": 0.5293865203857422, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Level → Purge Rate"}, {"chunk_id": "2212.04140v2_chunk_3", "chunk_text": "The effectiveness of the switching strategy is\nalso demonstrated via numerical simulation on the Tennessee Eastman Process.", "retrieval_score": 0.5248527526855469, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Level → Purge Rate"}, {"chunk_id": "pdf_6959_chunk_5", "chunk_text": "Simulation results demonstrate the efficacy of this approach, showcasing superior performance in \naccuracy, false alarm rate, and misclassifica tion rate compared to existing methods. This \nmethodology provides a robust and interpretable solution for fault detection and diagnosis in the \nTEP, highlighting its potential for industrial applications. Keywords. Fault Detection, Tennessee Eastman Process, Supervised Feature Selection, Attention \nMechanism, Bidirectional Long Short -Term Memory.", "retrieval_score": 0.52330082654953, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Stripper Level"}, {"chunk_id": "2308.11247v2_chunk_0", "chunk_text": "Benchmarking Domain Adaptation for Chemical Processes on the Tennessee\nEastman Process", "retrieval_score": 0.5169519186019897, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Level → Purge Rate"}, {"chunk_id": "2212.12092v1_chunk_5", "chunk_text": "Finally, we use the benchmark Tennessee Eastman to\nperform experiments to test the ensemble classifier's prediction and anomaly\ndetection capabilities.", "retrieval_score": 0.5165456533432007, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Stripper Level"}, {"chunk_id": "1607.07423v3_chunk_5", "chunk_text": "We illustrate the\nsuccessful use of $K_T$ chart using the Tennessee Eastman process data.", "retrieval_score": 0.****************, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2103.12222v1_chunk_2", "chunk_text": "The explainability is quantified by a novel relevance\nmeasure of input variables that is calculated from a Layerwise Relevance\nPropagation (LRP) algorithm. It is shown that the relevances can be used to\ndiscard redundant input feature vectors/ variables iteratively thus resulting\nin reduced over-fitting of noisy data, increasing distinguishability between\noutput classes and superior FDD test accuracy. The efficacy of the proposed\nmethod is demonstrated on the benchmark Tennessee Eastman Process.", "retrieval_score": 0.****************, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Stripper Level"}, {"chunk_id": "pdf_131_chunk_0", "chunk_text": "arXiv:2303.05904v1  [cs.LG]  10 Mar 2023Deep Anomaly Detection on Tennessee Eastman Process Data", "retrieval_score": 0.****************, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2210.14595v1_chunk_4", "chunk_text": "Finally, we demonstrate the\neffectiveness of the proposed switching strategy via numerical simulation on\nthe Tennessee Eastman Process.", "retrieval_score": 0.515484631061554, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate - Stripper Level"}, {"chunk_id": "2301.11573v3_chunk_3", "chunk_text": "The theoretical analysis is verified through\nMonte Carlo simulations and Tennessee Eastman process (TEP) dataset.", "retrieval_score": 0.5151387453079224, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}], "original_retrieval_results": 15}