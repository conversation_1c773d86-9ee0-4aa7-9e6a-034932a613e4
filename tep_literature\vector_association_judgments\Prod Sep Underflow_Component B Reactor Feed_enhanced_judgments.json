{"variable_pair": ["Prod Sep Underflow", "Component B Reactor Feed"], "aggregation": {"has_causal_relationship": true, "final_association": "A", "final_direction": "Prod Sep Underflow → Component B Reactor Feed", "association_votes": {"A": 5, "⊥": 3, "IA": 3, "?": 4}, "direction_votes": {"Prod Sep Underflow → Component B Reactor Feed": 5, "Prod Sep Underflow - Component B Reactor Feed": 3}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "pdf_2177_chunk_2", "chunk_text": "Eksioglub, <PERSON>ushi <PERSON>c,\naEnergy and Environment Science & Technology Department, Idaho National Laboratory, Idaho Falls, ID\nbIndustrial Engineering Department, University of Arkansas, Fayetteville, AR\ncGlenn Department of Civil Engineering, Clemson University, Clemson, SC\nAbstract\nThe variations in feedstock characteristics such as moisture and particle size distribution\nlead to an inconsistent ﬂow of feedstock from the biomass pre-processing system to the reactor\nin-feed system.", "retrieval_score": 0.5772027969360352, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_819_chunk_0", "chunk_text": "Geometric Decomposition of Feed Forward", "retrieval_score": 0.5648146867752075, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Prod Sep Underflow → Component B Reactor Feed"}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5492173433303833, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2103.05025v1_chunk_0", "chunk_text": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate", "retrieval_score": 0.5471411943435669, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Prod Sep Underflow → Component B Reactor Feed"}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.5383076667785645, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Prod Sep Underflow → Component B Reactor Feed"}, {"chunk_id": "2103.05025v1_chunk_1", "chunk_text": "The variations in feedstock characteristics such as moisture and particle\nsize distribution lead to an inconsistent flow of feedstock from the biomass\npre-processing system to the reactor in-feed system. These inconsistencies\nresult in low on-stream times at the reactor in-feed equipment. This research\ndevelops an optimal process control method for a biomass pre-processing system\ncomprised of milling and densification operations to provide the consistent\nflow of feedstock to a reactor's throat.", "retrieval_score": 0.5382789373397827, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2403.10842v4_chunk_0", "chunk_text": "Twin Transformer using Gated Dynamic Learnable Attention mechanism for\nFault Detection and Diagnosis in the Tennessee Eastman Process", "retrieval_score": 0.5345913171768188, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Prod Sep Underflow - Component B Reactor Feed"}, {"chunk_id": "2309.00157v1_chunk_3", "chunk_text": "We validate the approach\nusing the Benchmark Tennessee Eastman while doing an ablation study of the\nmodel update parameters.", "retrieval_score": 0.532904863357544, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_2177_chunk_3", "chunk_text": "These inconsistencies result in low on-stream times at the reactor in-feed\nequipment. This research develops an optimal process control method for a biomass pre-\nprocessing system comprised of milling and densiﬁcation operations to provide the consistent\nﬂow of feedstock to a reactor’s throat. This method uses a mixed-integer optimization\nmodel to identify optimal bale sequencing, equipment in-feed rate, and buﬀer location and\nsize in the biomass pre-processing system.", "retrieval_score": 0.5297756791114807, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Prod Sep Underflow → Component B Reactor Feed"}, {"chunk_id": "2103.07303v1_chunk_0", "chunk_text": "Second-Order Component Analysis for Fault Detection", "retrieval_score": 0.5268293023109436, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Prod Sep Underflow → Component B Reactor Feed"}, {"chunk_id": "pdf_131_chunk_0", "chunk_text": "arXiv:2303.05904v1  [cs.LG]  10 Mar 2023Deep Anomaly Detection on Tennessee Eastman Process Data", "retrieval_score": 0.5265418291091919, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_9441_chunk_0", "chunk_text": "On the space of coefﬁcients of a Feed Forward", "retrieval_score": 0.5226001739501953, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Prod Sep Underflow - Component B Reactor Feed"}, {"chunk_id": "1709.02232v1_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.519588828086853, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5195887684822083, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_2859_chunk_0", "chunk_text": "Feed-forward neural network unfolding", "retrieval_score": 0.518552303314209, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Prod Sep Underflow - Component B Reactor Feed"}], "original_retrieval_results": 15}