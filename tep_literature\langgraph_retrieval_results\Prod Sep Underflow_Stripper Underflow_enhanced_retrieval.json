{"variable_pair": ["Prod Sep Underflow", "Stripper Underflow"], "results": [{"chunk": {"chunk_id": "1706.02043v1_chunk_2", "paper_id": "1706.02043v1", "paper_title": "Reduced chemistry for butanol isomers at engine-relevant conditions", "chunk_text": "During the reduction\nprocess, issues were encountered with pressure-dependent reactions formulated\nusing the logarithmic pressure interpolation approach; these issues are\ndiscussed and recommendations made to avoid ambiguity in its future\nimplementation in mechanism development.", "found_variables": ["press", "pressure"], "variable_count": 2, "chunk_length": 279}, "score": 0.5377150774002075, "query": "Prod Sep Underflow Stripper Underflow interaction"}, {"chunk": {"chunk_id": "pdf_1321_chunk_3", "paper_id": "pdf_1321", "paper_title": "Reduced chemistry for butanol isomers at", "chunk_text": "During the reduction process, issues encountered with pressure-dependent\nreactions formulated using the logarithmic pressure interpolation approach were dis-\ncussed, with recommendations made to avoid ambiguity in its future implementation\nin mechanism development.", "found_variables": ["press", "pressure"], "variable_count": 2, "chunk_length": 265}, "score": 0.5261650085449219, "query": "Prod Sep Underflow Stripper Underflow interaction"}, {"chunk": {"chunk_id": "pdf_1546_chunk_0", "paper_id": "pdf_1546", "paper_title": "Online Fault Detection and Classification of Chemical Process", "chunk_text": "Online Fault Detection and Classification of Chemical Process", "found_variables": ["chemical process", "fault detection"], "variable_count": 2, "chunk_length": 61}, "score": 0.5109891891479492, "query": "Prod Sep Underflow and Stripper Underflow"}, {"chunk": {"chunk_id": "pdf_1839_chunk_4", "paper_id": "pdf_1839", "paper_title": "Draft version July 10, 2024", "chunk_text": "Here, we reevaluate this line of reasoning by incorporating recycling flows of gas\ninto a numerical one-dimensional thermodynamic model with more realistic equation of state and\nopacities and the thermal state of the advective flow. At 0. 1 AU, we find that advective flows are\nonly able to produce mini-Neptunes if they can penetrate below ∼0. 25 of the planet’s gravitational\nsphere of influence.", "found_variables": ["rate", "flow", "thermal"], "variable_count": 3, "chunk_length": 398}, "score": 0.5068256258964539, "query": "Prod Sep Underflow and Stripper Underflow"}, {"chunk": {"chunk_id": "pdf_8878_chunk_1", "paper_id": "pdf_8878", "paper_title": "Typeset using L ATEXtwocolumn style in AASTeX631", "chunk_text": "Typeset using L ATEXtwocolumn style in AASTeX631\nGrowing Planet Envelopes in Spite of Recycling Flows\nAvery P.", "found_variables": ["flow"], "variable_count": 1, "chunk_length": 110}, "score": 0.5009773373603821, "query": "Prod Sep Underflow and Stripper Underflow"}, {"chunk": {"chunk_id": "pdf_5305_chunk_4", "paper_id": "pdf_5305", "paper_title": "Fault Detection and Identification Using a Novel", "chunk_text": "ca (<PERSON>)\nPreprint submitted to Journal of Process Control December 24, 2024arXiv:2409. 11444v3  [eess. SY]  23 Dec 2024\nproposed method in a straightforward manner. Keywords: Fault Detection and Identification (FDI), Distributed Process\nMonitoring, PFD and Control Loop Based Process Decomposition,\nTennessee Eastman Plant (TEP)\n1.", "found_variables": ["Tennessee Eastman", "process control", "composition", "TEP", "fault detection"], "variable_count": 5, "chunk_length": 344}, "score": 0.4978860020637512, "query": "Prod Sep Underflow and Stripper Underflow"}, {"chunk": {"chunk_id": "pdf_184_chunk_3", "paper_id": "pdf_184", "paper_title": "Highlights", "chunk_text": "Stadium\nAve, West Lafayette, 47907, IN, USA\nAbstract\nMachine learning algorithms are increasingly being applied to fault detection\nand diagnosis (FDD) in chemical processes. However, existing data-driven\nFDD platforms often lack interpretability for process operators and strug-\ngle to identify root causes of previously unseen faults. This paper presents\nFaultExplainer , an interactive tool designed to improve fault detection, diag-\nnosis, and explanation in the Tennessee Eastman Process (TEP).", "found_variables": ["TEP", "chemical process", "Tennessee Eastman", "fault detection"], "variable_count": 4, "chunk_length": 498}, "score": 0.49773862957954407, "query": "Prod Sep Underflow and Stripper Underflow"}, {"chunk": {"chunk_id": "pdf_9901_chunk_4", "paper_id": "pdf_9901", "paper_title": "Root-KGD: A Novel Framework for Root Cause", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "found_variables": ["TEP", "flow", "Tennessee Eastman"], "variable_count": 3, "chunk_length": 469}, "score": 0.49654698371887207, "query": "Prod Sep Underflow and Stripper Underflow"}, {"chunk": {"chunk_id": "2409.11444v3_chunk_0", "paper_id": "2409.11444v3", "paper_title": "Fault Detection and Identification Using a Novel Process Decomposition\nAlgorithm for Distributed Process Monitoring", "chunk_text": "Fault Detection and Identification Using a Novel Process Decomposition\nAlgorithm for Distributed Process Monitoring", "found_variables": ["fault detection", "composition"], "variable_count": 2, "chunk_length": 115}, "score": 0.49266722798347473, "query": "Prod Sep Underflow and Stripper Underflow"}, {"chunk": {"chunk_id": "2301.05537v4_chunk_2", "paper_id": "2301.05537v4", "paper_title": "Almost Surely $\\sqrt{T}$ Regret for Adaptive LQR", "chunk_text": "The\ncontroller features a circuit-breaking mechanism, which circumvents potential\nsafety breach and guarantees the convergence of the system parameter estimate,\nbut is shown to be triggered only finitely often and hence has negligible\neffect on the asymptotic performance of the controller. The proposed controller\nis also validated via simulation on Tennessee Eastman Process~(TEP), a commonly\nused industrial process example.", "found_variables": ["TEP", "Tennessee Eastman", "vent"], "variable_count": 3, "chunk_length": 427}, "score": 0.49048542976379395, "query": "Prod Sep Underflow Stripper Underflow interaction"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}