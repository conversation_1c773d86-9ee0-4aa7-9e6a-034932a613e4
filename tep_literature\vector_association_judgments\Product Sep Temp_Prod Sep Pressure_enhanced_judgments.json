{"variable_pair": ["Product Sep Temp", "Prod Sep Pressure"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Product Sep Temp → Prod Sep Pressure", "association_votes": {"A": 5, "?": 1, "IA": 8, "⊥": 1}, "direction_votes": {"Product Sep Temp → Prod Sep Pressure": 8, "Product Sep Temp - Prod Sep Pressure": 5}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.5946431159973145, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Product Sep Temp → Prod Sep Pressure"}, {"chunk_id": "2301.11573v3_chunk_3", "chunk_text": "The theoretical analysis is verified through\nMonte Carlo simulations and Tennessee Eastman process (TEP) dataset.", "retrieval_score": 0.5941673517227173, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Product Sep Temp - Prod Sep Pressure"}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5932756662368774, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp → Prod Sep Pressure"}, {"chunk_id": "0104454v1_chunk_1", "chunk_text": "We have measured temperature dependence of thermoelectric power (TEP) on\nMgB$_2$ superconductor under hydrostatic pressure. The sign and temperature\ndependence of TEP shows metallic hole carriers are dominant with activation\ntype contribution at higher temperature. TEP increases with pressure while\n$T_c$ decrease with ratio -0.136 K/kbar. The data are discussed in\nconsideration of carriers from different bands and anisotropy of\ncompressibility.", "retrieval_score": 0.5877684354782104, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp → Prod Sep Pressure"}, {"chunk_id": "pdf_3170_chunk_2", "chunk_text": "An experimental setup was developed for the measurement of the\nthermoelectric power (TEP, Seebeck coeﬃcient) in the temperatu re range from 2\nto 350 K and magnetic ﬁelds up to 140 kOe. The system was built to ﬁt in a\ncommercial cryostat and is versatile, accurate and automated; u sing two heaters and\ntwo thermometers increases the accuracy of the TEP measureme nt.", "retrieval_score": 0.5864477753639221, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp → Prod Sep Pressure"}, {"chunk_id": "pdf_131_chunk_0", "chunk_text": "arXiv:2303.05904v1  [cs.LG]  10 Mar 2023Deep Anomaly Detection on Tennessee Eastman Process Data", "retrieval_score": 0.5851048231124878, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Product Sep Temp → Prod Sep Pressure"}, {"chunk_id": "2401.10266v2_chunk_3", "chunk_text": "Finally, a comparison of the\naccuracies and specifications of different algorithms utilizing the Tennessee\nEastman Process (TEP) is conducted. This research will be beneficial for both\nresearchers who are new to the field and experts, as it covers the literature\non condition monitoring and state-of-the-art methods alongside the challenges\nand possible solutions to them.", "retrieval_score": 0.5841841697692871, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2403.10842v4_chunk_1", "chunk_text": "Fault detection and diagnosis (FDD) is a crucial task for ensuring the safety\nand efficiency of industrial processes. We propose a novel FDD methodology for\nthe Tennessee Eastman Process (TEP), a widely used benchmark for chemical\nprocess control. The model employs two separate Transformer branches, enabling\nindependent processing of input data and potential extraction of diverse\ninformation.", "retrieval_score": 0.5801898241043091, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp → Prod Sep Pressure"}, {"chunk_id": "1911.04386v2_chunk_4", "chunk_text": "The\noutstanding performance of this method is demonstrated and contrasted to\n(dynamic) principal component analysis, which are widely applied in the\nindustry, in the benchmark Tennessee Eastman process~(TEP) and a real chemical\nmanufacturing dataset.", "retrieval_score": 0.5790345072746277, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp - Prod Sep Pressure"}, {"chunk_id": "1003.0921v1_chunk_1", "chunk_text": "An experimental setup was developed for the measurement of the thermoelectric\npower (TEP, Seebeck coefficient) in the temperature range from 2 to 350 K and\nmagnetic fields up to 140 kOe. The system was built to fit in a commercial\ncryostat and is versatile, accurate and automated; using two heaters and two\nthermometers increases the accuracy of the TEP measurement.", "retrieval_score": 0.5770488977432251, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp - Prod Sep Pressure"}, {"chunk_id": "pdf_6959_chunk_5", "chunk_text": "Simulation results demonstrate the efficacy of this approach, showcasing superior performance in \naccuracy, false alarm rate, and misclassifica tion rate compared to existing methods. This \nmethodology provides a robust and interpretable solution for fault detection and diagnosis in the \nTEP, highlighting its potential for industrial applications. Keywords. Fault Detection, Tennessee Eastman Process, Supervised Feature Selection, Attention \nMechanism, Bidirectional Long Short -Term Memory.", "retrieval_score": 0.5766153335571289, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp → Prod Sep Pressure"}, {"chunk_id": "1607.07423v3_chunk_5", "chunk_text": "We illustrate the\nsuccessful use of $K_T$ chart using the Tennessee Eastman process data.", "retrieval_score": 0.575144350528717, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_3805_chunk_2", "chunk_text": "Abstract: Fault detection and diagnosis (FDD) is a crucial task for ensuring the safety and \nefficiency of industrial processes. We propose a novel FDD methodology for the Tennessee \nEastman Process (TEP), a widely used benchmark for chemical process control. The model \nemploys two separate Transformer branches, enabling independent processing of input data and \npotential extraction of diverse information.", "retrieval_score": 0.5744913220405579, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Product Sep Temp → Prod Sep Pressure"}, {"chunk_id": "pdf_3937_chunk_3", "chunk_text": "We demon-\nstrate our model’s performance on two publicly available datasets of the Tennessee\nEastman Process with various faults. The results show that our method significantly\noutperforms existing approaches (+0. 2-0. 3 TPR for a fixed FPR) and effectively de-\ntects most of the process faults without expert annotation. Moreover, we show that the\nmodel fine-tuned on a small fraction of labeled data nearly reaches the performance of\na SOTA model trained on the full dataset.", "retrieval_score": 0.5696666240692139, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp - Prod Sep Pressure"}, {"chunk_id": "2203.11321v1_chunk_4", "chunk_text": "Finally, as a case study, the proposed\nmodel is implemented in the well-known Tennessee Eastman process, and the\nresults are presented.", "retrieval_score": 0.5658649802207947, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Product Sep Temp - Prod Sep Pressure"}], "original_retrieval_results": 15}