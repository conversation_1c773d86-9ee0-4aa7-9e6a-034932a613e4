{"variable_pair": ["Purge Rate", "Stripper Steam Flow"], "results": [{"chunk": {"chunk_id": "1807.03623v1_chunk_0", "paper_id": "1807.03623v1", "paper_title": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "chunk_text": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "found_variables": ["temperature", "temp", "reactor"], "variable_count": 3, "chunk_length": 68}, "score": 0.514478325843811, "query": "Purge Rate Stripper Steam Flow interaction"}, {"chunk": {"chunk_id": "pdf_6362_chunk_0", "paper_id": "pdf_6362", "paper_title": "Correlation between radiation damage and magnetic properties in reactor vessel", "chunk_text": "Correlation between radiation damage and magnetic properties in reactor vessel", "found_variables": ["reactor"], "variable_count": 1, "chunk_length": 78}, "score": 0.4934401512145996, "query": "correlation Purge Rate Stripper Steam Flow"}, {"chunk": {"chunk_id": "1003.0921v1_chunk_2", "paper_id": "1003.0921v1", "paper_title": "Experimental Setup for the Measurement of the Thermoelectric Power in\nZero and Applied Magnetic Field", "chunk_text": "High density data\nof temperature sweeps from 2 to 350 K can be acquired in under 16 hours and\nhigh density data of isothermal field sweeps from 0 to 140 kOe can be obtained\nin under 2 hours. Calibrations for the system have been performed on a platinum\nwire and Bi$_{2}$Sr$_{2}$CaCu$_{2}$O$_{8+\\delta}$ high $T_{c}$ superconductors. The measured TEP of phosphor-bronze (voltage lead wire) turns to be very small,\nwhere the absolute TEP value of phosphor-bronze wire is much less than 0.", "found_variables": ["temperature", "temp", "thermal", "TEP"], "variable_count": 4, "chunk_length": 486}, "score": 0.4850373864173889, "query": "Purge Rate and Stripper Steam Flow"}, {"chunk": {"chunk_id": "2107.08078v1_chunk_4", "paper_id": "2107.08078v1", "paper_title": "A Multi-stage Stochastic Programming Model for Adaptive Biomass\nProcessing Operation under Uncertainty", "chunk_text": "Our sensitivity\nanalysis indicates that updating the infeed rate of the system, the processing\nspeed of equipment, and bale sequencing based on the moisture level of biomass\nimproves the processing rate of the reactor and reduces operating costs.", "found_variables": ["feed", "rate", "level", "reactor"], "variable_count": 4, "chunk_length": 246}, "score": 0.4817730784416199, "query": "correlation Purge Rate Stripper Steam Flow"}, {"chunk": {"chunk_id": "pdf_6684_chunk_3", "paper_id": "pdf_6684", "paper_title": "arXiv:1003.0921v1  [physics.ins-det]  3 Mar 2010Experimental Setup for the Measurement of the", "chunk_text": "High density\ndata of temperature sweeps from 2 to 350 K can be acquired in unde r 16 hours and\nhigh density data of isothermal ﬁeld sweeps from 0 to 140 kOe can be obtained in\nunder 2 hours. Calibrations for the system have been performed o n a platinum wire\nand Bi 2Sr2CaCu2O8+δhighTcsuperconductors. The measured TEP of phosphor-\nbronze (voltage lead wire) turns to be very small, where the absolut e TEP value of\nphosphor-bronzewireismuch lessthan 0. 5 µV/Kbelow 80K.", "found_variables": ["temperature", "temp", "thermal", "TEP"], "variable_count": 4, "chunk_length": 470}, "score": 0.4800150692462921, "query": "Purge Rate and Stripper Steam Flow"}, {"chunk": {"chunk_id": "1708.00077v1_chunk_2", "paper_id": "1708.00077v1", "paper_title": "Bayesian Sparsification of Recurrent Neural Networks", "chunk_text": "5%\nsparsity level on sentiment analysis task without a quality drop and up to 87%\nsparsity level on language modeling task with slight loss of accuracy.", "found_variables": ["level"], "variable_count": 1, "chunk_length": 152}, "score": 0.4782686233520508, "query": "Purge Rate and Stripper Steam Flow"}, {"chunk": {"chunk_id": "1706.02043v1_chunk_3", "paper_id": "1706.02043v1", "paper_title": "Reduced chemistry for butanol isomers at engine-relevant conditions", "chunk_text": "The performance of the skeletal\nmechanisms generated here was compared with that of detailed mechanisms in\nsimulations of autoignition delay times, laminar flame speeds, and perfectly\nstirred reactor temperature response curves and extinction residence times,\nover a wide range of pressures, temperatures, and equivalence ratios.", "found_variables": ["temperature", "Reactor Temperature", "pressure", "rate", "temp", "press", "reactor"], "variable_count": 7, "chunk_length": 329}, "score": 0.4759000539779663, "query": "correlation Purge Rate Stripper Steam Flow"}, {"chunk": {"chunk_id": "2307.15094v1_chunk_2", "paper_id": "2307.15094v1", "paper_title": "Benchmarking of stainless steel cube neutron leakage in Research Center\nRez", "chunk_text": "The paper refers on validation efforts of neutron\nleakage from stainless steel block ~50 x 50 x50 cm in Research Center Rez. Both\nthe neutron leakage flux at a distance of 1 m from the center of the cubical\nassembly using stilbene spectrometry and the activation rates at different\npositions of the assembly were evaluated. In addition to experiments, main\nsources of uncertainty were identified and evaluated.", "found_variables": ["rate"], "variable_count": 1, "chunk_length": 410}, "score": 0.4757143259048462, "query": "correlation Purge Rate Stripper Steam Flow"}, {"chunk": {"chunk_id": "pdf_4762_chunk_5", "paper_id": "pdf_4762", "paper_title": "A Multi-stage Stochastic Programming Model for Adaptive Biomass Processing", "chunk_text": "We show\nthe value of multi-stage stochastic programming from an extensive computational experiment. Our\nsensitivity analysis indicates that updating the infeed rate of the system, the processing speed of\nequipment, and bale sequencing based on the moisture level of biomass improves the processing rate\nof the reactor and reduces operating costs. KEYWORDS\nbioenergy, biomass, biomass processing system, multi-stage stochastic programming\n1.", "found_variables": ["feed", "rate", "level", "reactor"], "variable_count": 4, "chunk_length": 440}, "score": 0.47494977712631226, "query": "Purge Rate and Stripper Steam Flow"}, {"chunk": {"chunk_id": "pdf_8086_chunk_4", "paper_id": "pdf_8086", "paper_title": "1 Predictions and Uncertainty Estimates of Reactor Pressure Vessel Steel Embrittlement Using  ", "chunk_text": "These improvements provide a model with \nsignificant new capabilities, including the ability to easily and accurately explore compositions, \nflux, and fluence effects on RPV stee l embrittlement for the first time.", "found_variables": ["composition", "rate"], "variable_count": 2, "chunk_length": 214}, "score": 0.47403430938720703, "query": "Purge Rate Stripper Steam Flow interaction"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}