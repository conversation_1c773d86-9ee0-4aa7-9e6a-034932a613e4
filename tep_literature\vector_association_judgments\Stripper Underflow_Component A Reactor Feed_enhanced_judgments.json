{"variable_pair": ["Stripper Underflow", "Component A Reactor Feed"], "aggregation": {"has_causal_relationship": true, "final_association": "A", "final_direction": "Stripper Underflow → Component A Reactor Feed", "association_votes": {"A": 6, "⊥": 3, "IA": 6}, "direction_votes": {"Stripper Underflow - Component A Reactor Feed": 2, "Stripper Underflow → Component A Reactor Feed": 10}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "pdf_2177_chunk_2", "chunk_text": "Eksioglub, <PERSON>ushi <PERSON>c,\naEnergy and Environment Science & Technology Department, Idaho National Laboratory, Idaho Falls, ID\nbIndustrial Engineering Department, University of Arkansas, Fayetteville, AR\ncGlenn Department of Civil Engineering, Clemson University, Clemson, SC\nAbstract\nThe variations in feedstock characteristics such as moisture and particle size distribution\nlead to an inconsistent ﬂow of feedstock from the biomass pre-processing system to the reactor\nin-feed system.", "retrieval_score": 0.5862848162651062, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow → Component A Reactor Feed"}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5662668347358704, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Underflow → Component A Reactor Feed"}, {"chunk_id": "2403.10842v4_chunk_0", "chunk_text": "Twin Transformer using Gated Dynamic Learnable Attention mechanism for\nFault Detection and Diagnosis in the Tennessee Eastman Process", "retrieval_score": 0.5581299066543579, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow → Component A Reactor Feed"}, {"chunk_id": "pdf_819_chunk_0", "chunk_text": "Geometric Decomposition of Feed Forward", "retrieval_score": 0.5579686760902405, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Underflow → Component A Reactor Feed"}, {"chunk_id": "2301.05537v4_chunk_2", "chunk_text": "The\ncontroller features a circuit-breaking mechanism, which circumvents potential\nsafety breach and guarantees the convergence of the system parameter estimate,\nbut is shown to be triggered only finitely often and hence has negligible\neffect on the asymptotic performance of the controller. The proposed controller\nis also validated via simulation on Tennessee Eastman Process~(TEP), a commonly\nused industrial process example.", "retrieval_score": 0.5575698614120483, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2309.00157v1_chunk_3", "chunk_text": "We validate the approach\nusing the Benchmark Tennessee Eastman while doing an ablation study of the\nmodel update parameters.", "retrieval_score": 0.5541948080062866, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Underflow → Component A Reactor Feed"}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.5530329346656799, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_1917_chunk_3", "chunk_text": "Stadium\nAve, West Lafayette, 47907, IN, USA\nAbstract\nMachine learning algorithms are increasingly being applied to fault detection\nand diagnosis (FDD) in chemical processes. However, existing data-driven\nFDD platforms often lack interpretability for process operators and strug-\ngle to identify root causes of previously unseen faults. This paper presents\nFaultExplainer , an interactive tool designed to improve fault detection, diag-\nnosis, and explanation in the Tennessee Eastman Process (TEP).", "retrieval_score": 0.5485936999320984, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Underflow → Component A Reactor Feed"}, {"chunk_id": "2103.05025v1_chunk_1", "chunk_text": "The variations in feedstock characteristics such as moisture and particle\nsize distribution lead to an inconsistent flow of feedstock from the biomass\npre-processing system to the reactor in-feed system. These inconsistencies\nresult in low on-stream times at the reactor in-feed equipment. This research\ndevelops an optimal process control method for a biomass pre-processing system\ncomprised of milling and densification operations to provide the consistent\nflow of feedstock to a reactor's throat.", "retrieval_score": 0.5462698340415955, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow → Component A Reactor Feed"}, {"chunk_id": "1709.02232v1_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5453324317932129, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow - Component A Reactor Feed"}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5453323125839233, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Underflow → Component A Reactor Feed"}, {"chunk_id": "2103.05025v1_chunk_0", "chunk_text": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate", "retrieval_score": 0.5447878837585449, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow - Component A Reactor Feed"}, {"chunk_id": "2106.02378v1_chunk_3", "chunk_text": "We illustrate and evaluate our\napproach using the Tennessee-Eastman process. We also compare our approach with\nthe baseline monitoring approaches proposed in previous work and assess its\nefficiency and scalability. Our evaluation results demonstrate that our\napproach can predict in a timely manner if a false data injection attack will\nbe able to cause damage, while remaining undetected. Thus, our approach can be\nused to provide operators with real-time early warnings about stealthy attacks.", "retrieval_score": 0.5423059463500977, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2308.11247v2_chunk_0", "chunk_text": "Benchmarking Domain Adaptation for Chemical Processes on the Tennessee\nEastman Process", "retrieval_score": 0.540565013885498, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Underflow → Component A Reactor Feed"}, {"chunk_id": "pdf_9406_chunk_5", "chunk_text": "The performance of the method is demonstrated\nand contrasted to (dynamic) principal component analysis, which is widely applied in the industry,\nin the benchmark Tennessee Eastman process (TEP) and a real chemical manufacturing dataset. Keywords: fault detection, fault identi\fcation, recurrent neural networks, variational dropout,\nBayesian inference, Tennessee Eastman process. 1.", "retrieval_score": 0.5404189825057983, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow → Component A Reactor Feed"}], "original_retrieval_results": 15}