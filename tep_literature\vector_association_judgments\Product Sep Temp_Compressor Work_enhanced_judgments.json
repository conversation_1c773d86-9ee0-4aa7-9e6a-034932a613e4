{"variable_pair": ["Product Sep Temp", "Compressor Work"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Product Sep Temp → Compressor Work", "association_votes": {"A": 5, "?": 1, "IA": 8, "⊥": 1}, "direction_votes": {"Product Sep Temp → Compressor Work": 10, "Product Sep Temp - Compressor Work": 3}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.6333482265472412, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Product Sep Temp → Compressor Work"}, {"chunk_id": "pdf_3170_chunk_2", "chunk_text": "An experimental setup was developed for the measurement of the\nthermoelectric power (TEP, Seebeck coeﬃcient) in the temperatu re range from 2\nto 350 K and magnetic ﬁelds up to 140 kOe. The system was built to ﬁt in a\ncommercial cryostat and is versatile, accurate and automated; u sing two heaters and\ntwo thermometers increases the accuracy of the TEP measureme nt.", "retrieval_score": 0.5871948003768921, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "1307.0249v1_chunk_1", "chunk_text": "We have simultaneously measured conductance and thermoelectric power (TEP) of\nindividual silicon and germanium/silicon core/shell nanowires in the field\neffect transistor device configuration. As the applied gate voltage changes,\nthe TEP shows distinctly different behaviors while the electrical conductance\nexhibits the turn-off, subthreshold, and saturation regimes respectively. At\nroom temperature, peak TEP value of $\\sim 300 \\mu$V/K is observed in the\nsubthreshold regime of the Si devices.", "retrieval_score": 0.5850880146026611, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Product Sep Temp - Compressor Work"}, {"chunk_id": "pdf_9936_chunk_3", "chunk_text": "The purpos e of this study is to find a series of alloys promptly forming \nmetal hydrides (MH) with suitable properties in  order to build a MH-based hydrogen compressor, \nworking in the same way between 20 and ~100 oC.", "retrieval_score": 0.5849184989929199, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp → Compressor Work"}, {"chunk_id": "1003.0921v1_chunk_1", "chunk_text": "An experimental setup was developed for the measurement of the thermoelectric\npower (TEP, Seebeck coefficient) in the temperature range from 2 to 350 K and\nmagnetic fields up to 140 kOe. The system was built to fit in a commercial\ncryostat and is versatile, accurate and automated; using two heaters and two\nthermometers increases the accuracy of the TEP measurement.", "retrieval_score": 0.5822750329971313, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp - Compressor Work"}, {"chunk_id": "pdf_2572_chunk_3", "chunk_text": "We apply our proposed compression\nmethod on the Tennessee Eastman Dataset, where we perform\nfault classification using the compressed data in two settings:\na fully supervised one and in a semi supervised, contrastive\nlearning setting. Both times, we were able to outperform real\nvalued counterparts as well as two baseline models: one with\nthe uncompressed time-series as the input and the other with a\nregular downsampling using the mean.", "retrieval_score": 0.5808975696563721, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp - Compressor Work"}, {"chunk_id": "2403.11722v2_chunk_3", "chunk_text": "We\napply our proposed compression method on the Tennessee Eastman Dataset, where\nwe perform fault classification using the compressed data in two settings: a\nfully supervised one and in a semi supervised, contrastive learning setting. Both times, we were able to outperform real valued counterparts as well as two\nbaseline models: one with the uncompressed time-series as the input and the\nother with a regular downsampling using the mean.", "retrieval_score": 0.5808975696563721, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Product Sep Temp → Compressor Work"}, {"chunk_id": "pdf_6959_chunk_5", "chunk_text": "Simulation results demonstrate the efficacy of this approach, showcasing superior performance in \naccuracy, false alarm rate, and misclassifica tion rate compared to existing methods. This \nmethodology provides a robust and interpretable solution for fault detection and diagnosis in the \nTEP, highlighting its potential for industrial applications. Keywords. Fault Detection, Tennessee Eastman Process, Supervised Feature Selection, Attention \nMechanism, Bidirectional Long Short -Term Memory.", "retrieval_score": 0.5807262659072876, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp → Compressor Work"}, {"chunk_id": "2308.11247v2_chunk_0", "chunk_text": "Benchmarking Domain Adaptation for Chemical Processes on the Tennessee\nEastman Process", "retrieval_score": 0.5802551507949829, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "1207.3541v1_chunk_2", "chunk_text": "The purpose of this study\nis to find a series of alloys promptly forming metal hydrides (MH) with\nsuitable properties in order to build a MH-based hydrogen compressor, working\nin the same way between 20 and ~100 oC.", "retrieval_score": 0.5782213807106018, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Product Sep Temp → Compressor Work"}, {"chunk_id": "2301.11573v3_chunk_3", "chunk_text": "The theoretical analysis is verified through\nMonte Carlo simulations and Tennessee Eastman process (TEP) dataset.", "retrieval_score": 0.5768629908561707, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp → Compressor Work"}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.5767700672149658, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Product Sep Temp → Compressor Work"}, {"chunk_id": "0104454v1_chunk_1", "chunk_text": "We have measured temperature dependence of thermoelectric power (TEP) on\nMgB$_2$ superconductor under hydrostatic pressure. The sign and temperature\ndependence of TEP shows metallic hole carriers are dominant with activation\ntype contribution at higher temperature. TEP increases with pressure while\n$T_c$ decrease with ratio -0.136 K/kbar. The data are discussed in\nconsideration of carriers from different bands and anisotropy of\ncompressibility.", "retrieval_score": 0.5763736963272095, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp → Compressor Work"}, {"chunk_id": "2506.08057v1_chunk_1", "chunk_text": "Accurate characterization of temperature-dependent thermoelectric properties\n(TEPs), such as thermal conductivity and the Seebeck coefficient, is essential\nfor reliable modeling and efficient design of thermoelectric devices. However,\ntheir nonlinear temperature dependence and coupled transport behavior make both\nforward simulation and inverse identification difficult, particularly under\nsparse measurement conditions.", "retrieval_score": 0.5754492282867432, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp → Compressor Work"}, {"chunk_id": "1911.04386v2_chunk_4", "chunk_text": "The\noutstanding performance of this method is demonstrated and contrasted to\n(dynamic) principal component analysis, which are widely applied in the\nindustry, in the benchmark Tennessee Eastman process~(TEP) and a real chemical\nmanufacturing dataset.", "retrieval_score": 0.5718581676483154, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Product Sep Temp → Compressor Work"}], "original_retrieval_results": 15}