{"variable_pair": ["Recycle Flow", "Stripper Level"], "results": [{"chunk": {"chunk_id": "pdf_5616_chunk_5", "paper_id": "pdf_5616", "paper_title": "Typeset using L ATEXtwocolumn style in AASTeX63", "chunk_text": "Our models show general agreement with 1D static calculations,\nsuggesting a limited role of recycling flows in determining envelope structure. By adopting a dimen-\nsionless framework, these models can be applied to a wide range of formation conditions and assumed\nopacities. In particular,.", "found_variables": ["work", "flow"], "variable_count": 2, "chunk_length": 290}, "score": 0.5356788635253906, "query": "Recycle Flow and Stripper Level"}, {"chunk": {"chunk_id": "pdf_8878_chunk_1", "paper_id": "pdf_8878", "paper_title": "Typeset using L ATEXtwocolumn style in AASTeX631", "chunk_text": "Typeset using L ATEXtwocolumn style in AASTeX631\nGrowing Planet Envelopes in Spite of Recycling Flows\nAvery P.", "found_variables": ["flow"], "variable_count": 1, "chunk_length": 110}, "score": 0.5271065831184387, "query": "Recycle Flow and Stripper Level"}, {"chunk": {"chunk_id": "pdf_1839_chunk_4", "paper_id": "pdf_1839", "paper_title": "Draft version July 10, 2024", "chunk_text": "Here, we reevaluate this line of reasoning by incorporating recycling flows of gas\ninto a numerical one-dimensional thermodynamic model with more realistic equation of state and\nopacities and the thermal state of the advective flow. At 0. 1 AU, we find that advective flows are\nonly able to produce mini-Neptunes if they can penetrate below ∼0. 25 of the planet’s gravitational\nsphere of influence.", "found_variables": ["rate", "flow", "thermal"], "variable_count": 3, "chunk_length": 398}, "score": 0.520667552947998, "query": "Recycle Flow and Stripper Level"}, {"chunk": {"chunk_id": "2406.09186v2_chunk_0", "paper_id": "2406.09186v2", "paper_title": "A formation pathway for terrestrial planets with moderate water content\ninvolving atmospheric-volatile recycling", "chunk_text": "A formation pathway for terrestrial planets with moderate water content\ninvolving atmospheric-volatile recycling", "found_variables": ["rate"], "variable_count": 1, "chunk_length": 112}, "score": 0.5166196823120117, "query": "Recycle Flow and Stripper Level"}, {"chunk": {"chunk_id": "2310.03117v1_chunk_0", "paper_id": "2310.03117v1", "paper_title": "Growing Planet Envelopes in Spite of Recycling Flows", "chunk_text": "Growing Planet Envelopes in Spite of Recycling Flows", "found_variables": ["flow"], "variable_count": 1, "chunk_length": 52}, "score": 0.5149089097976685, "query": "Recycle Flow and Stripper Level"}, {"chunk": {"chunk_id": "2106.12003v2_chunk_2", "paper_id": "2106.12003v2", "paper_title": "Global 3D Radiation Hydrodynamic Simulations of Proto-Jupiter's\nConvective Envelope", "chunk_text": "The\nenvelopes are heated at given rates to achieve steady states, enabling\ncomparisons with 1D models. We vary envelope properties to obtain both\nradiative and convective solutions. Using a passive scalar, we observe\nsignificant mass recycling on the orbital timescale. For a radiative envelope,\nrecycling can only penetrate from the disc surface until $\\sim$0. 1-0.", "found_variables": ["rate", "heat"], "variable_count": 2, "chunk_length": 366}, "score": 0.****************, "query": "Recycle Flow and Stripper Level"}, {"chunk": {"chunk_id": "pdf_4923_chunk_2", "paper_id": "pdf_4923", "paper_title": "Bayesian Sparsiﬁcation of Recurrent Neural Networks", "chunk_text": "We\napply this technique to sparsify recurrent neu-\nral networks. To account for recurrent speciﬁcs\nwe also rely on Binary Variational Dropout for\nRNN (Gal & Gha<PERSON>, 2016b). We report\n99. 5% sparsity level on sentiment analysis task\nwithout a quality drop and up to 87% sparsity\nlevel on language modeling task with slight loss\nof accuracy. 1.", "found_variables": ["work", "level"], "variable_count": 2, "chunk_length": 346}, "score": 0.****************, "query": "Recycle Flow and Stripper Level"}, {"chunk": {"chunk_id": "2310.03117v1_chunk_3", "paper_id": "2310.03117v1", "paper_title": "Growing Planet Envelopes in Spite of Recycling Flows", "chunk_text": "We incorporate\nthese 3D hydrodynamic effects into an extensible 1D framework with a physically\nmotivated three-layer recycling parameterization. Specializing to the case of\nJupiter, recycling produces minimal changes to the growth rate with the planet\nstill entering runaway accretion and becoming a gas giant in ~1 Myr. Even in\nthe inner disk (0. 1 AU), our 1D models suggest that recycling is not so robust\nand ubiquitous as to stop all cores from becoming giants.", "found_variables": ["work", "rate"], "variable_count": 2, "chunk_length": 466}, "score": 0.5097551345825195, "query": "Recycle Flow and Stripper Level"}, {"chunk": {"chunk_id": "pdf_9657_chunk_0", "paper_id": "pdf_9657", "paper_title": "Transmission Expansion Planning Using Cycle Flows", "chunk_text": "Transmission Expansion Planning Using Cycle Flows", "found_variables": ["flow"], "variable_count": 1, "chunk_length": 49}, "score": 0.508765697479248, "query": "Recycle Flow and Stripper Level"}, {"chunk": {"chunk_id": "2004.08702v1_chunk_0", "paper_id": "2004.08702v1", "paper_title": "Transmission Expansion Planning Using Cycle Flows", "chunk_text": "Transmission Expansion Planning Using Cycle Flows", "found_variables": ["flow"], "variable_count": 1, "chunk_length": 49}, "score": 0.508765697479248, "query": "Recycle Flow and Stripper Level"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}