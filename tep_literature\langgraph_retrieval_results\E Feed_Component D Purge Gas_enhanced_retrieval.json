{"variable_pair": ["<PERSON>ed", "Component D Purge Gas"], "results": [{"chunk": {"chunk_id": "pdf_3282_chunk_0", "paper_id": "pdf_3282", "paper_title": "Geometric Decomposition of Feed Forward", "chunk_text": "Geometric Decomposition of Feed Forward", "found_variables": ["feed", "composition"], "variable_count": 2, "chunk_length": 39}, "score": 0.4947539269924164, "query": "E Feed and Component D Purge Gas"}, {"chunk": {"chunk_id": "pdf_6995_chunk_0", "paper_id": "pdf_6995", "paper_title": "Enhanced Fault Detection and Cause Identification Using ", "chunk_text": "Enhanced Fault Detection and Cause Identification Using", "found_variables": ["fault detection"], "variable_count": 1, "chunk_length": 55}, "score": 0.494234561920166, "query": "E Feed and Component D Purge Gas"}, {"chunk": {"chunk_id": "2101.06993v1_chunk_0", "paper_id": "2101.06993v1", "paper_title": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "found_variables": ["chemical process", "work", "compression", "press", "fault detection"], "variable_count": 5, "chunk_length": 95}, "score": 0.4936563968658447, "query": "E Feed and Component D Purge Gas"}, {"chunk": {"chunk_id": "pdf_2886_chunk_0", "paper_id": "pdf_2886", "paper_title": "Machine Learned Interatomic Potential for Dispersion Strengthened Plasma Facing Components", "chunk_text": "Machine Learned Interatomic Potential for Dispersion Strengthened Plasma Facing Components", "found_variables": ["component"], "variable_count": 1, "chunk_length": 90}, "score": 0.4902838170528412, "query": "E Feed and Component D Purge Gas"}, {"chunk": {"chunk_id": "2212.01432v1_chunk_0", "paper_id": "2212.01432v1", "paper_title": "Machine Learned Interatomic Potential for Dispersion Strengthened Plasma\nFacing Components", "chunk_text": "Machine Learned Interatomic Potential for Dispersion Strengthened Plasma\nFacing Components", "found_variables": ["component"], "variable_count": 1, "chunk_length": 90}, "score": 0.4902838170528412, "query": "E Feed and Component D Purge Gas"}, {"chunk": {"chunk_id": "1712.04118v1_chunk_0", "paper_id": "1712.04118v1", "paper_title": "Neural Component Analysis for Fault Detection", "chunk_text": "Neural Component Analysis for Fault Detection", "found_variables": ["component", "fault detection"], "variable_count": 2, "chunk_length": 45}, "score": 0.4866010248661041, "query": "E Feed and Component D Purge Gas"}, {"chunk": {"chunk_id": "1807.03623v1_chunk_0", "paper_id": "1807.03623v1", "paper_title": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "chunk_text": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "found_variables": ["temperature", "reactor", "temp"], "variable_count": 3, "chunk_length": 68}, "score": 0.48648667335510254, "query": "E Feed and Component D Purge Gas"}, {"chunk": {"chunk_id": "2103.07303v1_chunk_0", "paper_id": "2103.07303v1", "paper_title": "Second-Order Component Analysis for Fault Detection", "chunk_text": "Second-Order Component Analysis for Fault Detection", "found_variables": ["component", "fault detection"], "variable_count": 2, "chunk_length": 51}, "score": 0.4848957061767578, "query": "E Feed and Component D Purge Gas"}, {"chunk": {"chunk_id": "pdf_3777_chunk_0", "paper_id": "pdf_3777", "paper_title": "Deep Compression of Neural Networks for Fault", "chunk_text": "Deep Compression of Neural Networks for Fault", "found_variables": ["press", "work", "compression"], "variable_count": 3, "chunk_length": 45}, "score": 0.4782874584197998, "query": "E Feed and Component D Purge Gas"}, {"chunk": {"chunk_id": "pdf_2902_chunk_0", "paper_id": "pdf_2902", "paper_title": "A N OVEL DEEPPARALLEL TIME-SERIES RELATION NETWORK", "chunk_text": "A N OVEL DEEPPARALLEL TIME-SERIES RELATION NETWORK", "found_variables": ["work"], "variable_count": 1, "chunk_length": 50}, "score": 0.4737299382686615, "query": "E Feed Component D Purge Gas interaction"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}