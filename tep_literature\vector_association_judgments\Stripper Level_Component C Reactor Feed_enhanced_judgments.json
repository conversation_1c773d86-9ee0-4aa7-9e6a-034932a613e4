{"variable_pair": ["Stripper Level", "Component C Reactor Feed"], "aggregation": {"has_causal_relationship": true, "final_association": "?", "final_direction": "Stripper Level → Component C Reactor Feed", "association_votes": {"IA": 5, "A": 2, "?": 7, "⊥": 1}, "direction_votes": {"Stripper Level → Component C Reactor Feed": 7}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "pdf_819_chunk_0", "chunk_text": "Geometric Decomposition of Feed Forward", "retrieval_score": 0.5577985644340515, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Level → Component C Reactor Feed"}, {"chunk_id": "0402435v1_chunk_0", "chunk_text": "The Indo-U.S. Library of Coude Feed Stellar Spectra", "retrieval_score": 0.5429730415344238, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_1428_chunk_0", "chunk_text": "Development of a Feeding Assistive Robot Using a Six Degree of", "retrieval_score": 0.5354562997817993, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_6635_chunk_3", "chunk_text": "The\nframework is illustrated via a computational model of the deposition of Cu\nin a Chemical Vapor Deposition (CVD) reactor, where the reactor pressure,\nthe deposition temperature and feed mass \row rate are important process\nparameters that determine the outcome of the process. The sampled ob-\nservations are high-dimensional vectors containing the outputs of a detailed\nCFD steady-state model of the process, i. e.", "retrieval_score": 0.5169769525527954, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "1703.08935v1_chunk_0", "chunk_text": "Security Constrained Multi-Stage Transmission Expansion Planning\nConsidering a Continuously Variable Series Reactor", "retrieval_score": 0.5168575048446655, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Level → Component C Reactor Feed"}, {"chunk_id": "pdf_2177_chunk_2", "chunk_text": "Eksioglub, <PERSON>ushi <PERSON>c,\naEnergy and Environment Science & Technology Department, Idaho National Laboratory, Idaho Falls, ID\nbIndustrial Engineering Department, University of Arkansas, Fayetteville, AR\ncGlenn Department of Civil Engineering, Clemson University, Clemson, SC\nAbstract\nThe variations in feedstock characteristics such as moisture and particle size distribution\nlead to an inconsistent ﬂow of feedstock from the biomass pre-processing system to the reactor\nin-feed system.", "retrieval_score": 0.5156000852584839, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_7578_chunk_0", "chunk_text": "Correlation between radiation damage and magnetic properties in reactor vessel", "retrieval_score": 0.5140206217765808, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2309.11594v1_chunk_0", "chunk_text": "Development of a Feeding Assistive Robot Using a Six Degree of Freedom\nRobotic Arm", "retrieval_score": 0.511299729347229, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5095429420471191, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Level → Component C Reactor Feed"}, {"chunk_id": "pdf_2859_chunk_0", "chunk_text": "Feed-forward neural network unfolding", "retrieval_score": 0.5079245567321777, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2112.08180v1_chunk_0", "chunk_text": "Feed-forward neural network unfolding", "retrieval_score": 0.5079245567321777, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Level → Component C Reactor Feed"}, {"chunk_id": "2103.05025v1_chunk_0", "chunk_text": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate", "retrieval_score": 0.5053985714912415, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Level → Component C Reactor Feed"}, {"chunk_id": "1612.02522v1_chunk_0", "chunk_text": "Geometric Decomposition of Feed Forward Neural Networks", "retrieval_score": 0.5027574300765991, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Level → Component C Reactor Feed"}, {"chunk_id": "pdf_2948_chunk_2", "chunk_text": "August 31, 2021\nAbstract\nIn this paper, genetic programming reinforcement learning (GPRL) is\nutilized to generate human-interpretable control policies for a Chylla-\nHaase polymerization reactor. Such continuously stirred tank reactors\n(CSTRs) with jacket cooling are widely used in the chemical industry,\nin the production of \fne chemicals, pigments, polymers, and medi-\ncal products.", "retrieval_score": 0.5020378828048706, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2108.13381v1_chunk_1", "chunk_text": "In this paper, genetic programming reinforcement learning (GPRL) is utilized\nto generate human-interpretable control policies for a Chylla-Haase\npolymerization reactor. Such continuously stirred tank reactors (CSTRs) with\njacket cooling are widely used in the chemical industry, in the production of\nfine chemicals, pigments, polymers, and medical products. Despite appearing\nrather simple, controlling CSTRs in real-world applications is quite a\nchallenging problem to tackle.", "retrieval_score": 0.4971173107624054, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Level → Component C Reactor Feed"}], "original_retrieval_results": 15}