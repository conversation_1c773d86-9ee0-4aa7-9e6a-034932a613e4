{"variable_pair": ["Reactor Level", "Stripper Underflow"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Reactor Level → Stripper Underflow", "association_votes": {"A": 4, "?": 2, "IA": 9}, "direction_votes": {"Reactor Level - Stripper Underflow": 1, "Stripper Underflow → Reactor Level": 2, "Reactor Level → Stripper Underflow": 10}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5898877382278442, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow → Reactor Level"}, {"chunk_id": "1807.03623v1_chunk_0", "chunk_text": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "retrieval_score": 0.5779989957809448, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Level - Stripper Underflow"}, {"chunk_id": "pdf_1917_chunk_3", "chunk_text": "Stadium\nAve, West Lafayette, 47907, IN, USA\nAbstract\nMachine learning algorithms are increasingly being applied to fault detection\nand diagnosis (FDD) in chemical processes. However, existing data-driven\nFDD platforms often lack interpretability for process operators and strug-\ngle to identify root causes of previously unseen faults. This paper presents\nFaultExplainer , an interactive tool designed to improve fault detection, diag-\nnosis, and explanation in the Tennessee Eastman Process (TEP).", "retrieval_score": 0.5702641010284424, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Level → Stripper Underflow"}, {"chunk_id": "2308.11247v2_chunk_0", "chunk_text": "Benchmarking Domain Adaptation for Chemical Processes on the Tennessee\nEastman Process", "retrieval_score": 0.5650584697723389, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Level → Stripper Underflow"}, {"chunk_id": "pdf_7856_chunk_5", "chunk_text": "In the second stage, a reconstruction error-based threshold approach using the trained\nGAWNO is employed to detect and isolate faults based on the discrepancy values. We validate the\nproposed approach using the Tennessee Eastman Process (TEP) dataset and Avedore wastewater\ntreatment plant (WWTP) and N2Oemissions nam.", "retrieval_score": 0.5626987218856812, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5568940043449402, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Level → Stripper Underflow"}, {"chunk_id": "1709.02232v1_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5568940043449402, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow → Reactor Level"}, {"chunk_id": "2401.04004v1_chunk_3", "chunk_text": "In the second stage, a reconstruction error-based threshold\napproach using the trained GAWNO is employed to detect and isolate faults based\non the discrepancy values. We validate the proposed approach using the\nTennessee Eastman Process (TEP) dataset and Avedore wastewater treatment plant\n(WWTP) and N2O emissions named as WWTPN2O datasets.", "retrieval_score": 0.554188072681427, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Level → Stripper Underflow"}, {"chunk_id": "pdf_131_chunk_0", "chunk_text": "arXiv:2303.05904v1  [cs.LG]  10 Mar 2023Deep Anomaly Detection on Tennessee Eastman Process Data", "retrieval_score": 0.5539255142211914, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Level → Stripper Underflow"}, {"chunk_id": "pdf_8399_chunk_4", "chunk_text": "The\tmechanisms\tdriving\ttheir\tformation\tare\tstill\tdebated. This\twork\tfocuses\ton\tlow-Cu\treactor\t pressure\t vessel\t (RPV)\tsteels,\t where\tsolute\t species\t are\tgenerally\t not\texpected\tto\tprecipitate. We\trationalize\tthe\tprocesses\tthat\ttake\tplace\tat\tthe\tnanometre\tscale\tunder\tirradiation,\trelying\ton\tthe\tlatest\ttheoretical\tand\texperimental\tevidence\ton\tatomic-level\tdiffusion\tand\ttransport\tprocesses. These\tare\tcompiled\tin\ta\tnew\tmodel,\tbased\ton\tthe\tobject\tkinetic\tMonte\tCarlo\t(OKMC)\ttechnique.", "retrieval_score": 0.5492631196975708, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Level → Stripper Underflow"}, {"chunk_id": "2412.14492v1_chunk_1", "chunk_text": "Machine learning algorithms are increasingly being applied to fault detection\nand diagnosis (FDD) in chemical processes. However, existing data-driven FDD\nplatforms often lack interpretability for process operators and struggle to\nidentify root causes of previously unseen faults. This paper presents\nFaultExplainer, an interactive tool designed to improve fault detection,\ndiagnosis, and explanation in the Tennessee Eastman Process (TEP).", "retrieval_score": 0.5483918190002441, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Level → Stripper Underflow"}, {"chunk_id": "2403.10842v4_chunk_0", "chunk_text": "Twin Transformer using Gated Dynamic Learnable Attention mechanism for\nFault Detection and Diagnosis in the Tennessee Eastman Process", "retrieval_score": 0.5459496378898621, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Level → Stripper Underflow"}, {"chunk_id": "pdf_131_chunk_2", "chunk_text": "LG]  10 Mar 2023Deep Anomaly Detection on Tennessee Eastman Process Data\n<PERSON>,2,<PERSON>,<PERSON>,<PERSON>1,<PERSON>1,<PERSON> <PERSON><PERSON><PERSON>1,<PERSON>,<PERSON>1,<PERSON><PERSON>,<PERSON>4,<PERSON><PERSON>,<PERSON>5,<PERSON>5,<PERSON>6,<PERSON>,<PERSON>,<PERSON><PERSON><PERSON>,∗\n1Technische Universit¨ at Kaiserslautern, Germany\n2BASF SE, Gas Treatment Technology, Germany\n3Bosch AI, USA\n4Technische Universit¨ at Dortmund, Germany\n5ABB Corporate Research Center Ladenburg, Germany\n6University of California Irvine, USA\n7Fraunhofer ITWM, Germany\n8Technische Universit¨ at M¨ unchen, Germany\nEmail corresponding author: kloft@cs.", "retrieval_score": 0.5369598865509033, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Level → Stripper Underflow"}, {"chunk_id": "pdf_6635_chunk_5", "chunk_text": "g. temperature in the reactor) given other par-\ntial observations (e. g. mass fraction in the reactor). The proposed work\row\nrelies on the manifold learning schemes Di\u000busion Maps and the associated\nGeometric Harmonics. Di\u000busion Maps is used for discovering a reduced rep-\nresent.", "retrieval_score": 0.5333090424537659, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Level → Stripper Underflow"}, {"chunk_id": "pdf_7578_chunk_0", "chunk_text": "Correlation between radiation damage and magnetic properties in reactor vessel", "retrieval_score": 0.5313993692398071, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}], "original_retrieval_results": 15}