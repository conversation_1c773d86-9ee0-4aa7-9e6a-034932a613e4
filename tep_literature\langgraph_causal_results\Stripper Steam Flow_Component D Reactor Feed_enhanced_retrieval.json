{"variable_pair": ["Stripper Steam Flow", "Component D Reactor Feed"], "results": [{"chunk": {"chunk_id": "1807.03623v1_chunk_0", "paper_id": "1807.03623v1", "paper_title": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "chunk_text": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "found_variables": ["temperature", "temp", "reactor"], "variable_count": 3, "chunk_length": 68}, "score": 0.5674194097518921, "query": "Stripper Steam Flow and Component D Reactor Feed"}, {"chunk": {"chunk_id": "pdf_8178_chunk_2", "paper_id": "pdf_8178", "paper_title": "Optimal Control to Handle Variations in Biomass Feedstock", "chunk_text": "Eksioglub, <PERSON>ushi <PERSON>c,\naEnergy and Environment Science & Technology Department, Idaho National Laboratory, Idaho Falls, ID\nbIndustrial Engineering Department, University of Arkansas, Fayetteville, AR\ncGlenn Department of Civil Engineering, Clemson University, Clemson, SC\nAbstract\nThe variations in feedstock characteristics such as moisture and particle size distribution\nlead to an inconsistent ﬂow of feedstock from the biomass pre-processing system to the reactor\nin-feed system.", "found_variables": ["feed", "reactor"], "variable_count": 2, "chunk_length": 485}, "score": 0.5421099662780762, "query": "Stripper Steam Flow and Component D Reactor Feed"}, {"chunk": {"chunk_id": "0402435v1_chunk_0", "paper_id": "0402435v1", "paper_title": "The Indo-U.S. Library of Coude Feed Stellar Spectra", "chunk_text": "The Indo-U.S. Library of Coude Feed Stellar Spectra", "found_variables": ["<PERSON>ed", "feed"], "variable_count": 2, "chunk_length": 51}, "score": 0.5376443862915039, "query": "Stripper Steam Flow and Component D Reactor Feed"}, {"chunk": {"chunk_id": "pdf_729_chunk_0", "paper_id": "pdf_729", "paper_title": "Geometric Decomposition of Feed Forward", "chunk_text": "Geometric Decomposition of Feed Forward", "found_variables": ["composition", "feed"], "variable_count": 2, "chunk_length": 39}, "score": 0.5356093049049377, "query": "Stripper Steam Flow and Component D Reactor Feed"}, {"chunk": {"chunk_id": "pdf_7858_chunk_3", "paper_id": "pdf_7858", "paper_title": "From partial data to out-of-sample", "chunk_text": "The\nframework is illustrated via a computational model of the deposition of Cu\nin a Chemical Vapor Deposition (CVD) reactor, where the reactor pressure,\nthe deposition temperature and feed mass \row rate are important process\nparameters that determine the outcome of the process. The sampled ob-\nservations are high-dimensional vectors containing the outputs of a detailed\nCFD steady-state model of the process, i. e.", "found_variables": ["temperature", "pressure", "feed", "rate", "temp", "press", "reactor", "work", "Reactor Pressure", "<PERSON> Feed"], "variable_count": 10, "chunk_length": 416}, "score": 0.5292391777038574, "query": "Stripper Steam Flow and Component D Reactor Feed"}, {"chunk": {"chunk_id": "1703.08935v1_chunk_0", "paper_id": "1703.08935v1", "paper_title": "Security Constrained Multi-Stage Transmission Expansion Planning\nConsidering a Continuously Variable Series Reactor", "chunk_text": "Security Constrained Multi-Stage Transmission Expansion Planning\nConsidering a Continuously Variable Series Reactor", "found_variables": ["reactor"], "variable_count": 1, "chunk_length": 115}, "score": 0.5108636617660522, "query": "Stripper Steam Flow and Component D Reactor Feed"}, {"chunk": {"chunk_id": "2301.11728v1_chunk_3", "paper_id": "2301.11728v1", "paper_title": "From partial data to out-of-sample parameter and observation estimation\nwith Diffusion Maps and Geometric Harmonics", "chunk_text": "inlet\ntemperature); (b) process parameters given observation data; and (c) partial\nobservations (e. g. temperature in the reactor) given other partial observations\n(e. g. mass fraction in the reactor). The proposed workflow relies on the\nmanifold learning schemes Diffusion Maps and the associated Geometric\nHarmonics. Diffusion Maps is used for discovering a reduced representation of\nthe available data, and Geometric Harmonics for extending functions defined on\nthe manifold.", "found_variables": ["temperature", "temp", "reactor", "inlet", "work", "flow"], "variable_count": 6, "chunk_length": 478}, "score": 0.5076801776885986, "query": "Stripper Steam Flow and Component D Reactor Feed"}, {"chunk": {"chunk_id": "2108.09224v1_chunk_0", "paper_id": "2108.09224v1", "paper_title": "Characterizing accelerated precipitation in proton irradiated steel", "chunk_text": "Characterizing accelerated precipitation in proton irradiated steel", "found_variables": ["rate"], "variable_count": 1, "chunk_length": 67}, "score": 0.5059090256690979, "query": "Stripper Steam Flow and Component D Reactor Feed"}, {"chunk": {"chunk_id": "2103.05025v1_chunk_0", "paper_id": "2103.05025v1", "paper_title": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate", "chunk_text": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate", "found_variables": ["feed", "rate", "reactor"], "variable_count": 3, "chunk_length": 98}, "score": 0.5046533942222595, "query": "Stripper Steam Flow and Component D Reactor Feed"}, {"chunk": {"chunk_id": "pdf_2841_chunk_1", "paper_id": "pdf_2841", "paper_title": "Adaptive Model Predictive Control of a Batch ", "chunk_text": "Abstract:  A sequential trajectory linearized adaptive model based predictive controller is designed \nusing the DMC algorithm to control the temperature of a batch MMA polymerization process. Using the mechanistic model of the polymerization, a parametric transfer function is derived to \nrelate the reactor temperature to the power of the heaters. Then, a multiple model predictive control approach is taken in to track a desired temperature trajec tory.", "found_variables": ["temperature", "heat", "Reactor Temperature", "temp", "reactor"], "variable_count": 5, "chunk_length": 455}, "score": 0.5040271282196045, "query": "Stripper Steam Flow and Component D Reactor Feed"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}