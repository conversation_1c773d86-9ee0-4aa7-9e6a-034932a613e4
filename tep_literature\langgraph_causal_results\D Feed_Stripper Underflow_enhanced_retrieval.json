{"variable_pair": ["<PERSON> Feed", "Stripper Underflow"], "results": [{"chunk": {"chunk_id": "pdf_6655_chunk_0", "paper_id": "pdf_6655", "paper_title": "Feed-forward neural network unfolding", "chunk_text": "Feed-forward neural network unfolding", "found_variables": ["work", "feed"], "variable_count": 2, "chunk_length": 37}, "score": 0.5757427215576172, "query": "D Feed and Stripper Underflow"}, {"chunk": {"chunk_id": "2112.08180v1_chunk_0", "paper_id": "2112.08180v1", "paper_title": "Feed-forward neural network unfolding", "chunk_text": "Feed-forward neural network unfolding", "found_variables": ["work", "feed"], "variable_count": 2, "chunk_length": 37}, "score": 0.5757427215576172, "query": "D Feed and Stripper Underflow"}, {"chunk": {"chunk_id": "pdf_729_chunk_0", "paper_id": "pdf_729", "paper_title": "Geometric Decomposition of Feed Forward", "chunk_text": "Geometric Decomposition of Feed Forward", "found_variables": ["composition", "feed"], "variable_count": 2, "chunk_length": 39}, "score": 0.5706335306167603, "query": "D Feed and Stripper Underflow"}, {"chunk": {"chunk_id": "1712.01580v1_chunk_0", "paper_id": "1712.01580v1", "paper_title": "The Lifting Bifurcation Problem on Feed-Forward Networks", "chunk_text": "The Lifting Bifurcation Problem on Feed-Forward Networks", "found_variables": ["work", "feed"], "variable_count": 2, "chunk_length": 56}, "score": 0.5235461592674255, "query": "D Feed and Stripper Underflow"}, {"chunk": {"chunk_id": "1509.03282v1_chunk_2", "paper_id": "1509.03282v1", "paper_title": "A Remark on the Second Neighborhood Problem", "chunk_text": "In the case of\ndigraphs missing a matching, we exhibit a feed vertex with the SNP by refining\na proof due to <PERSON><PERSON> and <PERSON><PERSON> and using good digraphs. Moreover, in some\ncases we exhibit two vertices with SNP.", "found_variables": ["A Feed", "feed"], "variable_count": 2, "chunk_length": 209}, "score": 0.5133827924728394, "query": "D Feed Stripper Underflow interaction"}, {"chunk": {"chunk_id": "pdf_3147_chunk_0", "paper_id": "pdf_3147", "paper_title": "On the space of coefﬁcients of a Feed Forward", "chunk_text": "On the space of coefﬁcients of a Feed Forward", "found_variables": ["A Feed", "feed"], "variable_count": 2, "chunk_length": 45}, "score": 0.5081020593643188, "query": "D Feed and Stripper Underflow"}, {"chunk": {"chunk_id": "pdf_4734_chunk_3", "paper_id": "pdf_4734", "paper_title": "SHAP-EDITOR : Instruction-guided Latent 3D Editing in Seconds", "chunk_text": "Distillation\nnecessitates at least tens of minutes per asset to attain sat-\nisfactory editing results, and is thus not very practical. In\ncontrast, we ask whether 3D editing can be carried out di-\nrectly by a feed-forward network, eschewing test-time op-\ntimization. In particular, we hypothesise that editing can\nbe greatly simplified by first encoding 3D objects in a suit-\nable latent space. We validate this hypothesis by building\nupon the latent space of Shap-E.", "found_variables": ["work", "distillation", "A Feed", "feed"], "variable_count": 4, "chunk_length": 467}, "score": 0.507015585899353, "query": "D Feed and Stripper Underflow"}, {"chunk": {"chunk_id": "pdf_5329_chunk_2", "paper_id": "pdf_5329", "paper_title": "arXiv:1911.07256v1  [cs.IT]  17 Nov 2019i", "chunk_text": "This predictor provides an in itialization (weight matrices, biases and\nactivation function) to a feed-forward neural network base d predictor. With a properly learned neural\nnetwork, we show that under the given channel model assumpti ons it is possible to easily outperform\nthe LMMSE predictor based on the <PERSON><PERSON> assumption of the unde rlying Doppler spectrum. Index Terms\nTime-variant channel state information, minimum mean squa red error prediction, machine learning,\nneural networks\nI.", "found_variables": ["work", "A Feed", "feed"], "variable_count": 3, "chunk_length": 491}, "score": 0.5069825649261475, "query": "correlation D Feed Stripper Underflow"}, {"chunk": {"chunk_id": "pdf_7213_chunk_0", "paper_id": "pdf_7213", "paper_title": "Deep Compression of Neural Networks for Fault", "chunk_text": "Deep Compression of Neural Networks for Fault", "found_variables": ["work", "compression", "press"], "variable_count": 3, "chunk_length": 45}, "score": 0.5043030381202698, "query": "D Feed and Stripper Underflow"}, {"chunk": {"chunk_id": "2312.09246v1_chunk_1", "paper_id": "2312.09246v1", "paper_title": "SHAP-EDITOR: Instruction-guided Latent 3D Editing in Seconds", "chunk_text": "We propose a novel feed-forward 3D editing framework called Shap-Editor. Prior research on editing 3D objects primarily concentrated on editing\nindividual objects by leveraging off-the-shelf 2D image editing networks. This\nis achieved via a process called distillation, which transfers knowledge from\nthe 2D network to 3D assets. Distillation necessitates at least tens of minutes\nper asset to attain satisfactory editing results, and is thus not very\npractical.", "found_variables": ["distillation", "rate", "work", "feed"], "variable_count": 4, "chunk_length": 462}, "score": 0.5026895999908447, "query": "D Feed and Stripper Underflow"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}