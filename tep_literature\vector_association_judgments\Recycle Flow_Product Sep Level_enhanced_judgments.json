{"variable_pair": ["Recycle Flow", "Product Sep Level"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Recycle Flow → Product Sep Level", "association_votes": {"A": 2, "?": 4, "IA": 8, "⊥": 1}, "direction_votes": {"Recycle Flow → Product Sep Level": 8, "Recycle Flow - Product Sep Level": 2}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "2308.11247v2_chunk_0", "chunk_text": "Benchmarking Domain Adaptation for Chemical Processes on the Tennessee\nEastman Process", "retrieval_score": 0.5612607598304749, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Product Sep Level"}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.5611715912818909, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Product Sep Level"}, {"chunk_id": "pdf_6959_chunk_5", "chunk_text": "Simulation results demonstrate the efficacy of this approach, showcasing superior performance in \naccuracy, false alarm rate, and misclassifica tion rate compared to existing methods. This \nmethodology provides a robust and interpretable solution for fault detection and diagnosis in the \nTEP, highlighting its potential for industrial applications. Keywords. Fault Detection, Tennessee Eastman Process, Supervised Feature Selection, Attention \nMechanism, Bidirectional Long Short -Term Memory.", "retrieval_score": 0.5539952516555786, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Product Sep Level"}, {"chunk_id": "pdf_9787_chunk_6", "chunk_text": "Experimental validation on both the Tennessee Eastman \nProcess (TEP) and the Secure Water Treatment (SWaT) \ntestbeds demonstrates the framework’s superior performan ce. A comprehensive ablation study, supported by extensive visualizations including Receiver Operatin.", "retrieval_score": 0.5506362915039062, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow - Product Sep Level"}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5481787919998169, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_9406_chunk_5", "chunk_text": "The performance of the method is demonstrated\nand contrasted to (dynamic) principal component analysis, which is widely applied in the industry,\nin the benchmark Tennessee Eastman process (TEP) and a real chemical manufacturing dataset. Keywords: fault detection, fault identi\fcation, recurrent neural networks, variational dropout,\nBayesian inference, Tennessee Eastman process. 1.", "retrieval_score": 0.5470008254051208, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2403.10842v4_chunk_1", "chunk_text": "Fault detection and diagnosis (FDD) is a crucial task for ensuring the safety\nand efficiency of industrial processes. We propose a novel FDD methodology for\nthe Tennessee Eastman Process (TEP), a widely used benchmark for chemical\nprocess control. The model employs two separate Transformer branches, enabling\nindependent processing of input data and potential extraction of diverse\ninformation.", "retrieval_score": 0.5467948913574219, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Recycle Flow → Product Sep Level"}, {"chunk_id": "pdf_3805_chunk_2", "chunk_text": "Abstract: Fault detection and diagnosis (FDD) is a crucial task for ensuring the safety and \nefficiency of industrial processes. We propose a novel FDD methodology for the Tennessee \nEastman Process (TEP), a widely used benchmark for chemical process control. The model \nemploys two separate Transformer branches, enabling independent processing of input data and \npotential extraction of diverse information.", "retrieval_score": 0.5462472438812256, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2309.00157v1_chunk_3", "chunk_text": "We validate the approach\nusing the Benchmark Tennessee Eastman while doing an ablation study of the\nmodel update parameters.", "retrieval_score": 0.535120964050293, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Product Sep Level"}, {"chunk_id": "1911.04386v2_chunk_4", "chunk_text": "The\noutstanding performance of this method is demonstrated and contrasted to\n(dynamic) principal component analysis, which are widely applied in the\nindustry, in the benchmark Tennessee Eastman process~(TEP) and a real chemical\nmanufacturing dataset.", "retrieval_score": 0.5345286130905151, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Recycle Flow - Product Sep Level"}, {"chunk_id": "2301.11573v3_chunk_3", "chunk_text": "The theoretical analysis is verified through\nMonte Carlo simulations and Tennessee Eastman process (TEP) dataset.", "retrieval_score": 0.5331839323043823, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Product Sep Level"}, {"chunk_id": "pdf_131_chunk_0", "chunk_text": "arXiv:2303.05904v1  [cs.LG]  10 Mar 2023Deep Anomaly Detection on Tennessee Eastman Process Data", "retrieval_score": 0.5295485854148865, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.527395486831665, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Product Sep Level"}, {"chunk_id": "1709.02232v1_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5273954272270203, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Product Sep Level"}, {"chunk_id": "pdf_7856_chunk_5", "chunk_text": "In the second stage, a reconstruction error-based threshold approach using the trained\nGAWNO is employed to detect and isolate faults based on the discrepancy values. We validate the\nproposed approach using the Tennessee Eastman Process (TEP) dataset and Avedore wastewater\ntreatment plant (WWTP) and N2Oemissions nam.", "retrieval_score": 0.5214158296585083, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}], "original_retrieval_results": 15}