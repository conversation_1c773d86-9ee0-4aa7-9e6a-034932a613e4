{"variable_pair": ["<PERSON>ed", "Product Sep Level"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "E Feed → Product Sep Level", "association_votes": {"A": 6, "?": 2, "IA": 7}, "direction_votes": {"E Feed - Product Sep Level": 3, "E Feed → Product Sep Level": 10}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "2301.11573v3_chunk_3", "chunk_text": "The theoretical analysis is verified through\nMonte Carlo simulations and Tennessee Eastman process (TEP) dataset.", "retrieval_score": 0.5590595006942749, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed → Product Sep Level"}, {"chunk_id": "pdf_9787_chunk_6", "chunk_text": "Experimental validation on both the Tennessee Eastman \nProcess (TEP) and the Secure Water Treatment (SWaT) \ntestbeds demonstrates the framework’s superior performan ce. A comprehensive ablation study, supported by extensive visualizations including Receiver Operatin.", "retrieval_score": 0.5569053292274475, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2210.08538v1_chunk_0", "chunk_text": "Advantages of OKID-ERA Identification in Control Systems. An Application\nto the Tennessee Eastman Plant", "retrieval_score": 0.5474532246589661, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed → Product Sep Level"}, {"chunk_id": "2309.00157v1_chunk_3", "chunk_text": "We validate the approach\nusing the Benchmark Tennessee Eastman while doing an ablation study of the\nmodel update parameters.", "retrieval_score": 0.5467174053192139, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed - Product Sep Level"}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5406941175460815, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "E Feed - Product Sep Level"}, {"chunk_id": "pdf_6959_chunk_5", "chunk_text": "Simulation results demonstrate the efficacy of this approach, showcasing superior performance in \naccuracy, false alarm rate, and misclassifica tion rate compared to existing methods. This \nmethodology provides a robust and interpretable solution for fault detection and diagnosis in the \nTEP, highlighting its potential for industrial applications. Keywords. Fault Detection, Tennessee Eastman Process, Supervised Feature Selection, Attention \nMechanism, Bidirectional Long Short -Term Memory.", "retrieval_score": 0.5406702756881714, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2308.11247v2_chunk_0", "chunk_text": "Benchmarking Domain Adaptation for Chemical Processes on the Tennessee\nEastman Process", "retrieval_score": 0.5295196771621704, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed → Product Sep Level"}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5272176861763, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "E Feed → Product Sep Level"}, {"chunk_id": "1709.02232v1_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5272176861763, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "E Feed → Product Sep Level"}, {"chunk_id": "1607.07423v3_chunk_5", "chunk_text": "We illustrate the\nsuccessful use of $K_T$ chart using the Tennessee Eastman process data.", "retrieval_score": 0.5263778567314148, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "E Feed → Product Sep Level"}, {"chunk_id": "1911.04386v2_chunk_4", "chunk_text": "The\noutstanding performance of this method is demonstrated and contrasted to\n(dynamic) principal component analysis, which are widely applied in the\nindustry, in the benchmark Tennessee Eastman process~(TEP) and a real chemical\nmanufacturing dataset.", "retrieval_score": 0.5241076350212097, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed → Product Sep Level"}, {"chunk_id": "pdf_4903_chunk_3", "chunk_text": "We conduct a\nsystematic evaluation of the FARM monitoring framework using the Tennessee Eastman Process\n(TEP) dataset. Results show that FARM performs competitively against state-of-the-art process\nmonitoring algorithms by achieving a good balance among fault detection rate (FDR), fault\ndetection speed (FDS), and false alarm rate (FAR). Specifically, FARM achieved an average\nFDR of 96.", "retrieval_score": 0.5236571431159973, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "E Feed → Product Sep Level"}, {"chunk_id": "2504.01276v1_chunk_2", "chunk_text": "We conduct a systematic evaluation of the FARM monitoring framework\nusing the Tennessee Eastman Process (TEP) dataset. Results show that FARM\nperforms competitively against state-of-the-art process monitoring algorithms\nby achieving a good balance among fault detection rate (FDR), fault detection\nspeed (FDS), and false alarm rate (FAR). Specifically, FARM achieved an average\nFDR of 96.", "retrieval_score": 0.5236571431159973, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "E Feed - Product Sep Level"}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.523086428642273, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed → Product Sep Level"}, {"chunk_id": "2001.01687v1_chunk_0", "chunk_text": "A Supervised Modified Hebbian Learning Method On Feed-forward Neural\nNetworks", "retrieval_score": 0.5220208764076233, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed → Product Sep Level"}], "original_retrieval_results": 15}