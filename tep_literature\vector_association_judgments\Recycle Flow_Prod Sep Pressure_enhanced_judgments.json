{"variable_pair": ["Recycle Flow", "Prod Sep Pressure"], "aggregation": {"has_causal_relationship": true, "final_association": "A", "final_direction": "Recycle Flow → Prod Sep Pressure", "association_votes": {"A": 6, "?": 4, "IA": 4, "⊥": 1}, "direction_votes": {"Recycle Flow - Prod Sep Pressure": 4, "Recycle Flow → Prod Sep Pressure": 6}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.6028395891189575, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Prod Sep Pressure"}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.6026773452758789, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_131_chunk_0", "chunk_text": "arXiv:2303.05904v1  [cs.LG]  10 Mar 2023Deep Anomaly Detection on Tennessee Eastman Process Data", "retrieval_score": 0.5813199281692505, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2403.10842v4_chunk_1", "chunk_text": "Fault detection and diagnosis (FDD) is a crucial task for ensuring the safety\nand efficiency of industrial processes. We propose a novel FDD methodology for\nthe Tennessee Eastman Process (TEP), a widely used benchmark for chemical\nprocess control. The model employs two separate Transformer branches, enabling\nindependent processing of input data and potential extraction of diverse\ninformation.", "retrieval_score": 0.576445460319519, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Recycle Flow → Prod Sep Pressure"}, {"chunk_id": "pdf_3805_chunk_2", "chunk_text": "Abstract: Fault detection and diagnosis (FDD) is a crucial task for ensuring the safety and \nefficiency of industrial processes. We propose a novel FDD methodology for the Tennessee \nEastman Process (TEP), a widely used benchmark for chemical process control. The model \nemploys two separate Transformer branches, enabling independent processing of input data and \npotential extraction of diverse information.", "retrieval_score": 0.5746396780014038, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow - Prod Sep Pressure"}, {"chunk_id": "pdf_750_chunk_0", "chunk_text": "Transmission Expansion Planning Using Cycle Flows", "retrieval_score": 0.5577483177185059, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Prod Sep Pressure"}, {"chunk_id": "2004.08702v1_chunk_0", "chunk_text": "Transmission Expansion Planning Using Cycle Flows", "retrieval_score": 0.5577483177185059, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Recycle Flow - Prod Sep Pressure"}, {"chunk_id": "2301.11573v3_chunk_3", "chunk_text": "The theoretical analysis is verified through\nMonte Carlo simulations and Tennessee Eastman process (TEP) dataset.", "retrieval_score": 0.5547657012939453, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2308.11247v2_chunk_0", "chunk_text": "Benchmarking Domain Adaptation for Chemical Processes on the Tennessee\nEastman Process", "retrieval_score": 0.5547088980674744, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Recycle Flow - Prod Sep Pressure"}, {"chunk_id": "pdf_6959_chunk_5", "chunk_text": "Simulation results demonstrate the efficacy of this approach, showcasing superior performance in \naccuracy, false alarm rate, and misclassifica tion rate compared to existing methods. This \nmethodology provides a robust and interpretable solution for fault detection and diagnosis in the \nTEP, highlighting its potential for industrial applications. Keywords. Fault Detection, Tennessee Eastman Process, Supervised Feature Selection, Attention \nMechanism, Bidirectional Long Short -Term Memory.", "retrieval_score": 0.5541084408760071, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Recycle Flow - Prod Sep Pressure"}, {"chunk_id": "2310.03117v1_chunk_0", "chunk_text": "Growing Planet Envelopes in Spite of Recycling Flows", "retrieval_score": 0.5507292151451111, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Recycle Flow → Prod Sep Pressure"}, {"chunk_id": "1607.07423v3_chunk_5", "chunk_text": "We illustrate the\nsuccessful use of $K_T$ chart using the Tennessee Eastman process data.", "retrieval_score": 0.5427243709564209, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Prod Sep Pressure"}, {"chunk_id": "2106.12003v2_chunk_1", "chunk_text": "The core accretion model of giant planet formation has been challenged by the\ndiscovery of recycling flows between the planetary envelope and the disc that\ncan slow or stall envelope accretion. We carry out 3D radiation hydrodynamic\nsimulations with an updated opacity compilation to model the proto-Jupiter's\nenvelope. To isolate the 3D effects of convection and recycling, we simulate\nboth isolated spherical envelopes and envelopes embedded in discs.", "retrieval_score": 0.5408879518508911, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Recycle Flow → Prod Sep Pressure"}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.539912760257721, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "1709.02232v1_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.539912760257721, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}], "original_retrieval_results": 15}