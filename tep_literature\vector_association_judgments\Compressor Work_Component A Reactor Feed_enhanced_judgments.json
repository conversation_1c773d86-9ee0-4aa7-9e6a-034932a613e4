{"variable_pair": ["Compressor Work", "Component A Reactor Feed"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Compressor Work → Component A Reactor Feed", "association_votes": {"A": 2, "⊥": 4, "IA": 9}, "direction_votes": {"Compressor Work → Component A Reactor Feed": 6, "Compressor Work - Component A Reactor Feed": 5}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "pdf_2177_chunk_2", "chunk_text": "Eksioglub, <PERSON>ushi <PERSON>c,\naEnergy and Environment Science & Technology Department, Idaho National Laboratory, Idaho Falls, ID\nbIndustrial Engineering Department, University of Arkansas, Fayetteville, AR\ncGlenn Department of Civil Engineering, Clemson University, Clemson, SC\nAbstract\nThe variations in feedstock characteristics such as moisture and particle size distribution\nlead to an inconsistent ﬂow of feedstock from the biomass pre-processing system to the reactor\nin-feed system.", "retrieval_score": 0.5963568687438965, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Compressor Work → Component A Reactor Feed"}, {"chunk_id": "pdf_7578_chunk_5", "chunk_text": "Key Word s: reactorpressure vessel (RPV), neutron irradiation, dose -rate effects,\nmagnetic properties of steels. 1. Introduction\nTheintegrity of the components of power plant reactors throughout their service life is\naffected by the degradation suffered by t.", "retrieval_score": 0.5722068548202515, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Compressor Work - Component A Reactor Feed"}, {"chunk_id": "pdf_2177_chunk_3", "chunk_text": "These inconsistencies result in low on-stream times at the reactor in-feed\nequipment. This research develops an optimal process control method for a biomass pre-\nprocessing system comprised of milling and densiﬁcation operations to provide the consistent\nﬂow of feedstock to a reactor’s throat. This method uses a mixed-integer optimization\nmodel to identify optimal bale sequencing, equipment in-feed rate, and buﬀer location and\nsize in the biomass pre-processing system.", "retrieval_score": 0.5651008486747742, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Compressor Work → Component A Reactor Feed"}, {"chunk_id": "pdf_819_chunk_0", "chunk_text": "Geometric Decomposition of Feed Forward", "retrieval_score": 0.5636059045791626, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2103.05025v1_chunk_1", "chunk_text": "The variations in feedstock characteristics such as moisture and particle\nsize distribution lead to an inconsistent flow of feedstock from the biomass\npre-processing system to the reactor in-feed system. These inconsistencies\nresult in low on-stream times at the reactor in-feed equipment. This research\ndevelops an optimal process control method for a biomass pre-processing system\ncomprised of milling and densification operations to provide the consistent\nflow of feedstock to a reactor's throat.", "retrieval_score": 0.5620667338371277, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2107.08078v1_chunk_4", "chunk_text": "Our sensitivity\nanalysis indicates that updating the infeed rate of the system, the processing\nspeed of equipment, and bale sequencing based on the moisture level of biomass\nimproves the processing rate of the reactor and reduces operating costs.", "retrieval_score": 0.5546723008155823, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Compressor Work → Component A Reactor Feed"}, {"chunk_id": "2403.10842v4_chunk_0", "chunk_text": "Twin Transformer using Gated Dynamic Learnable Attention mechanism for\nFault Detection and Diagnosis in the Tennessee Eastman Process", "retrieval_score": 0.5536115169525146, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Compressor Work → Component A Reactor Feed"}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5525059700012207, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Compressor Work - Component A Reactor Feed"}, {"chunk_id": "2309.00157v1_chunk_3", "chunk_text": "We validate the approach\nusing the Benchmark Tennessee Eastman while doing an ablation study of the\nmodel update parameters.", "retrieval_score": 0.5516952872276306, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Compressor Work - Component A Reactor Feed"}, {"chunk_id": "1910.12475v2_chunk_0", "chunk_text": "The coherent feed-forward loop acts as an efficient information\ntransmitting motif", "retrieval_score": 0.5515923500061035, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Compressor Work - Component A Reactor Feed"}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.5515156984329224, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2103.15186v1_chunk_2", "chunk_text": "The proposed approach is applied to an industrial\ncase study: Tennessee Eastman process. The results show that the proposed\napproach is successful in determining the probable cause of alarms generated\nwith high accuracy. The model was able to identify the cause accurately, even\nwhen tested with short alarm sub-sequences. This allows for early\nidentification of faults, providing more time to the operator to restore the\nsystem to normal operation.", "retrieval_score": 0.5508335828781128, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Compressor Work → Component A Reactor Feed"}, {"chunk_id": "pdf_7154_chunk_2", "chunk_text": "In this paper,  a feed-\nforward compensator is designed via a data- driven method to \nmodel and optimize the  controller’s  performance. Principal \nComponent Analysis (PCA) is  applied for extract ing influential \nfeatures , after which  a Time Delay Neural Network is adopted \nto predict control  errors  over  a future time horizon. Based on \nthe predict ed error, a feed forward compensator is then designed \nto impr ove control performance.", "retrieval_score": 0.5483055114746094, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Compressor Work - Component A Reactor Feed"}, {"chunk_id": "2301.05537v4_chunk_2", "chunk_text": "The\ncontroller features a circuit-breaking mechanism, which circumvents potential\nsafety breach and guarantees the convergence of the system parameter estimate,\nbut is shown to be triggered only finitely often and hence has negligible\neffect on the asymptotic performance of the controller. The proposed controller\nis also validated via simulation on Tennessee Eastman Process~(TEP), a commonly\nused industrial process example.", "retrieval_score": 0.5465342998504639, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_3469_chunk_0", "chunk_text": "Embedding the dynamics of a single delay system into a feed-forward ring", "retrieval_score": 0.5461507439613342, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Compressor Work → Component A Reactor Feed"}], "original_retrieval_results": 15}