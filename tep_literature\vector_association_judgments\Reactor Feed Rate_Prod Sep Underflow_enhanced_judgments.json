{"variable_pair": ["Reactor Feed Rate", "Prod Sep Underflow"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Reactor Feed Rate → Prod Sep Underflow", "association_votes": {"A": 3, "?": 4, "IA": 7, "⊥": 1}, "direction_votes": {"Reactor Feed Rate → Prod Sep Underflow": 10}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "pdf_2177_chunk_2", "chunk_text": "Eksioglub, <PERSON>ushi <PERSON>c,\naEnergy and Environment Science & Technology Department, Idaho National Laboratory, Idaho Falls, ID\nbIndustrial Engineering Department, University of Arkansas, Fayetteville, AR\ncGlenn Department of Civil Engineering, Clemson University, Clemson, SC\nAbstract\nThe variations in feedstock characteristics such as moisture and particle size distribution\nlead to an inconsistent ﬂow of feedstock from the biomass pre-processing system to the reactor\nin-feed system.", "retrieval_score": 0.6261401176452637, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Feed Rate → Prod Sep Underflow"}, {"chunk_id": "2103.05025v1_chunk_0", "chunk_text": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate", "retrieval_score": 0.6107938289642334, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Prod Sep Underflow"}, {"chunk_id": "2103.05025v1_chunk_1", "chunk_text": "The variations in feedstock characteristics such as moisture and particle\nsize distribution lead to an inconsistent flow of feedstock from the biomass\npre-processing system to the reactor in-feed system. These inconsistencies\nresult in low on-stream times at the reactor in-feed equipment. This research\ndevelops an optimal process control method for a biomass pre-processing system\ncomprised of milling and densification operations to provide the consistent\nflow of feedstock to a reactor's throat.", "retrieval_score": 0.6012783646583557, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Prod Sep Underflow"}, {"chunk_id": "pdf_2177_chunk_3", "chunk_text": "These inconsistencies result in low on-stream times at the reactor in-feed\nequipment. This research develops an optimal process control method for a biomass pre-\nprocessing system comprised of milling and densiﬁcation operations to provide the consistent\nﬂow of feedstock to a reactor’s throat. This method uses a mixed-integer optimization\nmodel to identify optimal bale sequencing, equipment in-feed rate, and buﬀer location and\nsize in the biomass pre-processing system.", "retrieval_score": 0.5831261873245239, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Feed Rate → Prod Sep Underflow"}, {"chunk_id": "2107.08078v1_chunk_4", "chunk_text": "Our sensitivity\nanalysis indicates that updating the infeed rate of the system, the processing\nspeed of equipment, and bale sequencing based on the moisture level of biomass\nimproves the processing rate of the reactor and reduces operating costs.", "retrieval_score": 0.5828922986984253, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Prod Sep Underflow"}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5800278186798096, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "1807.03623v1_chunk_0", "chunk_text": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "retrieval_score": 0.5708413124084473, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Prod Sep Underflow"}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.5662866234779358, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Prod Sep Underflow"}, {"chunk_id": "pdf_131_chunk_0", "chunk_text": "arXiv:2303.05904v1  [cs.LG]  10 Mar 2023Deep Anomaly Detection on Tennessee Eastman Process Data", "retrieval_score": 0.5623021125793457, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Prod Sep Underflow"}, {"chunk_id": "pdf_4903_chunk_3", "chunk_text": "We conduct a\nsystematic evaluation of the FARM monitoring framework using the Tennessee Eastman Process\n(TEP) dataset. Results show that FARM performs competitively against state-of-the-art process\nmonitoring algorithms by achieving a good balance among fault detection rate (FDR), fault\ndetection speed (FDS), and false alarm rate (FAR). Specifically, FARM achieved an average\nFDR of 96.", "retrieval_score": 0.561774492263794, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2504.01276v1_chunk_2", "chunk_text": "We conduct a systematic evaluation of the FARM monitoring framework\nusing the Tennessee Eastman Process (TEP) dataset. Results show that FARM\nperforms competitively against state-of-the-art process monitoring algorithms\nby achieving a good balance among fault detection rate (FDR), fault detection\nspeed (FDS), and false alarm rate (FAR). Specifically, FARM achieved an average\nFDR of 96.", "retrieval_score": 0.561774492263794, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Feed Rate → Prod Sep Underflow"}, {"chunk_id": "2301.11573v3_chunk_3", "chunk_text": "The theoretical analysis is verified through\nMonte Carlo simulations and Tennessee Eastman process (TEP) dataset.", "retrieval_score": 0.5521404147148132, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Prod Sep Underflow"}, {"chunk_id": "1709.02232v1_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5504973530769348, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.55049729347229, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2301.05537v4_chunk_2", "chunk_text": "The\ncontroller features a circuit-breaking mechanism, which circumvents potential\nsafety breach and guarantees the convergence of the system parameter estimate,\nbut is shown to be triggered only finitely often and hence has negligible\neffect on the asymptotic performance of the controller. The proposed controller\nis also validated via simulation on Tennessee Eastman Process~(TEP), a commonly\nused industrial process example.", "retrieval_score": 0.5493442416191101, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}], "original_retrieval_results": 15}