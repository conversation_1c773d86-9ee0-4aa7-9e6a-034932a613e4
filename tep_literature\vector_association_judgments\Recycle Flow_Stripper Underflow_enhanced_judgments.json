{"variable_pair": ["Recycle Flow", "Stripper Underflow"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Recycle Flow → Stripper Underflow", "association_votes": {"A": 1, "⊥": 1, "?": 4, "IA": 9}, "direction_votes": {"Recycle Flow - Stripper Underflow": 2, "Recycle Flow → Stripper Underflow": 8}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "pdf_750_chunk_0", "chunk_text": "Transmission Expansion Planning Using Cycle Flows", "retrieval_score": 0.5648834705352783, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2004.08702v1_chunk_0", "chunk_text": "Transmission Expansion Planning Using Cycle Flows", "retrieval_score": 0.5648834705352783, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Stripper Underflow"}, {"chunk_id": "pdf_1917_chunk_3", "chunk_text": "Stadium\nAve, West Lafayette, 47907, IN, USA\nAbstract\nMachine learning algorithms are increasingly being applied to fault detection\nand diagnosis (FDD) in chemical processes. However, existing data-driven\nFDD platforms often lack interpretability for process operators and strug-\ngle to identify root causes of previously unseen faults. This paper presents\nFaultExplainer , an interactive tool designed to improve fault detection, diag-\nnosis, and explanation in the Tennessee Eastman Process (TEP).", "retrieval_score": 0.5613365173339844, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Stripper Underflow"}, {"chunk_id": "2310.03117v1_chunk_0", "chunk_text": "Growing Planet Envelopes in Spite of Recycling Flows", "retrieval_score": 0.558417022228241, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Stripper Underflow"}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5581119060516357, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_6776_chunk_1", "chunk_text": "Typeset using L ATEXtwocolumn style in AASTeX631\nGrowing Planet Envelopes in Spite of Recycling Flows\nAvery P.", "retrieval_score": 0.5562203526496887, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Stripper Underflow"}, {"chunk_id": "pdf_131_chunk_0", "chunk_text": "arXiv:2303.05904v1  [cs.LG]  10 Mar 2023Deep Anomaly Detection on Tennessee Eastman Process Data", "retrieval_score": 0.5484915375709534, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_7856_chunk_5", "chunk_text": "In the second stage, a reconstruction error-based threshold approach using the trained\nGAWNO is employed to detect and isolate faults based on the discrepancy values. We validate the\nproposed approach using the Tennessee Eastman Process (TEP) dataset and Avedore wastewater\ntreatment plant (WWTP) and N2Oemissions nam.", "retrieval_score": 0.5482711791992188, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_6861_chunk_5", "chunk_text": "Our models show general agreement with 1D static calculations,\nsuggesting a limited role of recycling flows in determining envelope structure. By adopting a dimen-\nsionless framework, these models can be applied to a wide range of formation conditions and assumed\nopacities. In particular,.", "retrieval_score": 0.546421229839325, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Stripper Underflow"}, {"chunk_id": "2403.10842v4_chunk_0", "chunk_text": "Twin Transformer using Gated Dynamic Learnable Attention mechanism for\nFault Detection and Diagnosis in the Tennessee Eastman Process", "retrieval_score": 0.5424032211303711, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2412.14492v1_chunk_1", "chunk_text": "Machine learning algorithms are increasingly being applied to fault detection\nand diagnosis (FDD) in chemical processes. However, existing data-driven FDD\nplatforms often lack interpretability for process operators and struggle to\nidentify root causes of previously unseen faults. This paper presents\nFaultExplainer, an interactive tool designed to improve fault detection,\ndiagnosis, and explanation in the Tennessee Eastman Process (TEP).", "retrieval_score": 0.5395437479019165, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Stripper Underflow"}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.5368091464042664, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow - Stripper Underflow"}, {"chunk_id": "pdf_2572_chunk_3", "chunk_text": "We apply our proposed compression\nmethod on the Tennessee Eastman Dataset, where we perform\nfault classification using the compressed data in two settings:\na fully supervised one and in a semi supervised, contrastive\nlearning setting. Both times, we were able to outperform real\nvalued counterparts as well as two baseline models: one with\nthe uncompressed time-series as the input and the other with a\nregular downsampling using the mean.", "retrieval_score": 0.536736249923706, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Stripper Underflow"}, {"chunk_id": "2403.11722v2_chunk_3", "chunk_text": "We\napply our proposed compression method on the Tennessee Eastman Dataset, where\nwe perform fault classification using the compressed data in two settings: a\nfully supervised one and in a semi supervised, contrastive learning setting. Both times, we were able to outperform real valued counterparts as well as two\nbaseline models: one with the uncompressed time-series as the input and the\nother with a regular downsampling using the mean.", "retrieval_score": 0.536736249923706, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Recycle Flow - Stripper Underflow"}, {"chunk_id": "pdf_223_chunk_4", "chunk_text": "Here, we reevaluate this line of reasoning by incorporating recycling flows of gas\ninto a numerical one-dimensional thermodynamic model with more realistic equation of state and\nopacities and the thermal state of the advective flow. At 0. 1 AU, we find that advective flows are\nonly able to produce mini-Neptunes if they can penetrate below ∼0. 25 of the planet’s gravitational\nsphere of influence.", "retrieval_score": 0.5325388312339783, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Stripper Underflow"}], "original_retrieval_results": 15}