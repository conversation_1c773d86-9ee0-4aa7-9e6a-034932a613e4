{"variable_pair": ["Compressor Work", "Component E Purge Gas"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Compressor Work → Component E Purge Gas", "association_votes": {"A": 2, "⊥": 5, "IA": 8}, "direction_votes": {"Compressor Work → Component E Purge Gas": 8, "Compressor Work - Component E Purge Gas": 2}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.578415036201477, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_9936_chunk_3", "chunk_text": "The purpos e of this study is to find a series of alloys promptly forming \nmetal hydrides (MH) with suitable properties in  order to build a MH-based hydrogen compressor, \nworking in the same way between 20 and ~100 oC.", "retrieval_score": 0.5304625034332275, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Compressor Work → Component E Purge Gas"}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.527734100818634, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Compressor Work → Component E Purge Gas"}, {"chunk_id": "pdf_9406_chunk_5", "chunk_text": "The performance of the method is demonstrated\nand contrasted to (dynamic) principal component analysis, which is widely applied in the industry,\nin the benchmark Tennessee Eastman process (TEP) and a real chemical manufacturing dataset. Keywords: fault detection, fault identi\fcation, recurrent neural networks, variational dropout,\nBayesian inference, Tennessee Eastman process. 1.", "retrieval_score": 0.5262674689292908, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Compressor Work → Component E Purge Gas"}, {"chunk_id": "1911.04386v2_chunk_4", "chunk_text": "The\noutstanding performance of this method is demonstrated and contrasted to\n(dynamic) principal component analysis, which are widely applied in the\nindustry, in the benchmark Tennessee Eastman process~(TEP) and a real chemical\nmanufacturing dataset.", "retrieval_score": 0.5244476795196533, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Compressor Work → Component E Purge Gas"}, {"chunk_id": "pdf_6959_chunk_0", "chunk_text": "Enhanced Fault Detection and Cause Identification Using", "retrieval_score": 0.5222852826118469, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2403.13502v4_chunk_0", "chunk_text": "Adversarial Attacks and Defenses in Fault Detection and Diagnosis: A\nComprehensive Benchmark on the Tennessee Eastman Process", "retrieval_score": 0.519673228263855, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Compressor Work → Component E Purge Gas"}, {"chunk_id": "pdf_8440_chunk_1", "chunk_text": "Deep Compression of Neural Networks for Fault\nDetection on Tennessee Eastman Chemical\nProcesses\nMingxuan Li\nDepartment of Computer Science\nUniversity of North Carolina at Chapel Hill\nChapel Hill, NC, USA\nmingxuan li@unc. edu* <PERSON><PERSON>un Shao\nNuro Inc. Mountain View, CA, USA\nyuanxun@gatech. edu\nAbstract\nArtiﬁcial neural network has achieved the state-of-art performance in fault detection on the Tennessee Eastman process, but\nit often requires enormous memory to fund its massive parameters.", "retrieval_score": 0.5187649726867676, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Compressor Work → Component E Purge Gas"}, {"chunk_id": "pdf_1846_chunk_3", "chunk_text": "1 Introduction\nThe ECG is often corrupted by di\u000berent types of noise, namely, power line inter-\nference, electrode contact and motion artifacts, respiration, electrical activity\nof muscles in the vicinity of the electrodes and interference from other elec-\ntronic devices. Analysis of noisy ECGs is di\u000ecult for humans and for computer\nprograms. In this work we place ourselves in context of automatic and semi\nautomatic ECG analysis: denoising should facilitate automatic ECG analysis.", "retrieval_score": 0.5157126188278198, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_1917_chunk_3", "chunk_text": "Stadium\nAve, West Lafayette, 47907, IN, USA\nAbstract\nMachine learning algorithms are increasingly being applied to fault detection\nand diagnosis (FDD) in chemical processes. However, existing data-driven\nFDD platforms often lack interpretability for process operators and strug-\ngle to identify root causes of previously unseen faults. This paper presents\nFaultExplainer , an interactive tool designed to improve fault detection, diag-\nnosis, and explanation in the Tennessee Eastman Process (TEP).", "retrieval_score": 0.5145303010940552, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Compressor Work - Component E Purge Gas"}, {"chunk_id": "2103.15186v1_chunk_2", "chunk_text": "The proposed approach is applied to an industrial\ncase study: Tennessee Eastman process. The results show that the proposed\napproach is successful in determining the probable cause of alarms generated\nwith high accuracy. The model was able to identify the cause accurately, even\nwhen tested with short alarm sub-sequences. This allows for early\nidentification of faults, providing more time to the operator to restore the\nsystem to normal operation.", "retrieval_score": 0.5101804733276367, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Compressor Work → Component E Purge Gas"}, {"chunk_id": "1712.04118v1_chunk_0", "chunk_text": "Neural Component Analysis for Fault Detection", "retrieval_score": 0.5097321271896362, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "1607.07423v3_chunk_5", "chunk_text": "We illustrate the\nsuccessful use of $K_T$ chart using the Tennessee Eastman process data.", "retrieval_score": 0.508664608001709, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Compressor Work - Component E Purge Gas"}, {"chunk_id": "2308.11247v2_chunk_0", "chunk_text": "Benchmarking Domain Adaptation for Chemical Processes on the Tennessee\nEastman Process", "retrieval_score": 0.5086243748664856, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Compressor Work → Component E Purge Gas"}, {"chunk_id": "2403.10842v4_chunk_0", "chunk_text": "Twin Transformer using Gated Dynamic Learnable Attention mechanism for\nFault Detection and Diagnosis in the Tennessee Eastman Process", "retrieval_score": 0.5051172375679016, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}], "original_retrieval_results": 15}