{"variable_pair": ["Reactor Pressure", "Stripper Underflow"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Reactor Pressure → Stripper Underflow", "association_votes": {"A": 2, "⊥": 1, "?": 4, "IA": 8}, "direction_votes": {"Stripper Underflow → Reactor Pressure": 2, "Reactor Pressure - Stripper Underflow": 2, "Reactor Pressure → Stripper Underflow": 6}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5822277069091797, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "1807.03623v1_chunk_0", "chunk_text": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "retrieval_score": 0.5736525058746338, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "1706.02043v1_chunk_2", "chunk_text": "During the reduction\nprocess, issues were encountered with pressure-dependent reactions formulated\nusing the logarithmic pressure interpolation approach; these issues are\ndiscussed and recommendations made to avoid ambiguity in its future\nimplementation in mechanism development.", "retrieval_score": 0.5622358918190002, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Pressure - Stripper Underflow"}, {"chunk_id": "pdf_1917_chunk_3", "chunk_text": "Stadium\nAve, West Lafayette, 47907, IN, USA\nAbstract\nMachine learning algorithms are increasingly being applied to fault detection\nand diagnosis (FDD) in chemical processes. However, existing data-driven\nFDD platforms often lack interpretability for process operators and strug-\ngle to identify root causes of previously unseen faults. This paper presents\nFaultExplainer , an interactive tool designed to improve fault detection, diag-\nnosis, and explanation in the Tennessee Eastman Process (TEP).", "retrieval_score": 0.5585353374481201, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Pressure → Stripper Underflow"}, {"chunk_id": "pdf_7900_chunk_1", "chunk_text": "Keywords:  reactor pressure vessel, embrittlement, tran sition temperature shift, machine \nlearning, neural network", "retrieval_score": 0.5574162602424622, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Pressure - Stripper Underflow"}, {"chunk_id": "2309.02362v1_chunk_0", "chunk_text": "Predictions and Uncertainty Estimates of Reactor Pressure Vessel Steel\nEmbrittlement Using Machine Learning", "retrieval_score": 0.557307243347168, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Pressure → Stripper Underflow"}, {"chunk_id": "pdf_7900_chunk_0", "chunk_text": "1 Predictions and Uncertainty Estimates of Reactor Pressure Vessel Steel Embrittlement Using\n\n1 Predictions and Uncertainty Estimates of Reactor Pressure Vessel Steel Embrittlement Using  \nMachine Learning\n\nAuthors: <AUTHORS>\n1 Department of Materials Science and Engineering, University of Wisconsin -Madison, Madison, \nWI, USA  \n2 Mechanical Engineering Department, University of California Santa Barbara, Santa Barbara, CA, \nUSA.", "retrieval_score": 0.5551026463508606, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_3945_chunk_3", "chunk_text": "During the reduction process, issues encountered with pressure-dependent\nreactions formulated using the logarithmic pressure interpolation approach were dis-\ncussed, with recommendations made to avoid ambiguity in its future implementation\nin mechanism development.", "retrieval_score": 0.5512949228286743, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Pressure → Stripper Underflow"}, {"chunk_id": "pdf_131_chunk_0", "chunk_text": "arXiv:2303.05904v1  [cs.LG]  10 Mar 2023Deep Anomaly Detection on Tennessee Eastman Process Data", "retrieval_score": 0.5470151901245117, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2108.09224v1_chunk_0", "chunk_text": "Characterizing accelerated precipitation in proton irradiated steel", "retrieval_score": 0.5467877388000488, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_8399_chunk_4", "chunk_text": "The\tmechanisms\tdriving\ttheir\tformation\tare\tstill\tdebated. This\twork\tfocuses\ton\tlow-Cu\treactor\t pressure\t vessel\t (RPV)\tsteels,\t where\tsolute\t species\t are\tgenerally\t not\texpected\tto\tprecipitate. We\trationalize\tthe\tprocesses\tthat\ttake\tplace\tat\tthe\tnanometre\tscale\tunder\tirradiation,\trelying\ton\tthe\tlatest\ttheoretical\tand\texperimental\tevidence\ton\tatomic-level\tdiffusion\tand\ttransport\tprocesses. These\tare\tcompiled\tin\ta\tnew\tmodel,\tbased\ton\tthe\tobject\tkinetic\tMonte\tCarlo\t(OKMC)\ttechnique.", "retrieval_score": 0.5438508987426758, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Pressure → Stripper Underflow"}, {"chunk_id": "pdf_7900_chunk_2", "chunk_text": "Abstract:  An essential aspect of extending safe operation of the world’s active nuclear reactors \nis understanding and predicting the embrittlement that occurs in the steels that make up the \nReacto r pressure vessel (RPV). In this work we integrate state of the art machine learning \nmethods using ensembles of neural networks with unprecedented data collection and \nintegration to develop a new model for RPV steel embrittlement.", "retrieval_score": 0.5428751111030579, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Pressure → Stripper Underflow"}, {"chunk_id": "pdf_9114_chunk_0", "chunk_text": "Thermodynamic and kinetic modeling of Mn-Ni-Si precipitates in low-Cu  reactor pressure vessel steel [1707.08072v1]", "retrieval_score": 0.5426051616668701, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow → Reactor Pressure"}, {"chunk_id": "2309.02362v1_chunk_1", "chunk_text": "An essential aspect of extending safe operation of the active nuclear\nreactors is understanding and predicting the embrittlement that occurs in the\nsteels that make up the Reactor pressure vessel (RPV). In this work we\nintegrate state of the art machine learning methods using ensembles of neural\nnetworks with unprecedented data collection and integration to develop a new\nmodel for RPV steel embrittlement.", "retrieval_score": 0.5401890277862549, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Pressure → Stripper Underflow"}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.539044976234436, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow → Reactor Pressure"}], "original_retrieval_results": 15}