{"variable_pair": ["Product Sep Level", "Stripper Level"], "results": [{"chunk": {"chunk_id": "pdf_8965_chunk_0", "paper_id": "pdf_8965", "paper_title": "Texture Edge detection by Patch consensus (TEP)", "chunk_text": "Texture Edge detection by Patch consensus (TEP)", "found_variables": ["TEP"], "variable_count": 1, "chunk_length": 47}, "score": 0.5051997900009155, "query": "Product Sep Level and Stripper Level"}, {"chunk": {"chunk_id": "2403.11038v1_chunk_0", "paper_id": "2403.11038v1", "paper_title": "Texture Edge detection by Patch consensus (TEP)", "chunk_text": "Texture Edge detection by Patch consensus (TEP)", "found_variables": ["TEP"], "variable_count": 1, "chunk_length": 47}, "score": 0.505199670791626, "query": "Product Sep Level and Stripper Level"}, {"chunk": {"chunk_id": "pdf_1023_chunk_4", "paper_id": "pdf_1023", "paper_title": "arXiv:cond-mat/0204249v1  11 Apr 2002Nonlinear behavior in the Thermopower of Doped Carbon Nanot ubes", "chunk_text": "In contrast, the nitrogen d oped material exhibits negative TEP over\nthe same temperature range, suggesting electron-like cond uction. Therefore, the TEP distinct non-\nlinearites are primarily due to the formation of donor and ac ceptor states in the B- and N- doped\nmaterials. The sharply varying density of states used in our model can be directly correlated to the\nscanning tunneling spectroscopy studies of these material s. PACS numbers: 65. 80+n, 61. 46+w, 73. 63. Fg, 81. 07.", "found_variables": ["temperature", "temp", "TEP"], "variable_count": 3, "chunk_length": 482}, "score": 0.5045356154441833, "query": "correlation Product Sep Level Stripper Level"}, {"chunk": {"chunk_id": "1201.0715v3_chunk_4", "paper_id": "1201.0715v3", "paper_title": "Tree-Structure Expectation Propagation for LDPC Decoding over the BEC", "chunk_text": "The\nsolution of these equations is used to predict the TEP decoder performance in\nboth the asymptotic regime and the finite-length regime over the BEC. While the\nasymptotic threshold of the TEP decoder is the same as the BP decoder for\nregular and optimized codes, we propose a scaling law (SL) for finite-length\nLDPC codes, which accurately approximates the TEP improved performance and\nfacilitates its optimization.", "found_variables": ["rate", "TEP"], "variable_count": 2, "chunk_length": 417}, "score": 0.49643945693969727, "query": "Product Sep Level and Stripper Level"}, {"chunk": {"chunk_id": "pdf_7507_chunk_5", "paper_id": "pdf_7507", "paper_title": "arXiv:cond-mat/0107360v2  [cond-mat.str-el]  31 Jul 2001Theory of Thermoelectric Power in High- TcSuperconductors", "chunk_text": "(ii)Sis negative in electron-doped compounds\nlike Nd 2−xCexCuO4(NCCO) and LaPr 1−xCexCuO4\n(PCCO), and dS/dT > 0 in the under-doped compound\nat higher temperatures T>∼200 K [2,3]. In both cases,\n|S|increases drastically as the doping decreases. Thus,\na conventional Fermi liquid type behavior, S∝T, is\ntotally violated in high- Tccuprates for a wide range of\ntemperatures. In particular, a qualitative particle-hole\nsymmetric behavior of the TEP, i. e.", "found_variables": ["temperature", "rate", "temp", "TEP", "vent"], "variable_count": 5, "chunk_length": 451}, "score": 0.49586397409439087, "query": "Product Sep Level and Stripper Level"}, {"chunk": {"chunk_id": "pdf_9794_chunk_4", "paper_id": "pdf_9794", "paper_title": "IEEE TRANSACTIONS ON INFORMATION THEORY 1", "chunk_text": "The solution of these equations is used\nto predict the TEP decoder performance in both the asymptotic\nregime and the ﬁnite-length regime over the BEC. While the\nasymptotic threshold of the TEP decoder is the same as the\nBP decoder for regular and optimized codes, we propose a\nscaling law (SL) for ﬁnite-length LDPC codes, which accurately\napproximates the TEP improved performance and facilitates its\noptimization. I.", "found_variables": ["rate", "TEP"], "variable_count": 2, "chunk_length": 418}, "score": 0.49412044882774353, "query": "Product Sep Level and Stripper Level"}, {"chunk": {"chunk_id": "1307.0249v1_chunk_1", "paper_id": "1307.0249v1", "paper_title": "Electric Field Effect Thermoelectric Transport in Individual Silicon and\nGermanium/Silicon Nanowire", "chunk_text": "We have simultaneously measured conductance and thermoelectric power (TEP) of\nindividual silicon and germanium/silicon core/shell nanowires in the field\neffect transistor device configuration. As the applied gate voltage changes,\nthe TEP shows distinctly different behaviors while the electrical conductance\nexhibits the turn-off, subthreshold, and saturation regimes respectively. At\nroom temperature, peak TEP value of $\\sim 300 \\mu$V/K is observed in the\nsubthreshold regime of the Si devices.", "found_variables": ["temperature", "temp", "TEP"], "variable_count": 3, "chunk_length": 496}, "score": 0.4935328960418701, "query": "Product Sep Level and Stripper Level"}, {"chunk": {"chunk_id": "pdf_7507_chunk_4", "paper_id": "pdf_7507", "paper_title": "arXiv:cond-mat/0107360v2  [cond-mat.str-el]  31 Jul 2001Theory of Thermoelectric Power in High- TcSuperconductors", "chunk_text": "In particular, the\nthermoelectric power (TEP), expressed by the Seebeck\ncoeﬃcient S, isknowntoshowcharacteristictemperature\nand doping dependences above Tc, namely (i)dS/dT <\n0 in hole-doped compounds such as YBa 2Cu3O7−x\n(YBCO) or La 2−xSrxCuO4(LSCO), and Sis posi-\ntive in the under-doped systems at room temperatures\n[1,2].", "found_variables": ["temperature", "temp", "press", "TEP"], "variable_count": 4, "chunk_length": 326}, "score": 0.493162602186203, "query": "Product Sep Level and Stripper Level"}, {"chunk": {"chunk_id": "pdf_3480_chunk_3", "paper_id": "pdf_3480", "paper_title": "arXiv:1307.0249v1  [cond-mat.mes-hall]  30 Jun 2013Electric Field Eﬀect Thermoelectric Transport in Individu al Silicon and", "chunk_text": "As the applied gate voltage changes, the TEP shows dist inctly diﬀerent behaviors while the\nelectrical conductance exhibits the turn-oﬀ, subthreshol d, and saturation regimes respectively. At\nroom temperature, peak TEP value of ∼300µV/K is observed in the subthreshold regime of the Si\ndevices. The temperature dependence of the saturated TEP va lues are used to estimate the carrier\ndoping of Si nanowires.", "found_variables": ["temperature", "rate", "temp", "TEP"], "variable_count": 4, "chunk_length": 407}, "score": 0.49096590280532837, "query": "Product Sep Level and Stripper Level"}, {"chunk": {"chunk_id": "1708.00077v1_chunk_2", "paper_id": "1708.00077v1", "paper_title": "Bayesian Sparsification of Recurrent Neural Networks", "chunk_text": "5%\nsparsity level on sentiment analysis task without a quality drop and up to 87%\nsparsity level on language modeling task with slight loss of accuracy.", "found_variables": ["level"], "variable_count": 1, "chunk_length": 152}, "score": 0.4876229465007782, "query": "Product Sep Level and Stripper Level"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}