{"variable_pair": ["Component F Reactor Feed", "Component A Purge Gas"], "aggregation": {"has_causal_relationship": true, "final_association": "A", "final_direction": "Component F Reactor Feed → Component A Purge Gas", "association_votes": {"A": 10, "?": 1, "IA": 3, "⊥": 1}, "direction_votes": {"Component F Reactor Feed → Component A Purge Gas": 12, "Component F Reactor Feed - Component A Purge Gas": 1}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "pdf_2177_chunk_2", "chunk_text": "Eksioglub, <PERSON>ushi <PERSON>c,\naEnergy and Environment Science & Technology Department, Idaho National Laboratory, Idaho Falls, ID\nbIndustrial Engineering Department, University of Arkansas, Fayetteville, AR\ncGlenn Department of Civil Engineering, Clemson University, Clemson, SC\nAbstract\nThe variations in feedstock characteristics such as moisture and particle size distribution\nlead to an inconsistent ﬂow of feedstock from the biomass pre-processing system to the reactor\nin-feed system.", "retrieval_score": 0.5275747179985046, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Component F Reactor Feed → Component A Purge Gas"}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5132085084915161, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Component F Reactor Feed → Component A Purge Gas"}, {"chunk_id": "pdf_1917_chunk_3", "chunk_text": "Stadium\nAve, West Lafayette, 47907, IN, USA\nAbstract\nMachine learning algorithms are increasingly being applied to fault detection\nand diagnosis (FDD) in chemical processes. However, existing data-driven\nFDD platforms often lack interpretability for process operators and strug-\ngle to identify root causes of previously unseen faults. This paper presents\nFaultExplainer , an interactive tool designed to improve fault detection, diag-\nnosis, and explanation in the Tennessee Eastman Process (TEP).", "retrieval_score": 0.5006899833679199, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_3805_chunk_2", "chunk_text": "Abstract: Fault detection and diagnosis (FDD) is a crucial task for ensuring the safety and \nefficiency of industrial processes. We propose a novel FDD methodology for the Tennessee \nEastman Process (TEP), a widely used benchmark for chemical process control. The model \nemploys two separate Transformer branches, enabling independent processing of input data and \npotential extraction of diverse information.", "retrieval_score": 0.49976345896720886, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Component F Reactor Feed → Component A Purge Gas"}, {"chunk_id": "1911.04386v2_chunk_4", "chunk_text": "The\noutstanding performance of this method is demonstrated and contrasted to\n(dynamic) principal component analysis, which are widely applied in the\nindustry, in the benchmark Tennessee Eastman process~(TEP) and a real chemical\nmanufacturing dataset.", "retrieval_score": 0.49950703978538513, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Component F Reactor Feed → Component A Purge Gas"}, {"chunk_id": "2309.00157v1_chunk_3", "chunk_text": "We validate the approach\nusing the Benchmark Tennessee Eastman while doing an ablation study of the\nmodel update parameters.", "retrieval_score": 0.49596118927001953, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Component F Reactor Feed → Component A Purge Gas"}, {"chunk_id": "2403.10842v4_chunk_1", "chunk_text": "Fault detection and diagnosis (FDD) is a crucial task for ensuring the safety\nand efficiency of industrial processes. We propose a novel FDD methodology for\nthe Tennessee Eastman Process (TEP), a widely used benchmark for chemical\nprocess control. The model employs two separate Transformer branches, enabling\nindependent processing of input data and potential extraction of diverse\ninformation.", "retrieval_score": 0.4952196478843689, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Component F Reactor Feed → Component A Purge Gas"}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.4952017664909363, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Component F Reactor Feed → Component A Purge Gas"}, {"chunk_id": "pdf_819_chunk_0", "chunk_text": "Geometric Decomposition of Feed Forward", "retrieval_score": 0.49424299597740173, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Component F Reactor Feed → Component A Purge Gas"}, {"chunk_id": "pdf_745_chunk_5", "chunk_text": "-p\n1 Introduction\nUranium dioxide (UO 2) is a standard nuclear fuel in pressurized-water reac-\ntors. In operation in power plants or in the context of direct dispos al of spent\nfuel, a clear understanding of its structural, thermodynamical an d kinetical\nproperties is very important. Therefore many studies have been u ndertaken\n∗Corresponding author. PreprintsubmittedtoJOURNALOFALLOYSANDCOMPOUNDS17Sep tember2018\nabout the behavior of this material under irradiation.", "retrieval_score": 0.4887882471084595, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Component F Reactor Feed → Component A Purge Gas"}, {"chunk_id": "1712.04118v1_chunk_0", "chunk_text": "Neural Component Analysis for Fault Detection", "retrieval_score": 0.48462608456611633, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Component F Reactor Feed - Component A Purge Gas"}, {"chunk_id": "2103.07303v1_chunk_0", "chunk_text": "Second-Order Component Analysis for Fault Detection", "retrieval_score": 0.48415619134902954, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Component F Reactor Feed → Component A Purge Gas"}, {"chunk_id": "pdf_520_chunk_4", "chunk_text": "ca (<PERSON>)\nPreprint submitted to Journal of Process Control December 24, 2024arXiv:2409. 11444v3  [eess. SY]  23 Dec 2024\nproposed method in a straightforward manner. Keywords: Fault Detection and Identification (FDI), Distributed Process\nMonitoring, PFD and Control Loop Based Process Decomposition,\nTennessee Eastman Plant (TEP)\n1.", "retrieval_score": 0.4832606911659241, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Component F Reactor Feed → Component A Purge Gas"}, {"chunk_id": "pdf_7578_chunk_0", "chunk_text": "Correlation between radiation damage and magnetic properties in reactor vessel", "retrieval_score": 0.4828970432281494, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_1428_chunk_0", "chunk_text": "Development of a Feeding Assistive Robot Using a Six Degree of", "retrieval_score": 0.48284459114074707, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Component F Reactor Feed → Component A Purge Gas"}], "original_retrieval_results": 15}