{"variable_pair": ["<PERSON>ed", "Stripper Underflow"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "E Feed → Stripper Underflow", "association_votes": {"A": 4, "⊥": 1, "?": 4, "IA": 6}, "direction_votes": {"Stripper Underflow → E Feed": 1, "E Feed - Stripper Underflow": 2, "E Feed → Stripper Underflow": 7}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5604937076568604, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow → E Feed"}, {"chunk_id": "pdf_819_chunk_0", "chunk_text": "Geometric Decomposition of Feed Forward", "retrieval_score": 0.5556057095527649, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2403.10842v4_chunk_0", "chunk_text": "Twin Transformer using Gated Dynamic Learnable Attention mechanism for\nFault Detection and Diagnosis in the Tennessee Eastman Process", "retrieval_score": 0.5524593591690063, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed → Stripper Underflow"}, {"chunk_id": "pdf_2859_chunk_0", "chunk_text": "Feed-forward neural network unfolding", "retrieval_score": 0.5511668920516968, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2112.08180v1_chunk_0", "chunk_text": "Feed-forward neural network unfolding", "retrieval_score": 0.5511668920516968, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_1917_chunk_3", "chunk_text": "Stadium\nAve, West Lafayette, 47907, IN, USA\nAbstract\nMachine learning algorithms are increasingly being applied to fault detection\nand diagnosis (FDD) in chemical processes. However, existing data-driven\nFDD platforms often lack interpretability for process operators and strug-\ngle to identify root causes of previously unseen faults. This paper presents\nFaultExplainer , an interactive tool designed to improve fault detection, diag-\nnosis, and explanation in the Tennessee Eastman Process (TEP).", "retrieval_score": 0.5494763851165771, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed → Stripper Underflow"}, {"chunk_id": "pdf_131_chunk_0", "chunk_text": "arXiv:2303.05904v1  [cs.LG]  10 Mar 2023Deep Anomaly Detection on Tennessee Eastman Process Data", "retrieval_score": 0.5366328954696655, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed - Stripper Underflow"}, {"chunk_id": "pdf_7856_chunk_5", "chunk_text": "In the second stage, a reconstruction error-based threshold approach using the trained\nGAWNO is employed to detect and isolate faults based on the discrepancy values. We validate the\nproposed approach using the Tennessee Eastman Process (TEP) dataset and Avedore wastewater\ntreatment plant (WWTP) and N2Oemissions nam.", "retrieval_score": 0.535054087638855, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2403.13502v4_chunk_0", "chunk_text": "Adversarial Attacks and Defenses in Fault Detection and Diagnosis: A\nComprehensive Benchmark on the Tennessee Eastman Process", "retrieval_score": 0.5333547592163086, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "E Feed → Stripper Underflow"}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.5321184396743774, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "E Feed → Stripper Underflow"}, {"chunk_id": "2309.00157v1_chunk_3", "chunk_text": "We validate the approach\nusing the Benchmark Tennessee Eastman while doing an ablation study of the\nmodel update parameters.", "retrieval_score": 0.5311541557312012, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "E Feed - Stripper Underflow"}, {"chunk_id": "pdf_3937_chunk_3", "chunk_text": "We demon-\nstrate our model’s performance on two publicly available datasets of the Tennessee\nEastman Process with various faults. The results show that our method significantly\noutperforms existing approaches (+0. 2-0. 3 TPR for a fixed FPR) and effectively de-\ntects most of the process faults without expert annotation. Moreover, we show that the\nmodel fine-tuned on a small fraction of labeled data nearly reaches the performance of\na SOTA model trained on the full dataset.", "retrieval_score": 0.5257692337036133, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed → Stripper Underflow"}, {"chunk_id": "2202.07058v1_chunk_0", "chunk_text": "Features of Linear Models that May Compromise Model-Based, Plant-Wide\nControl Techniques. The Case of the Tennessee Eastman Plant", "retrieval_score": 0.5254483819007874, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2301.05537v4_chunk_2", "chunk_text": "The\ncontroller features a circuit-breaking mechanism, which circumvents potential\nsafety breach and guarantees the convergence of the system parameter estimate,\nbut is shown to be triggered only finitely often and hence has negligible\neffect on the asymptotic performance of the controller. The proposed controller\nis also validated via simulation on Tennessee Eastman Process~(TEP), a commonly\nused industrial process example.", "retrieval_score": 0.5248477458953857, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "E Feed → Stripper Underflow"}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5247567296028137, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "E Feed → Stripper Underflow"}], "original_retrieval_results": 15}