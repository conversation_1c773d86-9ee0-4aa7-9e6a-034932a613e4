{"variable_pair": ["Purge Rate", "Component F Reactor Feed"], "aggregation": {"has_causal_relationship": true, "final_association": "A", "final_direction": "Purge Rate → Component F Reactor Feed", "association_votes": {"A": 4, "⊥": 4, "IA": 4, "?": 3}, "direction_votes": {"Purge Rate → Component F Reactor Feed": 7, "Purge Rate - Component F Reactor Feed": 1}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "2107.08078v1_chunk_4", "chunk_text": "Our sensitivity\nanalysis indicates that updating the infeed rate of the system, the processing\nspeed of equipment, and bale sequencing based on the moisture level of biomass\nimproves the processing rate of the reactor and reduces operating costs.", "retrieval_score": 0.5530887842178345, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Component F Reactor Feed"}, {"chunk_id": "pdf_819_chunk_0", "chunk_text": "Geometric Decomposition of Feed Forward", "retrieval_score": 0.5393316745758057, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Component F Reactor Feed"}, {"chunk_id": "pdf_2177_chunk_2", "chunk_text": "Eksioglub, <PERSON>ushi <PERSON>c,\naEnergy and Environment Science & Technology Department, Idaho National Laboratory, Idaho Falls, ID\nbIndustrial Engineering Department, University of Arkansas, Fayetteville, AR\ncGlenn Department of Civil Engineering, Clemson University, Clemson, SC\nAbstract\nThe variations in feedstock characteristics such as moisture and particle size distribution\nlead to an inconsistent ﬂow of feedstock from the biomass pre-processing system to the reactor\nin-feed system.", "retrieval_score": 0.5352458953857422, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_7578_chunk_0", "chunk_text": "Correlation between radiation damage and magnetic properties in reactor vessel", "retrieval_score": 0.5317016839981079, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Purge Rate - Component F Reactor Feed"}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5301650762557983, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Component F Reactor Feed"}, {"chunk_id": "2103.05025v1_chunk_0", "chunk_text": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate", "retrieval_score": 0.528775691986084, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2309.00157v1_chunk_3", "chunk_text": "We validate the approach\nusing the Benchmark Tennessee Eastman while doing an ablation study of the\nmodel update parameters.", "retrieval_score": 0.5261545181274414, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Component F Reactor Feed"}, {"chunk_id": "pdf_7578_chunk_5", "chunk_text": "Key Word s: reactorpressure vessel (RPV), neutron irradiation, dose -rate effects,\nmagnetic properties of steels. 1. Introduction\nTheintegrity of the components of power plant reactors throughout their service life is\naffected by the degradation suffered by t.", "retrieval_score": 0.525668203830719, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "1709.02232v1_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5183970332145691, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Purge Rate → Component F Reactor Feed"}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5183968544006348, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.****************, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2103.12222v1_chunk_2", "chunk_text": "The explainability is quantified by a novel relevance\nmeasure of input variables that is calculated from a Layerwise Relevance\nPropagation (LRP) algorithm. It is shown that the relevances can be used to\ndiscard redundant input feature vectors/ variables iteratively thus resulting\nin reduced over-fitting of noisy data, increasing distinguishability between\noutput classes and superior FDD test accuracy. The efficacy of the proposed\nmethod is demonstrated on the benchmark Tennessee Eastman Process.", "retrieval_score": 0.5082607865333557, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Purge Rate → Component F Reactor Feed"}, {"chunk_id": "pdf_2177_chunk_1", "chunk_text": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate\n<PERSON><PERSON><PERSON>, <PERSON>,1, <PERSON>.", "retrieval_score": 0.5054507255554199, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_1917_chunk_3", "chunk_text": "Stadium\nAve, West Lafayette, 47907, IN, USA\nAbstract\nMachine learning algorithms are increasingly being applied to fault detection\nand diagnosis (FDD) in chemical processes. However, existing data-driven\nFDD platforms often lack interpretability for process operators and strug-\ngle to identify root causes of previously unseen faults. This paper presents\nFaultExplainer , an interactive tool designed to improve fault detection, diag-\nnosis, and explanation in the Tennessee Eastman Process (TEP).", "retrieval_score": 0.5011711120605469, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Purge Rate → Component F Reactor Feed"}, {"chunk_id": "1612.02522v1_chunk_0", "chunk_text": "Geometric Decomposition of Feed Forward Neural Networks", "retrieval_score": 0.5006293654441833, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}], "original_retrieval_results": 15}