{"variable_pair": ["Recycle Flow", "Component D Purge Gas"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Recycle Flow → Component D Purge Gas", "association_votes": {"A": 4, "⊥": 3, "IA": 6, "?": 2}, "direction_votes": {"Recycle Flow → Component D Purge Gas": 6, "Recycle Flow - Component D Purge Gas": 4}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5772998332977295, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2403.10842v4_chunk_0", "chunk_text": "Twin Transformer using Gated Dynamic Learnable Attention mechanism for\nFault Detection and Diagnosis in the Tennessee Eastman Process", "retrieval_score": 0.526996374130249, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow - Component D Purge Gas"}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.5267723202705383, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Component D Purge Gas"}, {"chunk_id": "pdf_131_chunk_0", "chunk_text": "arXiv:2303.05904v1  [cs.LG]  10 Mar 2023Deep Anomaly Detection on Tennessee Eastman Process Data", "retrieval_score": 0.5244931578636169, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Component D Purge Gas"}, {"chunk_id": "pdf_6776_chunk_5", "chunk_text": "We incor-\nporate these 3D hydrodynamic effects into an extensible 1D framework with a physically motivated\nthree-layer recycling parameterization. Specializing to the case of Jupiter, recycling produces minimal\nchanges to the growth rate with the planet still entering runaway accretion and becoming a gas giant\nin∼1 Myr. Even in the inner disk (0. 1 AU), our 1D models suggest that recycling is not so robust\nand ubiquitous as to stop all cores from becoming giants.", "retrieval_score": 0.5227329730987549, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Recycle Flow → Component D Purge Gas"}, {"chunk_id": "pdf_1764_chunk_0", "chunk_text": "Machine Learned Interatomic Potential for Dispersion Strengthened Plasma Facing Components", "retrieval_score": 0.5225812196731567, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2212.01432v1_chunk_0", "chunk_text": "Machine Learned Interatomic Potential for Dispersion Strengthened Plasma\nFacing Components", "retrieval_score": 0.5225812196731567, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2310.03117v1_chunk_3", "chunk_text": "We incorporate\nthese 3D hydrodynamic effects into an extensible 1D framework with a physically\nmotivated three-layer recycling parameterization. Specializing to the case of\nJupiter, recycling produces minimal changes to the growth rate with the planet\nstill entering runaway accretion and becoming a gas giant in ~1 Myr. Even in\nthe inner disk (0. 1 AU), our 1D models suggest that recycling is not so robust\nand ubiquitous as to stop all cores from becoming giants.", "retrieval_score": 0.5198094844818115, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Recycle Flow → Component D Purge Gas"}, {"chunk_id": "2406.09186v2_chunk_0", "chunk_text": "A formation pathway for terrestrial planets with moderate water content\ninvolving atmospheric-volatile recycling", "retrieval_score": 0.518359363079071, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Recycle Flow - Component D Purge Gas"}, {"chunk_id": "pdf_3805_chunk_2", "chunk_text": "Abstract: Fault detection and diagnosis (FDD) is a crucial task for ensuring the safety and \nefficiency of industrial processes. We propose a novel FDD methodology for the Tennessee \nEastman Process (TEP), a widely used benchmark for chemical process control. The model \nemploys two separate Transformer branches, enabling independent processing of input data and \npotential extraction of diverse information.", "retrieval_score": 0.5147669315338135, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Recycle Flow → Component D Purge Gas"}, {"chunk_id": "pdf_1917_chunk_3", "chunk_text": "Stadium\nAve, West Lafayette, 47907, IN, USA\nAbstract\nMachine learning algorithms are increasingly being applied to fault detection\nand diagnosis (FDD) in chemical processes. However, existing data-driven\nFDD platforms often lack interpretability for process operators and strug-\ngle to identify root causes of previously unseen faults. This paper presents\nFaultExplainer , an interactive tool designed to improve fault detection, diag-\nnosis, and explanation in the Tennessee Eastman Process (TEP).", "retrieval_score": 0.5125999450683594, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "1807.03623v1_chunk_0", "chunk_text": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "retrieval_score": 0.5119932293891907, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow - Component D Purge Gas"}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5119479894638062, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "1709.02232v1_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5119479894638062, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow → Component D Purge Gas"}, {"chunk_id": "pdf_6861_chunk_5", "chunk_text": "Our models show general agreement with 1D static calculations,\nsuggesting a limited role of recycling flows in determining envelope structure. By adopting a dimen-\nsionless framework, these models can be applied to a wide range of formation conditions and assumed\nopacities. In particular,.", "retrieval_score": 0.5106790065765381, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Recycle Flow - Component D Purge Gas"}], "original_retrieval_results": 15}