{"variable_pair": ["Purge Rate", "Product Sep Level"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Purge Rate → Product Sep Level", "association_votes": {"A": 1, "⊥": 3, "IA": 10, "?": 1}, "direction_votes": {"Purge Rate - Product Sep Level": 3, "Purge Rate → Product Sep Level": 8}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5644192099571228, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Product Sep Level"}, {"chunk_id": "1708.00077v1_chunk_2", "chunk_text": "5%\nsparsity level on sentiment analysis task without a quality drop and up to 87%\nsparsity level on language modeling task with slight loss of accuracy.", "retrieval_score": 0.5619814395904541, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate - Product Sep Level"}, {"chunk_id": "pdf_6959_chunk_5", "chunk_text": "Simulation results demonstrate the efficacy of this approach, showcasing superior performance in \naccuracy, false alarm rate, and misclassifica tion rate compared to existing methods. This \nmethodology provides a robust and interpretable solution for fault detection and diagnosis in the \nTEP, highlighting its potential for industrial applications. Keywords. Fault Detection, Tennessee Eastman Process, Supervised Feature Selection, Attention \nMechanism, Bidirectional Long Short -Term Memory.", "retrieval_score": 0.5579882860183716, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Product Sep Level"}, {"chunk_id": "2309.00157v1_chunk_3", "chunk_text": "We validate the approach\nusing the Benchmark Tennessee Eastman while doing an ablation study of the\nmodel update parameters.", "retrieval_score": 0.5547031164169312, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate - Product Sep Level"}, {"chunk_id": "1911.04386v2_chunk_4", "chunk_text": "The\noutstanding performance of this method is demonstrated and contrasted to\n(dynamic) principal component analysis, which are widely applied in the\nindustry, in the benchmark Tennessee Eastman process~(TEP) and a real chemical\nmanufacturing dataset.", "retrieval_score": 0.5512081980705261, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Product Sep Level"}, {"chunk_id": "pdf_9406_chunk_5", "chunk_text": "The performance of the method is demonstrated\nand contrasted to (dynamic) principal component analysis, which is widely applied in the industry,\nin the benchmark Tennessee Eastman process (TEP) and a real chemical manufacturing dataset. Keywords: fault detection, fault identi\fcation, recurrent neural networks, variational dropout,\nBayesian inference, Tennessee Eastman process. 1.", "retrieval_score": 0.5495941042900085, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Product Sep Level"}, {"chunk_id": "pdf_3937_chunk_3", "chunk_text": "We demon-\nstrate our model’s performance on two publicly available datasets of the Tennessee\nEastman Process with various faults. The results show that our method significantly\noutperforms existing approaches (+0. 2-0. 3 TPR for a fixed FPR) and effectively de-\ntects most of the process faults without expert annotation. Moreover, we show that the\nmodel fine-tuned on a small fraction of labeled data nearly reaches the performance of\na SOTA model trained on the full dataset.", "retrieval_score": 0.547919511795044, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Purge Rate → Product Sep Level"}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5471688508987427, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "1709.02232v1_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5471688508987427, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate - Product Sep Level"}, {"chunk_id": "2301.11573v3_chunk_3", "chunk_text": "The theoretical analysis is verified through\nMonte Carlo simulations and Tennessee Eastman process (TEP) dataset.", "retrieval_score": 0.5466874837875366, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_9787_chunk_6", "chunk_text": "Experimental validation on both the Tennessee Eastman \nProcess (TEP) and the Secure Water Treatment (SWaT) \ntestbeds demonstrates the framework’s superior performan ce. A comprehensive ablation study, supported by extensive visualizations including Receiver Operatin.", "retrieval_score": 0.5439839363098145, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "1607.07423v3_chunk_5", "chunk_text": "We illustrate the\nsuccessful use of $K_T$ chart using the Tennessee Eastman process data.", "retrieval_score": 0.5404489040374756, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Product Sep Level"}, {"chunk_id": "2308.11247v2_chunk_0", "chunk_text": "Benchmarking Domain Adaptation for Chemical Processes on the Tennessee\nEastman Process", "retrieval_score": 0.5380180478096008, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Product Sep Level"}, {"chunk_id": "pdf_131_chunk_0", "chunk_text": "arXiv:2303.05904v1  [cs.LG]  10 Mar 2023Deep Anomaly Detection on Tennessee Eastman Process Data", "retrieval_score": 0.5318249464035034, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2403.10842v4_chunk_1", "chunk_text": "Fault detection and diagnosis (FDD) is a crucial task for ensuring the safety\nand efficiency of industrial processes. We propose a novel FDD methodology for\nthe Tennessee Eastman Process (TEP), a widely used benchmark for chemical\nprocess control. The model employs two separate Transformer branches, enabling\nindependent processing of input data and potential extraction of diverse\ninformation.", "retrieval_score": 0.5311708450317383, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Product Sep Level"}], "original_retrieval_results": 15}