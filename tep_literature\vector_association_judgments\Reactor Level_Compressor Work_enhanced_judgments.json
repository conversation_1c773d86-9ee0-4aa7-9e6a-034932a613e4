{"variable_pair": ["Reactor Level", "Compressor Work"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Reactor Level → Compressor Work", "association_votes": {"A": 4, "?": 3, "IA": 6, "⊥": 2}, "direction_votes": {"Reactor Level - Compressor Work": 1, "Reactor Level → Compressor Work": 9}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.6134268045425415, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2308.11247v2_chunk_0", "chunk_text": "Benchmarking Domain Adaptation for Chemical Processes on the Tennessee\nEastman Process", "retrieval_score": 0.5798934102058411, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Level → Compressor Work"}, {"chunk_id": "pdf_7578_chunk_0", "chunk_text": "Correlation between radiation damage and magnetic properties in reactor vessel", "retrieval_score": 0.5710567235946655, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_6635_chunk_5", "chunk_text": "g. temperature in the reactor) given other par-\ntial observations (e. g. mass fraction in the reactor). The proposed work\row\nrelies on the manifold learning schemes Di\u000busion Maps and the associated\nGeometric Harmonics. Di\u000busion Maps is used for discovering a reduced rep-\nresent.", "retrieval_score": 0.5698485374450684, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Level → Compressor Work"}, {"chunk_id": "2403.10842v4_chunk_0", "chunk_text": "Twin Transformer using Gated Dynamic Learnable Attention mechanism for\nFault Detection and Diagnosis in the Tennessee Eastman Process", "retrieval_score": 0.5644468665122986, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Level → Compressor Work"}, {"chunk_id": "pdf_7900_chunk_1", "chunk_text": "Keywords:  reactor pressure vessel, embrittlement, tran sition temperature shift, machine \nlearning, neural network", "retrieval_score": 0.5534385442733765, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Level - Compressor Work"}, {"chunk_id": "pdf_8440_chunk_1", "chunk_text": "Deep Compression of Neural Networks for Fault\nDetection on Tennessee Eastman Chemical\nProcesses\nMingxuan Li\nDepartment of Computer Science\nUniversity of North Carolina at Chapel Hill\nChapel Hill, NC, USA\nmingxuan li@unc. edu* <PERSON><PERSON>un Shao\nNuro Inc. Mountain View, CA, USA\nyuanxun@gatech. edu\nAbstract\nArtiﬁcial neural network has achieved the state-of-art performance in fault detection on the Tennessee Eastman process, but\nit often requires enormous memory to fund its massive parameters.", "retrieval_score": 0.5494660139083862, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Level → Compressor Work"}, {"chunk_id": "pdf_2572_chunk_3", "chunk_text": "We apply our proposed compression\nmethod on the Tennessee Eastman Dataset, where we perform\nfault classification using the compressed data in two settings:\na fully supervised one and in a semi supervised, contrastive\nlearning setting. Both times, we were able to outperform real\nvalued counterparts as well as two baseline models: one with\nthe uncompressed time-series as the input and the other with a\nregular downsampling using the mean.", "retrieval_score": 0.5417976975440979, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2403.11722v2_chunk_3", "chunk_text": "We\napply our proposed compression method on the Tennessee Eastman Dataset, where\nwe perform fault classification using the compressed data in two settings: a\nfully supervised one and in a semi supervised, contrastive learning setting. Both times, we were able to outperform real valued counterparts as well as two\nbaseline models: one with the uncompressed time-series as the input and the\nother with a regular downsampling using the mean.", "retrieval_score": 0.5417976379394531, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Level → Compressor Work"}, {"chunk_id": "2107.08078v1_chunk_4", "chunk_text": "Our sensitivity\nanalysis indicates that updating the infeed rate of the system, the processing\nspeed of equipment, and bale sequencing based on the moisture level of biomass\nimproves the processing rate of the reactor and reduces operating costs.", "retrieval_score": 0.5398842692375183, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Level → Compressor Work"}, {"chunk_id": "pdf_7578_chunk_5", "chunk_text": "Key Word s: reactorpressure vessel (RPV), neutron irradiation, dose -rate effects,\nmagnetic properties of steels. 1. Introduction\nTheintegrity of the components of power plant reactors throughout their service life is\naffected by the degradation suffered by t.", "retrieval_score": 0.5358524918556213, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Level → Compressor Work"}, {"chunk_id": "2103.15186v1_chunk_2", "chunk_text": "The proposed approach is applied to an industrial\ncase study: Tennessee Eastman process. The results show that the proposed\napproach is successful in determining the probable cause of alarms generated\nwith high accuracy. The model was able to identify the cause accurately, even\nwhen tested with short alarm sub-sequences. This allows for early\nidentification of faults, providing more time to the operator to restore the\nsystem to normal operation.", "retrieval_score": 0.5345040559768677, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Level → Compressor Work"}, {"chunk_id": "pdf_6959_chunk_5", "chunk_text": "Simulation results demonstrate the efficacy of this approach, showcasing superior performance in \naccuracy, false alarm rate, and misclassifica tion rate compared to existing methods. This \nmethodology provides a robust and interpretable solution for fault detection and diagnosis in the \nTEP, highlighting its potential for industrial applications. Keywords. Fault Detection, Tennessee Eastman Process, Supervised Feature Selection, Attention \nMechanism, Bidirectional Long Short -Term Memory.", "retrieval_score": 0.5343600511550903, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_7393_chunk_1", "chunk_text": "arXiv:1703. 08935v1  [math. OC]  27 Mar 2017PREPRINT OF DOI: 10. 1109/TPWRS. 2017. 2671786, IEEE TRANSA CTIONS ON POWER SYSTEMS 1\nSecurity Constrained Multi-Stage Transmission\nExpansion Planning Considering a Continuously\nVariable Series Reactor\n<PERSON><PERSON>, Student Member, IEEE, <PERSON>, Fellow, IEEE,\nand <PERSON><PERSON><PERSON><PERSON>, Senior Member, IEEE\nAbstract —This paper introduces a Continuously Variable Se-\nries Reactor (CVSR) to the transmission expansion planning\n(TEP) problem.", "retrieval_score": 0.5324805974960327, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Level → Compressor Work"}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5310136079788208, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}], "original_retrieval_results": 15}