{"variable_pair": ["Purge Rate", "Stripper Level"], "results": [{"chunk": {"chunk_id": "1708.00077v1_chunk_2", "paper_id": "1708.00077v1", "paper_title": "Bayesian Sparsification of Recurrent Neural Networks", "chunk_text": "5%\nsparsity level on sentiment analysis task without a quality drop and up to 87%\nsparsity level on language modeling task with slight loss of accuracy.", "found_variables": ["level"], "variable_count": 1, "chunk_length": 152}, "score": 0.5416725873947144, "query": "Purge Rate and Stripper Level"}, {"chunk": {"chunk_id": "pdf_5760_chunk_0", "paper_id": "pdf_5760", "paper_title": "A N OVEL DEEPPARALLEL TIME-SERIES RELATION NETWORK", "chunk_text": "A N OVEL DEEPPARALLEL TIME-SERIES RELATION NETWORK", "found_variables": ["work"], "variable_count": 1, "chunk_length": 50}, "score": 0.497867226600647, "query": "Purge Rate Stripper Level interaction"}, {"chunk": {"chunk_id": "1003.0921v1_chunk_2", "paper_id": "1003.0921v1", "paper_title": "Experimental Setup for the Measurement of the Thermoelectric Power in\nZero and Applied Magnetic Field", "chunk_text": "High density data\nof temperature sweeps from 2 to 350 K can be acquired in under 16 hours and\nhigh density data of isothermal field sweeps from 0 to 140 kOe can be obtained\nin under 2 hours. Calibrations for the system have been performed on a platinum\nwire and Bi$_{2}$Sr$_{2}$CaCu$_{2}$O$_{8+\\delta}$ high $T_{c}$ superconductors. The measured TEP of phosphor-bronze (voltage lead wire) turns to be very small,\nwhere the absolute TEP value of phosphor-bronze wire is much less than 0.", "found_variables": ["temperature", "temp", "thermal", "TEP"], "variable_count": 4, "chunk_length": 486}, "score": 0.****************, "query": "Purge Rate and Stripper Level"}, {"chunk": {"chunk_id": "pdf_9_chunk_2", "paper_id": "pdf_9", "paper_title": "Bayesian Sparsiﬁcation of Recurrent Neural Networks", "chunk_text": "We\napply this technique to sparsify recurrent neu-\nral networks. To account for recurrent speciﬁcs\nwe also rely on Binary Variational Dropout for\nRNN (Gal & Gha<PERSON>, 2016b). We report\n99. 5% sparsity level on sentiment analysis task\nwithout a quality drop and up to 87% sparsity\nlevel on language modeling task with slight loss\nof accuracy. 1.", "found_variables": ["work", "level"], "variable_count": 2, "chunk_length": 346}, "score": 0.*****************, "query": "Purge Rate and Stripper Level"}, {"chunk": {"chunk_id": "pdf_6260_chunk_0", "paper_id": "pdf_6260", "paper_title": "Attention-Based Multiscale Temporal Fusion Network", "chunk_text": "Attention-Based Multiscale Temporal Fusion Network", "found_variables": ["temp", "work"], "variable_count": 2, "chunk_length": 50}, "score": 0.****************, "query": "Purge Rate Stripper Level interaction"}, {"chunk": {"chunk_id": "pdf_6684_chunk_3", "paper_id": "pdf_6684", "paper_title": "arXiv:1003.0921v1  [physics.ins-det]  3 Mar 2010Experimental Setup for the Measurement of the", "chunk_text": "High density\ndata of temperature sweeps from 2 to 350 K can be acquired in unde r 16 hours and\nhigh density data of isothermal ﬁeld sweeps from 0 to 140 kOe can be obtained in\nunder 2 hours. Calibrations for the system have been performed o n a platinum wire\nand Bi 2Sr2CaCu2O8+δhighTcsuperconductors. The measured TEP of phosphor-\nbronze (voltage lead wire) turns to be very small, where the absolut e TEP value of\nphosphor-bronzewireismuch lessthan 0. 5 µV/Kbelow 80K.", "found_variables": ["temperature", "temp", "thermal", "TEP"], "variable_count": 4, "chunk_length": 470}, "score": 0.4795214533805847, "query": "Purge Rate and Stripper Level"}, {"chunk": {"chunk_id": "1701.06551v1_chunk_2", "paper_id": "1701.06551v1", "paper_title": "On the Parametric Study of Lubricating Oil Production using an\nArtificial Neural Network (ANN) Approach", "chunk_text": "A feed-forward Multi-Layer Perceptron Neural Network was\nsuccessfully applied to capture the relationship between inputs and output\nparameters.", "found_variables": ["work", "A Feed", "input", "feed"], "variable_count": 4, "chunk_length": 143}, "score": 0.4790586531162262, "query": "Purge Rate Stripper Level interaction"}, {"chunk": {"chunk_id": "pdf_6362_chunk_0", "paper_id": "pdf_6362", "paper_title": "Correlation between radiation damage and magnetic properties in reactor vessel", "chunk_text": "Correlation between radiation damage and magnetic properties in reactor vessel", "found_variables": ["reactor"], "variable_count": 1, "chunk_length": 78}, "score": 0.****************, "query": "correlation Purge Rate Stripper Level"}, {"chunk": {"chunk_id": "2103.12222v1_chunk_2", "paper_id": "2103.12222v1", "paper_title": "Explainability: Relevance based Dynamic Deep Learning Algorithm for\nFault Detection and Diagnosis in Chemical Processes", "chunk_text": "The explainability is quantified by a novel relevance\nmeasure of input variables that is calculated from a Layerwise Relevance\nPropagation (LRP) algorithm. It is shown that the relevances can be used to\ndiscard redundant input feature vectors/ variables iteratively thus resulting\nin reduced over-fitting of noisy data, increasing distinguishability between\noutput classes and superior FDD test accuracy. The efficacy of the proposed\nmethod is demonstrated on the benchmark Tennessee Eastman Process.", "found_variables": ["rate", "input", "Tennessee Eastman"], "variable_count": 3, "chunk_length": 500}, "score": 0.*****************, "query": "correlation Purge Rate Stripper Level"}, {"chunk": {"chunk_id": "pdf_9386_chunk_2", "paper_id": "pdf_9386", "paper_title": "On the Parametric Study of Lubricating Oil Production using an ", "chunk_text": "A feed-forward Multi -Layer Perceptron Neural Network was successfully \napplied to capture the relationship b etween  inputs and output parameters.\n\nKeywords: Solvent extraction,  Lubricating oil, Machine Learning, Artificial Neural Network  \n(ANN)", "found_variables": ["input", "work", "vent", "A Feed", "feed"], "variable_count": 5, "chunk_length": 248}, "score": 0.4711824953556061, "query": "Purge Rate Stripper Level interaction"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}