{"variable_pair": ["Reactor Feed Rate", "Component D Purge Gas"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Reactor Feed Rate → Component D Purge Gas", "association_votes": {"A": 3, "?": 5, "IA": 7}, "direction_votes": {"Reactor Feed Rate → Component D Purge Gas": 10}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "1807.03623v1_chunk_0", "chunk_text": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "retrieval_score": 0.5857357978820801, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "1807.03623v1_chunk_1", "chunk_text": "This paper reports on experimental measurements of the gas temperature in a\ndielectric barrier discharge reactor powered by a high voltage pulsed signal. It is shown that the thermal behavior of the reactor follows a first order\nmodel. However, an unexpected runaway phenomenon was observed at a frequency of\n300Hz. A sudden increase in the power source and consequently in reactor\ntemperature which reaches 170{\\deg}C is observed.", "retrieval_score": 0.563843846321106, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Component D Purge Gas"}, {"chunk_id": "pdf_927_chunk_1", "chunk_text": "This paper reports on experimental measurements of the gas temperature in a dielectric barrier \ndischarge reactor powered by a high voltage pulsed signal. It is shown that the thermal \nbehavior of the reactor follows a first order model. However, an unexpected runaway \nphenomenon was observed at a frequency of 300Hz. A sudden increase in the power source \nand consequently in reactor temperature which reach es 170°C is observed.", "retrieval_score": 0.560464084148407, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2107.08078v1_chunk_4", "chunk_text": "Our sensitivity\nanalysis indicates that updating the infeed rate of the system, the processing\nspeed of equipment, and bale sequencing based on the moisture level of biomass\nimproves the processing rate of the reactor and reduces operating costs.", "retrieval_score": 0.5546927452087402, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Feed Rate → Component D Purge Gas"}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5477976202964783, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Component D Purge Gas"}, {"chunk_id": "pdf_2177_chunk_2", "chunk_text": "Eksioglub, <PERSON>ushi <PERSON>c,\naEnergy and Environment Science & Technology Department, Idaho National Laboratory, Idaho Falls, ID\nbIndustrial Engineering Department, University of Arkansas, Fayetteville, AR\ncGlenn Department of Civil Engineering, Clemson University, Clemson, SC\nAbstract\nThe variations in feedstock characteristics such as moisture and particle size distribution\nlead to an inconsistent ﬂow of feedstock from the biomass pre-processing system to the reactor\nin-feed system.", "retrieval_score": 0.5437883138656616, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Component D Purge Gas"}, {"chunk_id": "pdf_7578_chunk_0", "chunk_text": "Correlation between radiation damage and magnetic properties in reactor vessel", "retrieval_score": 0.5434753894805908, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2103.05025v1_chunk_0", "chunk_text": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate", "retrieval_score": 0.5308631658554077, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Component D Purge Gas"}, {"chunk_id": "pdf_927_chunk_0", "chunk_text": "1\n\n1 \nTemperature Runaway in a Pulsed Dielectric Barrier Discharge \nReactor\n\nH.Sad<PERSON> 1,  <PERSON><PERSON> 1, <PERSON><PERSON><PERSON><PERSON> 2\n\n1. Institut PPRIME, UPR CNRS 3346  \n2. Laboratoire de Catalyse en Chimie Organique, UMR CNRS 6503\n\nUniversité de Poitiers, Ecole Supérieure d'Ingénieu rs de Poitiers,  \n40, Avenue du Recteur Pineau, 86022 Poitiers (Franc e)\n\nAbstract :", "retrieval_score": 0.5263122916221619, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Feed Rate → Component D Purge Gas"}, {"chunk_id": "pdf_6125_chunk_5", "chunk_text": "The LLRF uses four approximate programme d curves to control the acceleration \nprocess: FREQ, Bias, ROF, and RAG. With  the input of the phase error from phase FERMILAB-TM-2271-AD \n 2feedback of LLRF, VXI LLRF generates the LO -Freq using the FREQ program in such a \nway that the LO-Freq has the same frequenc y and a constant phase difference with the \nCB through a cycle. The radial feedba.", "retrieval_score": 0.5196573138237, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Component D Purge Gas"}, {"chunk_id": "pdf_2177_chunk_3", "chunk_text": "These inconsistencies result in low on-stream times at the reactor in-feed\nequipment. This research develops an optimal process control method for a biomass pre-\nprocessing system comprised of milling and densiﬁcation operations to provide the consistent\nﬂow of feedstock to a reactor’s throat. This method uses a mixed-integer optimization\nmodel to identify optimal bale sequencing, equipment in-feed rate, and buﬀer location and\nsize in the biomass pre-processing system.", "retrieval_score": 0.5139272212982178, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Component D Purge Gas"}, {"chunk_id": "pdf_6125_chunk_3", "chunk_text": "Introduction \nThe proton beam is accelerated from 400 MeV to  8 GeV in Booster in a time of 33. 3 ms, \nwhile the RF frequency sweeps from 37. 8 MHz to 52. 9 MHz. The low-level RF system (LLRF) together with the bias running clos ed loop maintains the correct phase relation \nbetween the circulating beam bunches (CB) a nd the accelerating gap vo ltage. [1]  The RF \nphase angle is continuously adjusted to mainta in the required rate of energy gain.", "retrieval_score": 0.5127983093261719, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2206.00751v1_chunk_0", "chunk_text": "Disturbance Observer Based Frequency & Voltage Regulation for RES\nIntegrated Uncertain Power Systems", "retrieval_score": 0.5033532977104187, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_7578_chunk_5", "chunk_text": "Key Word s: reactorpressure vessel (RPV), neutron irradiation, dose -rate effects,\nmagnetic properties of steels. 1. Introduction\nTheintegrity of the components of power plant reactors throughout their service life is\naffected by the degradation suffered by t.", "retrieval_score": 0.4988340735435486, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Component D Purge Gas"}, {"chunk_id": "2103.05025v1_chunk_1", "chunk_text": "The variations in feedstock characteristics such as moisture and particle\nsize distribution lead to an inconsistent flow of feedstock from the biomass\npre-processing system to the reactor in-feed system. These inconsistencies\nresult in low on-stream times at the reactor in-feed equipment. This research\ndevelops an optimal process control method for a biomass pre-processing system\ncomprised of milling and densification operations to provide the consistent\nflow of feedstock to a reactor's throat.", "retrieval_score": 0.4981629252433777, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Feed Rate → Component D Purge Gas"}], "original_retrieval_results": 15}