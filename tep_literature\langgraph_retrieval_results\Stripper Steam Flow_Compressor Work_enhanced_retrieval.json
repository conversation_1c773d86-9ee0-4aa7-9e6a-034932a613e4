{"variable_pair": ["Stripper Steam Flow", "Compressor Work"], "results": [{"chunk": {"chunk_id": "1706.02043v1_chunk_3", "paper_id": "1706.02043v1", "paper_title": "Reduced chemistry for butanol isomers at engine-relevant conditions", "chunk_text": "The performance of the skeletal\nmechanisms generated here was compared with that of detailed mechanisms in\nsimulations of autoignition delay times, laminar flame speeds, and perfectly\nstirred reactor temperature response curves and extinction residence times,\nover a wide range of pressures, temperatures, and equivalence ratios.", "found_variables": ["temperature", "pressure", "rate", "reactor", "Reactor Temperature", "press", "temp"], "variable_count": 7, "chunk_length": 329}, "score": 0.5099217295646667, "query": "correlation Stripper Steam Flow Compressor Work"}, {"chunk": {"chunk_id": "pdf_9657_chunk_0", "paper_id": "pdf_9657", "paper_title": "Transmission Expansion Planning Using Cycle Flows", "chunk_text": "Transmission Expansion Planning Using Cycle Flows", "found_variables": ["flow"], "variable_count": 1, "chunk_length": 49}, "score": 0.5091050863265991, "query": "Stripper Steam Flow and Compressor Work"}, {"chunk": {"chunk_id": "2004.08702v1_chunk_0", "paper_id": "2004.08702v1", "paper_title": "Transmission Expansion Planning Using Cycle Flows", "chunk_text": "Transmission Expansion Planning Using Cycle Flows", "found_variables": ["flow"], "variable_count": 1, "chunk_length": 49}, "score": 0.5091050863265991, "query": "Stripper Steam Flow and Compressor Work"}, {"chunk": {"chunk_id": "2103.05025v1_chunk_1", "paper_id": "2103.05025v1", "paper_title": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate", "chunk_text": "The variations in feedstock characteristics such as moisture and particle\nsize distribution lead to an inconsistent flow of feedstock from the biomass\npre-processing system to the reactor in-feed system. These inconsistencies\nresult in low on-stream times at the reactor in-feed equipment. This research\ndevelops an optimal process control method for a biomass pre-processing system\ncomprised of milling and densification operations to provide the consistent\nflow of feedstock to a reactor's throat.", "found_variables": ["feed", "reactor", "flow", "process control"], "variable_count": 4, "chunk_length": 499}, "score": 0.502537727355957, "query": "Stripper Steam Flow and Compressor Work"}, {"chunk": {"chunk_id": "pdf_1321_chunk_4", "paper_id": "pdf_1321", "paper_title": "Reduced chemistry for butanol isomers at", "chunk_text": "The performances of the skeletal mechanisms generated\nhere were compared with those of detailed mechanisms in simulations of autoignition\ndelay times, laminar ﬂame speeds, and perfectly stirred reactor temperature response\ncurves and extinction residence times, over a wide range of pressures, temperatures,\n1arXiv:1706. 02043v1  [physics. chem-ph]  7 Jun 2017\nand equivalence ratios.", "found_variables": ["temperature", "pressure", "rate", "reactor", "Reactor Temperature", "press", "temp"], "variable_count": 7, "chunk_length": 384}, "score": 0.4987392723560333, "query": "correlation Stripper Steam Flow Compressor Work"}, {"chunk": {"chunk_id": "pdf_9328_chunk_3", "paper_id": "pdf_9328", "paper_title": "Two-stage Hydrogen Compression Using Zr-based Metal Hydrides ", "chunk_text": "The purpos e of this study is to find a series of alloys promptly forming \nmetal hydrides (MH) with suitable properties in  order to build a MH-based hydrogen compressor, \nworking in the same way between 20 and ~100 oC.", "found_variables": ["press", "work", "compressor"], "variable_count": 3, "chunk_length": 219}, "score": 0.4928584098815918, "query": "Stripper Steam Flow and Compressor Work"}, {"chunk": {"chunk_id": "1207.3541v1_chunk_2", "paper_id": "1207.3541v1", "paper_title": "Two-stage Hydrogen Compression Using Zr-based Metal Hydrides", "chunk_text": "The purpose of this study\nis to find a series of alloys promptly forming metal hydrides (MH) with\nsuitable properties in order to build a MH-based hydrogen compressor, working\nin the same way between 20 and ~100 oC.", "found_variables": ["press", "work", "compressor"], "variable_count": 3, "chunk_length": 215}, "score": 0.49285605549812317, "query": "Stripper Steam Flow and Compressor Work"}, {"chunk": {"chunk_id": "2301.11728v1_chunk_3", "paper_id": "2301.11728v1", "paper_title": "From partial data to out-of-sample parameter and observation estimation\nwith Diffusion Maps and Geometric Harmonics", "chunk_text": "inlet\ntemperature); (b) process parameters given observation data; and (c) partial\nobservations (e. g. temperature in the reactor) given other partial observations\n(e. g. mass fraction in the reactor). The proposed workflow relies on the\nmanifold learning schemes Diffusion Maps and the associated Geometric\nHarmonics. Diffusion Maps is used for discovering a reduced representation of\nthe available data, and Geometric Harmonics for extending functions defined on\nthe manifold.", "found_variables": ["temperature", "inlet", "flow", "reactor", "work", "temp"], "variable_count": 6, "chunk_length": 478}, "score": 0.49175533652305603, "query": "Stripper Steam Flow and Compressor Work"}, {"chunk": {"chunk_id": "pdf_2825_chunk_3", "paper_id": "pdf_2825", "paper_title": "Accelerated Methods with Compression for Horizontal", "chunk_text": "Therefore, it is crucial\nto enhance current methods with strategies that minimize the amount of data transmitted\nduring training while still achieving a model of similar quality. This paper introduces two\naccelerated algorithms with various compressors, working in the regime of horizontal and\nvertical data division.", "found_variables": ["press", "work", "rate", "compressor"], "variable_count": 4, "chunk_length": 317}, "score": 0.4896933436393738, "query": "Stripper Steam Flow and Compressor Work"}, {"chunk": {"chunk_id": "pdf_8896_chunk_3", "paper_id": "pdf_8896", "paper_title": "Optimal Control to Handle Variations in Biomass Feedstock", "chunk_text": "These inconsistencies result in low on-stream times at the reactor in-feed\nequipment. This research develops an optimal process control method for a biomass pre-\nprocessing system comprised of milling and densiﬁcation operations to provide the consistent\nﬂow of feedstock to a reactor’s throat. This method uses a mixed-integer optimization\nmodel to identify optimal bale sequencing, equipment in-feed rate, and buﬀer location and\nsize in the biomass pre-processing system.", "found_variables": ["feed", "reactor", "rate", "process control"], "variable_count": 4, "chunk_length": 473}, "score": 0.4867038428783417, "query": "Stripper Steam Flow and Compressor Work"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}