{"variable_pair": ["<PERSON> Feed", "Stripper Pressure"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "D Feed → Stripper Pressure", "association_votes": {"?": 2, "⊥": 5, "IA": 8}, "direction_votes": {"D Feed → Stripper Pressure": 5, "D Feed - Stripper Pressure": 3}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5841200351715088, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed → Stripper Pressure"}, {"chunk_id": "2403.10842v4_chunk_0", "chunk_text": "Twin Transformer using Gated Dynamic Learnable Attention mechanism for\nFault Detection and Diagnosis in the Tennessee Eastman Process", "retrieval_score": 0.5419990420341492, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_819_chunk_0", "chunk_text": "Geometric Decomposition of Feed Forward", "retrieval_score": 0.5419106483459473, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed → Stripper Pressure"}, {"chunk_id": "pdf_131_chunk_0", "chunk_text": "arXiv:2303.05904v1  [cs.LG]  10 Mar 2023Deep Anomaly Detection on Tennessee Eastman Process Data", "retrieval_score": 0.5398014783859253, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed - Stripper Pressure"}, {"chunk_id": "pdf_3805_chunk_2", "chunk_text": "Abstract: Fault detection and diagnosis (FDD) is a crucial task for ensuring the safety and \nefficiency of industrial processes. We propose a novel FDD methodology for the Tennessee \nEastman Process (TEP), a widely used benchmark for chemical process control. The model \nemploys two separate Transformer branches, enabling independent processing of input data and \npotential extraction of diverse information.", "retrieval_score": 0.525210440158844, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_3937_chunk_3", "chunk_text": "We demon-\nstrate our model’s performance on two publicly available datasets of the Tennessee\nEastman Process with various faults. The results show that our method significantly\noutperforms existing approaches (+0. 2-0. 3 TPR for a fixed FPR) and effectively de-\ntects most of the process faults without expert annotation. Moreover, we show that the\nmodel fine-tuned on a small fraction of labeled data nearly reaches the performance of\na SOTA model trained on the full dataset.", "retrieval_score": 0.5222601890563965, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed → Stripper Pressure"}, {"chunk_id": "2403.10842v4_chunk_1", "chunk_text": "Fault detection and diagnosis (FDD) is a crucial task for ensuring the safety\nand efficiency of industrial processes. We propose a novel FDD methodology for\nthe Tennessee Eastman Process (TEP), a widely used benchmark for chemical\nprocess control. The model employs two separate Transformer branches, enabling\nindependent processing of input data and potential extraction of diverse\ninformation.", "retrieval_score": 0.5182747840881348, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2301.11573v3_chunk_3", "chunk_text": "The theoretical analysis is verified through\nMonte Carlo simulations and Tennessee Eastman process (TEP) dataset.", "retrieval_score": 0.509455680847168, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.5086565017700195, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2212.04140v2_chunk_3", "chunk_text": "The effectiveness of the switching strategy is\nalso demonstrated via numerical simulation on the Tennessee Eastman Process.", "retrieval_score": 0.5081184506416321, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2210.08538v1_chunk_0", "chunk_text": "Advantages of OKID-ERA Identification in Control Systems. An Application\nto the Tennessee Eastman Plant", "retrieval_score": 0.5057374238967896, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed - Stripper Pressure"}, {"chunk_id": "pdf_6373_chunk_0", "chunk_text": "Linear Time Algorithm for Optimal Feed-link Placement", "retrieval_score": 0.5054026246070862, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "1208.0395v2_chunk_0", "chunk_text": "Linear Time Algorithm for Optimal Feed-link Placement", "retrieval_score": 0.5054025650024414, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed - Stripper Pressure"}, {"chunk_id": "2309.11594v1_chunk_0", "chunk_text": "Development of a Feeding Assistive Robot Using a Six Degree of Freedom\nRobotic Arm", "retrieval_score": 0.5053625702857971, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed → Stripper Pressure"}, {"chunk_id": "2403.11722v2_chunk_3", "chunk_text": "We\napply our proposed compression method on the Tennessee Eastman Dataset, where\nwe perform fault classification using the compressed data in two settings: a\nfully supervised one and in a semi supervised, contrastive learning setting. Both times, we were able to outperform real valued counterparts as well as two\nbaseline models: one with the uncompressed time-series as the input and the\nother with a regular downsampling using the mean.", "retrieval_score": 0.5049315690994263, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed → Stripper Pressure"}], "original_retrieval_results": 15}