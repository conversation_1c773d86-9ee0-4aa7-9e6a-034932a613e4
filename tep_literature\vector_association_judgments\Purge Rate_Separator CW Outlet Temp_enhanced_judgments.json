{"variable_pair": ["Purge Rate", "Separator CW Outlet Temp"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Purge Rate → Separator CW Outlet Temp", "association_votes": {"A": 3, "⊥": 2, "IA": 9, "?": 1}, "direction_votes": {"Purge Rate → Separator CW Outlet Temp": 11, "Purge Rate - Separator CW Outlet Temp": 1}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "1807.03623v1_chunk_0", "chunk_text": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "retrieval_score": 0.5511415600776672, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Separator CW Outlet Temp"}, {"chunk_id": "2212.01432v1_chunk_1", "chunk_text": "Tungsten (W) is a material of choice for the divertor material due to its\nhigh melting temperature, thermal conductivity, and sputtering threshold. However, W has a very high brittle-to-ductile transition temperature and at\nfusion reactor temperatures ($\\geq$1000K) may undergo recrystallization and\ngrain growth.", "retrieval_score": 0.5346762537956238, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Separator CW Outlet Temp"}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5331236720085144, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Separator CW Outlet Temp"}, {"chunk_id": "1709.02232v1_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5290550589561462, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate - Separator CW Outlet Temp"}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5290549993515015, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Separator CW Outlet Temp"}, {"chunk_id": "1003.0921v1_chunk_2", "chunk_text": "High density data\nof temperature sweeps from 2 to 350 K can be acquired in under 16 hours and\nhigh density data of isothermal field sweeps from 0 to 140 kOe can be obtained\nin under 2 hours. Calibrations for the system have been performed on a platinum\nwire and Bi$_{2}$Sr$_{2}$CaCu$_{2}$O$_{8+\\delta}$ high $T_{c}$ superconductors. The measured TEP of phosphor-bronze (voltage lead wire) turns to be very small,\nwhere the absolute TEP value of phosphor-bronze wire is much less than 0.", "retrieval_score": 0.5276316404342651, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Separator CW Outlet Temp"}, {"chunk_id": "pdf_7856_chunk_5", "chunk_text": "In the second stage, a reconstruction error-based threshold approach using the trained\nGAWNO is employed to detect and isolate faults based on the discrepancy values. We validate the\nproposed approach using the Tennessee Eastman Process (TEP) dataset and Avedore wastewater\ntreatment plant (WWTP) and N2Oemissions nam.", "retrieval_score": 0.5260642766952515, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.5260248184204102, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Purge Rate → Separator CW Outlet Temp"}, {"chunk_id": "pdf_1764_chunk_2", "chunk_text": "Thompson1\n1Center for Computing Research, Sandia National Laboratories, Albuquerque, New Mexico 87185, USA\n2Material, Physical, and Chemical Science Center, Sandia National Laboratories, Albuquerque, New Mexico 87185, USA\n3CEA, DES/IRESNE/DEC 13018 Saint Paul L ˜A¨s Durance, France\n(Dated: December 6, 2022)\nTungsten (W) is a material of choice for the divertor material due to its high melting temperature, thermal\nconductivity, and sputtering threshold.", "retrieval_score": 0.5237830877304077, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Separator CW Outlet Temp"}, {"chunk_id": "2401.04004v1_chunk_3", "chunk_text": "In the second stage, a reconstruction error-based threshold\napproach using the trained GAWNO is employed to detect and isolate faults based\non the discrepancy values. We validate the proposed approach using the\nTennessee Eastman Process (TEP) dataset and Avedore wastewater treatment plant\n(WWTP) and N2O emissions named as WWTPN2O datasets.", "retrieval_score": 0.5229213833808899, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "9809106v1_chunk_2", "chunk_text": "For samples of $\\sigma_{RT}$$>$20000 S/cm, the\n$\\rho (1. 5K)/\\rho (300K) <1$, i. e. , the resistivity at 1. 5K is lower than the\nroom temperature resistivity value. The temperature dependence of the TEP shows\ndiffusive linear metallic TEP becoming temperature independent below 40K. Unlike the others who used Cu(ClO_4)_2 for the ClO_4^- doping, the initial\ndoping material we used is anhydrous Fe(ClO_4)_3 which is crucial to obtain the\npositive TCR from T=1. 5K to 300K.", "retrieval_score": 0.5221636295318604, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Separator CW Outlet Temp"}, {"chunk_id": "2401.08380v1_chunk_1", "chunk_text": "We report measurements of the in-plane thermoelectric power (TEP) for an\noverdoped (OD) crystal of the single layer cuprate superconductor\nTl$_2$Ba$_2$CuO$_{6+x}$ (Tl2201) at several hole concentrations ($p$), from 300\nor 400 K to below the superconducting transition temperature ($T_c$). For $p$ =\n0. 192 and 0. 220, small upturns in the TEP below 150 K are attributed to the\npresence of charge density waves (CDW) detected by resonant inelastic X-ray\nscattering studies.", "retrieval_score": 0.5196040272712708, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_9787_chunk_6", "chunk_text": "Experimental validation on both the Tennessee Eastman \nProcess (TEP) and the Secure Water Treatment (SWaT) \ntestbeds demonstrates the framework’s superior performan ce. A comprehensive ablation study, supported by extensive visualizations including Receiver Operatin.", "retrieval_score": 0.5184541344642639, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Purge Rate → Separator CW Outlet Temp"}, {"chunk_id": "pdf_6959_chunk_5", "chunk_text": "Simulation results demonstrate the efficacy of this approach, showcasing superior performance in \naccuracy, false alarm rate, and misclassifica tion rate compared to existing methods. This \nmethodology provides a robust and interpretable solution for fault detection and diagnosis in the \nTEP, highlighting its potential for industrial applications. Keywords. Fault Detection, Tennessee Eastman Process, Supervised Feature Selection, Attention \nMechanism, Bidirectional Long Short -Term Memory.", "retrieval_score": 0.5182369947433472, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Separator CW Outlet Temp"}, {"chunk_id": "2212.12092v1_chunk_5", "chunk_text": "Finally, we use the benchmark Tennessee Eastman to\nperform experiments to test the ensemble classifier's prediction and anomaly\ndetection capabilities.", "retrieval_score": 0.5163355469703674, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Purge Rate → Separator CW Outlet Temp"}], "original_retrieval_results": 15}