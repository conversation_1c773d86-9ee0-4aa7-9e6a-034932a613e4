{"variable_pair": ["Recycle Flow", "Product Sep Level"], "results": [{"chunk": {"chunk_id": "2208.08879v2_chunk_0", "paper_id": "2208.08879v2", "paper_title": "SensorSCAN: Self-Supervised Learning and Deep Clustering for Fault\nDiagnosis in Chemical Processes", "chunk_text": "SensorSCAN: Self-Supervised Learning and Deep Clustering for Fault\nDiagnosis in Chemical Processes", "found_variables": ["chemical process"], "variable_count": 1, "chunk_length": 98}, "score": 0.5154985189437866, "query": "Recycle Flow and Product Sep Level"}, {"chunk": {"chunk_id": "pdf_2191_chunk_3", "paper_id": "pdf_2191", "paper_title": "IEEE TRANSACTIONS ON INFORMATION THEORY 1", "chunk_text": "We also reformulate the Tree-EP algorithm for the binary\nerasure channel (BEC) as a peeling-type algorithm (TEP) and we\nshow that the algorithm has the same computational complexity\nas BP and it decodes a higher fraction of errors. We describe\nthe TEP decoding process by a set of differential equations that\nrepresents the expected residual graph evolution as a function\nof the code parameters.", "found_variables": ["TEP"], "variable_count": 1, "chunk_length": 395}, "score": 0.5134233832359314, "query": "Recycle Flow and Product Sep Level"}, {"chunk": {"chunk_id": "1201.0715v3_chunk_3", "paper_id": "1201.0715v3", "paper_title": "Tree-Structure Expectation Propagation for LDPC Decoding over the BEC", "chunk_text": "We also reformulate\nthe Tree-EP algorithm for the binary erasure channel (BEC) as a peeling-type\nalgorithm (TEP) and we show that the algorithm has the same computational\ncomplexity as BP and it decodes a higher fraction of errors. We describe the\nTEP decoding process by a set of differential equations that represents the\nexpected residual graph evolution as a function of the code parameters.", "found_variables": ["TEP"], "variable_count": 1, "chunk_length": 395}, "score": 0.5134233832359314, "query": "Recycle Flow and Product Sep Level"}, {"chunk": {"chunk_id": "2406.09186v2_chunk_0", "paper_id": "2406.09186v2", "paper_title": "A formation pathway for terrestrial planets with moderate water content\ninvolving atmospheric-volatile recycling", "chunk_text": "A formation pathway for terrestrial planets with moderate water content\ninvolving atmospheric-volatile recycling", "found_variables": ["rate"], "variable_count": 1, "chunk_length": 112}, "score": 0.5113110542297363, "query": "Recycle Flow and Product Sep Level"}, {"chunk": {"chunk_id": "1201.0715v3_chunk_4", "paper_id": "1201.0715v3", "paper_title": "Tree-Structure Expectation Propagation for LDPC Decoding over the BEC", "chunk_text": "The\nsolution of these equations is used to predict the TEP decoder performance in\nboth the asymptotic regime and the finite-length regime over the BEC. While the\nasymptotic threshold of the TEP decoder is the same as the BP decoder for\nregular and optimized codes, we propose a scaling law (SL) for finite-length\nLDPC codes, which accurately approximates the TEP improved performance and\nfacilitates its optimization.", "found_variables": ["TEP", "rate"], "variable_count": 2, "chunk_length": 417}, "score": 0.5064713358879089, "query": "Recycle Flow and Product Sep Level"}, {"chunk": {"chunk_id": "pdf_2191_chunk_4", "paper_id": "pdf_2191", "paper_title": "IEEE TRANSACTIONS ON INFORMATION THEORY 1", "chunk_text": "The solution of these equations is used\nto predict the TEP decoder performance in both the asymptotic\nregime and the ﬁnite-length regime over the BEC. While the\nasymptotic threshold of the TEP decoder is the same as the\nBP decoder for regular and optimized codes, we propose a\nscaling law (SL) for ﬁnite-length LDPC codes, which accurately\napproximates the TEP improved performance and facilitates its\noptimization. I.", "found_variables": ["TEP", "rate"], "variable_count": 2, "chunk_length": 418}, "score": 0.5055617094039917, "query": "Recycle Flow and Product Sep Level"}, {"chunk": {"chunk_id": "pdf_1839_chunk_4", "paper_id": "pdf_1839", "paper_title": "Draft version July 10, 2024", "chunk_text": "Here, we reevaluate this line of reasoning by incorporating recycling flows of gas\ninto a numerical one-dimensional thermodynamic model with more realistic equation of state and\nopacities and the thermal state of the advective flow. At 0. 1 AU, we find that advective flows are\nonly able to produce mini-Neptunes if they can penetrate below ∼0. 25 of the planet’s gravitational\nsphere of influence.", "found_variables": ["rate", "flow", "thermal"], "variable_count": 3, "chunk_length": 398}, "score": 0.5053951740264893, "query": "Recycle Flow and Product Sep Level"}, {"chunk": {"chunk_id": "pdf_764_chunk_4", "paper_id": "pdf_764", "paper_title": "MNRAS 000, 1{24 (2021) Preprint 3 September 2021 Compiled using MNRAS L ATEX style \fle v3.0", "chunk_text": "Using a passive scalar, we observe sig-\nni\fcant mass recycling on the orbital timescale. For a radiative envelope, recycling\ncan only penetrate from the disc surface until \u00180. 1-0. 2 planetary Hill radii, while for\na convective envelope, the convective motion can \\dredge up\" the deeper part of the\nenvelope so that the entire convective envelope is recycled e\u000eciently. This recycling,\nhowever, has only limited e\u000bects on the envelopes' thermal structure.", "found_variables": ["rate", "thermal"], "variable_count": 2, "chunk_length": 455}, "score": 0.5052065849304199, "query": "Recycle Flow and Product Sep Level"}, {"chunk": {"chunk_id": "pdf_6281_chunk_2", "paper_id": "pdf_6281", "paper_title": "1 ", "chunk_text": "High shares of renewable reso urces a re \nintegrated in to the proposed hybrid AC/DC TEP model. Due to \nthe intermittency of renewable resources, the planning of large -\nscale Energy Storage (ES) devices is considered. In order to \naccurately estimate the total TEP costs and hence capturing the \nscenarios of load and renewable generation uncertainty, using a \nclustering approach, each year of the planning horizon is replaced \nwith four representative days.", "found_variables": ["TEP", "rate"], "variable_count": 2, "chunk_length": 460}, "score": 0.5049761533737183, "query": "Recycle Flow and Product Sep Level"}, {"chunk": {"chunk_id": "pdf_8878_chunk_1", "paper_id": "pdf_8878", "paper_title": "Typeset using L ATEXtwocolumn style in AASTeX631", "chunk_text": "Typeset using L ATEXtwocolumn style in AASTeX631\nGrowing Planet Envelopes in Spite of Recycling Flows\nAvery P.", "found_variables": ["flow"], "variable_count": 1, "chunk_length": 110}, "score": 0.502488374710083, "query": "Recycle Flow and Product Sep Level"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}