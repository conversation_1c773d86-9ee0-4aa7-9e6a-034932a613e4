{"variable_pair": ["Reactor Feed Rate", "Component E Purge Gas"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Reactor Feed Rate → Component E Purge Gas", "association_votes": {"A": 4, "?": 4, "IA": 6, "⊥": 1}, "direction_votes": {"Reactor Feed Rate - Component E Purge Gas": 3, "Reactor Feed Rate → Component E Purge Gas": 7}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "2107.08078v1_chunk_4", "chunk_text": "Our sensitivity\nanalysis indicates that updating the infeed rate of the system, the processing\nspeed of equipment, and bale sequencing based on the moisture level of biomass\nimproves the processing rate of the reactor and reduces operating costs.", "retrieval_score": 0.5687991380691528, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Feed Rate → Component E Purge Gas"}, {"chunk_id": "pdf_2177_chunk_2", "chunk_text": "Eksioglub, <PERSON>ushi <PERSON>c,\naEnergy and Environment Science & Technology Department, Idaho National Laboratory, Idaho Falls, ID\nbIndustrial Engineering Department, University of Arkansas, Fayetteville, AR\ncGlenn Department of Civil Engineering, Clemson University, Clemson, SC\nAbstract\nThe variations in feedstock characteristics such as moisture and particle size distribution\nlead to an inconsistent ﬂow of feedstock from the biomass pre-processing system to the reactor\nin-feed system.", "retrieval_score": 0.5615451335906982, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "1807.03623v1_chunk_0", "chunk_text": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "retrieval_score": 0.5554685592651367, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Component E Purge Gas"}, {"chunk_id": "pdf_927_chunk_1", "chunk_text": "This paper reports on experimental measurements of the gas temperature in a dielectric barrier \ndischarge reactor powered by a high voltage pulsed signal. It is shown that the thermal \nbehavior of the reactor follows a first order model. However, an unexpected runaway \nphenomenon was observed at a frequency of 300Hz. A sudden increase in the power source \nand consequently in reactor temperature which reach es 170°C is observed.", "retrieval_score": 0.5477786064147949, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Component E Purge Gas"}, {"chunk_id": "2103.05025v1_chunk_0", "chunk_text": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate", "retrieval_score": 0.5476998090744019, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Feed Rate → Component E Purge Gas"}, {"chunk_id": "pdf_7578_chunk_0", "chunk_text": "Correlation between radiation damage and magnetic properties in reactor vessel", "retrieval_score": 0.5435717105865479, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Feed Rate - Component E Purge Gas"}, {"chunk_id": "1807.03623v1_chunk_1", "chunk_text": "This paper reports on experimental measurements of the gas temperature in a\ndielectric barrier discharge reactor powered by a high voltage pulsed signal. It is shown that the thermal behavior of the reactor follows a first order\nmodel. However, an unexpected runaway phenomenon was observed at a frequency of\n300Hz. A sudden increase in the power source and consequently in reactor\ntemperature which reaches 170{\\deg}C is observed.", "retrieval_score": 0.5415732860565186, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Feed Rate - Component E Purge Gas"}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5338006615638733, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Component E Purge Gas"}, {"chunk_id": "pdf_6125_chunk_3", "chunk_text": "Introduction \nThe proton beam is accelerated from 400 MeV to  8 GeV in Booster in a time of 33. 3 ms, \nwhile the RF frequency sweeps from 37. 8 MHz to 52. 9 MHz. The low-level RF system (LLRF) together with the bias running clos ed loop maintains the correct phase relation \nbetween the circulating beam bunches (CB) a nd the accelerating gap vo ltage. [1]  The RF \nphase angle is continuously adjusted to mainta in the required rate of energy gain.", "retrieval_score": 0.5279044508934021, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Component E Purge Gas"}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.5218379497528076, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_2177_chunk_3", "chunk_text": "These inconsistencies result in low on-stream times at the reactor in-feed\nequipment. This research develops an optimal process control method for a biomass pre-\nprocessing system comprised of milling and densiﬁcation operations to provide the consistent\nﬂow of feedstock to a reactor’s throat. This method uses a mixed-integer optimization\nmodel to identify optimal bale sequencing, equipment in-feed rate, and buﬀer location and\nsize in the biomass pre-processing system.", "retrieval_score": 0.5198248624801636, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Component E Purge Gas"}, {"chunk_id": "1911.04386v2_chunk_4", "chunk_text": "The\noutstanding performance of this method is demonstrated and contrasted to\n(dynamic) principal component analysis, which are widely applied in the\nindustry, in the benchmark Tennessee Eastman process~(TEP) and a real chemical\nmanufacturing dataset.", "retrieval_score": 0.5170243978500366, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_9406_chunk_5", "chunk_text": "The performance of the method is demonstrated\nand contrasted to (dynamic) principal component analysis, which is widely applied in the industry,\nin the benchmark Tennessee Eastman process (TEP) and a real chemical manufacturing dataset. Keywords: fault detection, fault identi\fcation, recurrent neural networks, variational dropout,\nBayesian inference, Tennessee Eastman process. 1.", "retrieval_score": 0.5113333463668823, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2210.08538v1_chunk_0", "chunk_text": "Advantages of OKID-ERA Identification in Control Systems. An Application\nto the Tennessee Eastman Plant", "retrieval_score": 0.509364664554596, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate - Component E Purge Gas"}, {"chunk_id": "1607.07423v3_chunk_5", "chunk_text": "We illustrate the\nsuccessful use of $K_T$ chart using the Tennessee Eastman process data.", "retrieval_score": 0.5085980892181396, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}], "original_retrieval_results": 15}