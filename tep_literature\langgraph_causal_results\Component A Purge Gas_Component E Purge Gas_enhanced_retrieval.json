{"variable_pair": ["Component A Purge Gas", "Component E Purge Gas"], "results": [{"chunk": {"chunk_id": "1712.04118v1_chunk_0", "paper_id": "1712.04118v1", "paper_title": "Neural Component Analysis for Fault Detection", "chunk_text": "Neural Component Analysis for Fault Detection", "found_variables": ["component", "fault detection"], "variable_count": 2, "chunk_length": 45}, "score": 0.5043039917945862, "query": "correlation Component A Purge Gas Component E Purge Gas"}, {"chunk": {"chunk_id": "pdf_8813_chunk_1", "paper_id": "pdf_8813", "paper_title": "1", "chunk_text": "1\nNeural Component Analysis for Fault Detection\nHaitao Zhao\nKey Laboratory of Advanced Control and Optimization for\nChemical Processes of Ministry of Education, School of\nInformation Science and Engineering, East China University\nof Science and Technology\nAbstract —Principal component analysis (PCA) is largely\nadopted for chemical process monitoring and numerous PCA-\nbased systems have been developed to solve various fault detection\nand diagnosis problems.", "found_variables": ["component", "fault detection", "chemical process"], "variable_count": 3, "chunk_length": 460}, "score": 0.4976308047771454, "query": "correlation Component A Purge Gas Component E Purge Gas"}, {"chunk": {"chunk_id": "pdf_6057_chunk_3", "paper_id": "pdf_6057", "paper_title": "A Neural Network Approach to ECG Denoising", "chunk_text": "1 Introduction\nThe ECG is often corrupted by di\u000berent types of noise, namely, power line inter-\nference, electrode contact and motion artifacts, respiration, electrical activity\nof muscles in the vicinity of the electrodes and interference from other elec-\ntronic devices. Analysis of noisy ECGs is di\u000ecult for humans and for computer\nprograms. In this work we place ourselves in context of automatic and semi\nautomatic ECG analysis: denoising should facilitate automatic ECG analysis.", "found_variables": ["work"], "variable_count": 1, "chunk_length": 485}, "score": 0.4936490058898926, "query": "correlation Component A Purge Gas Component E Purge Gas"}, {"chunk": {"chunk_id": "pdf_688_chunk_4", "paper_id": "pdf_688", "paper_title": "arXiv:0906.1548v2  [cond-mat.supr-con]  7 Aug 2009Thermoelectric power and Hall coeﬃcient measurements on", "chunk_text": "These changes possibly point to a signiﬁc ant modiﬁcation of the Fermi surface\n/ band structure of Ba(Fe 1−xTMx)2As2at small electron doping, that in the case of Co-doping is\njust before, and probably allows for, the onset of supercond uctivity. These data further suggest\nthat suppression of the structural / magnetic phase transit ion and the establishment of a proper\ne-value are each necessary but, individually, not suﬃcient c onditions for superconductivity. PACS numbers: 74. 70. Dd; 72. 15.", "found_variables": ["press"], "variable_count": 1, "chunk_length": 498}, "score": 0.4930535554885864, "query": "correlation Component A Purge Gas Component E Purge Gas"}, {"chunk": {"chunk_id": "pdf_688_chunk_3", "paper_id": "pdf_688", "paper_title": "arXiv:0906.1548v2  [cond-mat.supr-con]  7 Aug 2009Thermoelectric power and Hall coeﬃcient measurements on", "chunk_text": "For Co-dopingwe clearly see a change in thete mperaturedependent TEP andHall\ncoeﬃcient data when the sample is doped to suﬃcient e(the number of extra electrons associated\nwith the TMdoping) so as to stabilize low temperature superconductivi ty. Remarkably, a similar\nchange is found in the Cu-doped samples at comparable e-value, even though these compounds do\nnot superconduct.", "found_variables": ["temperature", "temp", "TEP"], "variable_count": 3, "chunk_length": 379}, "score": 0.4923243224620819, "query": "correlation Component A Purge Gas Component E Purge Gas"}, {"chunk": {"chunk_id": "pdf_7430_chunk_5", "paper_id": "pdf_7430", "paper_title": "arXiv:2205.05250v2  [cs.LG]  5 Oct 2023Spatial-temporal associations representation and applic ation for process", "chunk_text": "Finally, these networks corresponding to process sta tes at diﬀerent times are fed into a graph convolutional\nneural network to implement graph classiﬁcation to achieve process monitoring. A benchmark experiment (Ten-\nnessee Eastman chemical process) and one application study (cobalt puriﬁcation from zinc solution) are employed to\ndemonstrate the feasibility and applicability of this pape r. Keywords: Spatial-Temporal Associations Representation; Process M onitoring; Stat.", "found_variables": ["rate", "temp", "work", "chemical process"], "variable_count": 4, "chunk_length": 477}, "score": 0.4904927909374237, "query": "correlation Component A Purge Gas Component E Purge Gas"}, {"chunk": {"chunk_id": "pdf_9229_chunk_5", "paper_id": "pdf_9229", "paper_title": "1 ", "chunk_text": "Moreover, the increased number of gas colli sion can cause a large fraction of the input \npower to be dissipated in gas heati.", "found_variables": ["heat", "input"], "variable_count": 2, "chunk_length": 126}, "score": 0.4871380031108856, "query": "Component A Purge Gas influence Component E Purge Gas"}, {"chunk": {"chunk_id": "2310.06013v3_chunk_0", "paper_id": "2310.06013v3", "paper_title": "The Not-So Dramatic Effect of Advective Flows on Gas Accretion", "chunk_text": "The Not-So Dramatic Effect of Advective Flows on Gas Accretion", "found_variables": ["flow"], "variable_count": 1, "chunk_length": 62}, "score": 0.4864809811115265, "query": "effect of Component A Purge Gas on Component E Purge Gas"}, {"chunk": {"chunk_id": "2103.07303v1_chunk_0", "paper_id": "2103.07303v1", "paper_title": "Second-Order Component Analysis for Fault Detection", "chunk_text": "Second-Order Component Analysis for Fault Detection", "found_variables": ["component", "fault detection"], "variable_count": 2, "chunk_length": 51}, "score": 0.4851979613304138, "query": "Component A Purge Gas and Component E Purge Gas"}, {"chunk": {"chunk_id": "pdf_9229_chunk_4", "paper_id": "pdf_9229", "paper_title": "1 ", "chunk_text": "A \ngreat deal of experimental, theoretical and numeric al works has been conducted so far and \nhelped us to better understand these processes [6]. Whatever the concerned application, gas \ntemperature has a great influence on the electrical  parameters and on the nature and \nproduction of primary species formed by plasma  and must then play an important role. The increase of temperature leads to modify current  and voltage shapes as it has been shown \nin [7].", "found_variables": ["temperature", "temp", "work"], "variable_count": 3, "chunk_length": 462}, "score": 0.48379942774772644, "query": "Component A Purge Gas influence Component E Purge Gas"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}