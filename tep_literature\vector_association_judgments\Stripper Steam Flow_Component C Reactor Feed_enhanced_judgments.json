{"variable_pair": ["Stripper Steam Flow", "Component C Reactor Feed"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Stripper Steam Flow → Component C Reactor Feed", "association_votes": {"A": 3, "?": 2, "⊥": 3, "IA": 7}, "direction_votes": {"Stripper Steam Flow - Component C Reactor Feed": 4, "Stripper Steam Flow → Component C Reactor Feed": 6}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "pdf_2177_chunk_2", "chunk_text": "Eksioglub, <PERSON>ushi <PERSON>c,\naEnergy and Environment Science & Technology Department, Idaho National Laboratory, Idaho Falls, ID\nbIndustrial Engineering Department, University of Arkansas, Fayetteville, AR\ncGlenn Department of Civil Engineering, Clemson University, Clemson, SC\nAbstract\nThe variations in feedstock characteristics such as moisture and particle size distribution\nlead to an inconsistent ﬂow of feedstock from the biomass pre-processing system to the reactor\nin-feed system.", "retrieval_score": 0.5505789518356323, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_6635_chunk_3", "chunk_text": "The\nframework is illustrated via a computational model of the deposition of Cu\nin a Chemical Vapor Deposition (CVD) reactor, where the reactor pressure,\nthe deposition temperature and feed mass \row rate are important process\nparameters that determine the outcome of the process. The sampled ob-\nservations are high-dimensional vectors containing the outputs of a detailed\nCFD steady-state model of the process, i. e.", "retrieval_score": 0.5364028215408325, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Steam Flow - Component C Reactor Feed"}, {"chunk_id": "pdf_819_chunk_0", "chunk_text": "Geometric Decomposition of Feed Forward", "retrieval_score": 0.5355705618858337, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "1807.03623v1_chunk_0", "chunk_text": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "retrieval_score": 0.5262655019760132, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "0402435v1_chunk_0", "chunk_text": "The Indo-U.S. Library of Coude Feed Stellar Spectra", "retrieval_score": 0.5253584980964661, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Steam Flow → Component C Reactor Feed"}, {"chunk_id": "1703.08935v1_chunk_0", "chunk_text": "Security Constrained Multi-Stage Transmission Expansion Planning\nConsidering a Continuously Variable Series Reactor", "retrieval_score": 0.5173776745796204, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Steam Flow → Component C Reactor Feed"}, {"chunk_id": "2108.13381v1_chunk_1", "chunk_text": "In this paper, genetic programming reinforcement learning (GPRL) is utilized\nto generate human-interpretable control policies for a Chylla-Haase\npolymerization reactor. Such continuously stirred tank reactors (CSTRs) with\njacket cooling are widely used in the chemical industry, in the production of\nfine chemicals, pigments, polymers, and medical products. Despite appearing\nrather simple, controlling CSTRs in real-world applications is quite a\nchallenging problem to tackle.", "retrieval_score": 0.5102576017379761, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Steam Flow - Component C Reactor Feed"}, {"chunk_id": "pdf_2948_chunk_2", "chunk_text": "August 31, 2021\nAbstract\nIn this paper, genetic programming reinforcement learning (GPRL) is\nutilized to generate human-interpretable control policies for a Chylla-\nHaase polymerization reactor. Such continuously stirred tank reactors\n(CSTRs) with jacket cooling are widely used in the chemical industry,\nin the production of \fne chemicals, pigments, polymers, and medi-\ncal products.", "retrieval_score": 0.5066676735877991, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Steam Flow - Component C Reactor Feed"}, {"chunk_id": "2301.11728v1_chunk_3", "chunk_text": "inlet\ntemperature); (b) process parameters given observation data; and (c) partial\nobservations (e. g. temperature in the reactor) given other partial observations\n(e. g. mass fraction in the reactor). The proposed workflow relies on the\nmanifold learning schemes Diffusion Maps and the associated Geometric\nHarmonics. Diffusion Maps is used for discovering a reduced representation of\nthe available data, and Geometric Harmonics for extending functions defined on\nthe manifold.", "retrieval_score": 0.5027539730072021, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Steam Flow → Component C Reactor Feed"}, {"chunk_id": "2301.11728v1_chunk_1", "chunk_text": "A data-driven framework is presented, that enables the prediction of\nquantities, either observations or parameters, given sufficient partial data. The framework is illustrated via a computational model of the deposition of Cu\nin a Chemical Vapor Deposition (CVD) reactor, where the reactor pressure, the\ndeposition temperature and feed mass flow rate are important process parameters\nthat determine the outcome of the process.", "retrieval_score": 0.5019908547401428, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Stripper Steam Flow → Component C Reactor Feed"}, {"chunk_id": "pdf_7578_chunk_0", "chunk_text": "Correlation between radiation damage and magnetic properties in reactor vessel", "retrieval_score": 0.5017220973968506, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2003.00264v2_chunk_3", "chunk_text": "To\ndemonstrate its applicability and efficiency, the proposed fault diagnosis\nmethod is applied to process monitoring of continuous stirred tank reactor\n(CSTR) and Tennessee Eastman (TE) process. The proposed QC-based deep learning\napproach enjoys superior fault detection and diagnosis performance with\nobtained average fault detection rates of 79. 2% and 99. 39% for CSTR and TE\nprocess, respectively.", "retrieval_score": 0.4996151328086853, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Steam Flow - Component C Reactor Feed"}, {"chunk_id": "pdf_9114_chunk_0", "chunk_text": "Thermodynamic and kinetic modeling of Mn-Ni-Si precipitates in low-Cu  reactor pressure vessel steel [1707.08072v1]", "retrieval_score": 0.499379962682724, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2108.09224v1_chunk_0", "chunk_text": "Characterizing accelerated precipitation in proton irradiated steel", "retrieval_score": 0.495997816324234, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Steam Flow → Component C Reactor Feed"}, {"chunk_id": "2301.11728v1_chunk_2", "chunk_text": "The sampled observations are\nhigh-dimensional vectors containing the outputs of a detailed CFD steady-state\nmodel of the process, i. e. the values of velocity, pressure, temperature, and\nspecies mass fractions at each point in the discretization. A machine learning\nworkflow is presented, able to predict out-of-sample (a) observations (e. g. mass fraction in the reactor) given process parameters (e. g.", "retrieval_score": 0.4949888288974762, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Steam Flow → Component C Reactor Feed"}], "original_retrieval_results": 15}