{"variable_pair": ["<PERSON> Feed", "Purge Rate"], "results": [{"chunk": {"chunk_id": "pdf_729_chunk_0", "paper_id": "pdf_729", "paper_title": "Geometric Decomposition of Feed Forward", "chunk_text": "Geometric Decomposition of Feed Forward", "found_variables": ["composition", "feed"], "variable_count": 2, "chunk_length": 39}, "score": 0.5270636081695557, "query": "D Feed Purge Rate interaction"}, {"chunk": {"chunk_id": "pdf_6655_chunk_0", "paper_id": "pdf_6655", "paper_title": "Feed-forward neural network unfolding", "chunk_text": "Feed-forward neural network unfolding", "found_variables": ["work", "feed"], "variable_count": 2, "chunk_length": 37}, "score": 0.5102927684783936, "query": "D Feed Purge Rate interaction"}, {"chunk": {"chunk_id": "2112.08180v1_chunk_0", "paper_id": "2112.08180v1", "paper_title": "Feed-forward neural network unfolding", "chunk_text": "Feed-forward neural network unfolding", "found_variables": ["work", "feed"], "variable_count": 2, "chunk_length": 37}, "score": 0.5102927684783936, "query": "D Feed Purge Rate interaction"}, {"chunk": {"chunk_id": "2107.08078v1_chunk_4", "paper_id": "2107.08078v1", "paper_title": "A Multi-stage Stochastic Programming Model for Adaptive Biomass\nProcessing Operation under Uncertainty", "chunk_text": "Our sensitivity\nanalysis indicates that updating the infeed rate of the system, the processing\nspeed of equipment, and bale sequencing based on the moisture level of biomass\nimproves the processing rate of the reactor and reduces operating costs.", "found_variables": ["feed", "rate", "level", "reactor"], "variable_count": 4, "chunk_length": 246}, "score": 0.5041370987892151, "query": "D Feed Purge Rate interaction"}, {"chunk": {"chunk_id": "1807.03623v1_chunk_0", "paper_id": "1807.03623v1", "paper_title": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "chunk_text": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "found_variables": ["temperature", "temp", "reactor"], "variable_count": 3, "chunk_length": 68}, "score": 0.49888211488723755, "query": "D Feed Purge Rate interaction"}, {"chunk": {"chunk_id": "1712.01580v1_chunk_0", "paper_id": "1712.01580v1", "paper_title": "The Lifting Bifurcation Problem on Feed-Forward Networks", "chunk_text": "The Lifting Bifurcation Problem on Feed-Forward Networks", "found_variables": ["work", "feed"], "variable_count": 2, "chunk_length": 56}, "score": 0.4986196756362915, "query": "D Feed Purge Rate interaction"}, {"chunk": {"chunk_id": "pdf_6260_chunk_0", "paper_id": "pdf_6260", "paper_title": "Attention-Based Multiscale Temporal Fusion Network", "chunk_text": "Attention-Based Multiscale Temporal Fusion Network", "found_variables": ["temp", "work"], "variable_count": 2, "chunk_length": 50}, "score": 0.4981257915496826, "query": "D Feed Purge Rate interaction"}, {"chunk": {"chunk_id": "pdf_7407_chunk_2", "paper_id": "pdf_7407", "paper_title": "Fault Diagnosis for Power Electronics Converters based on Deep  ", "chunk_text": "Firstly, the correlation analysis of the voltage or current data running in various fault states is performed to remove \nthe redundant features and the sampling point. Secondly, the wavelet transform is used to remove the redundant data of the \nfeatures, and then the training sample data is greatly compressed. The deep feedforward network is trained by the low \nfrequency component of the features, while the training spee d is greatly accelerated.", "found_variables": ["rate", "press", "work", "component", "feed"], "variable_count": 5, "chunk_length": 450}, "score": 0.4875829815864563, "query": "correlation D Feed Purge Rate"}, {"chunk": {"chunk_id": "pdf_8488_chunk_5", "paper_id": "pdf_8488", "paper_title": "1Improvement in RF Curves for Booster Running at High ", "chunk_text": "The LLRF uses four approximate programme d curves to control the acceleration \nprocess: FREQ, Bias, ROF, and RAG. With  the input of the phase error from phase FERMILAB-TM-2271-AD \n 2feedback of LLRF, VXI LLRF generates the LO -Freq using the FREQ program in such a \nway that the LO-Freq has the same frequenc y and a constant phase difference with the \nCB through a cycle. The radial feedba.", "found_variables": ["rate", "input", "feed"], "variable_count": 3, "chunk_length": 392}, "score": 0.48743146657943726, "query": "D Feed Purge Rate interaction"}, {"chunk": {"chunk_id": "1701.06551v1_chunk_2", "paper_id": "1701.06551v1", "paper_title": "On the Parametric Study of Lubricating Oil Production using an\nArtificial Neural Network (ANN) Approach", "chunk_text": "A feed-forward Multi-Layer Perceptron Neural Network was\nsuccessfully applied to capture the relationship between inputs and output\nparameters.", "found_variables": ["work", "A Feed", "input", "feed"], "variable_count": 4, "chunk_length": 143}, "score": 0.4837762415409088, "query": "D Feed Purge Rate interaction"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}