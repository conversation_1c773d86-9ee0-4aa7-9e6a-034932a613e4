{"variable_pair": ["<PERSON> Feed", "Prod Sep Underflow"], "aggregation": {"has_causal_relationship": true, "final_association": "A", "final_direction": "D Feed → Prod Sep Underflow", "association_votes": {"A": 6, "⊥": 2, "IA": 5, "?": 2}, "direction_votes": {"D Feed → Prod Sep Underflow": 11}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.6169189214706421, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed → Prod Sep Underflow"}, {"chunk_id": "pdf_131_chunk_0", "chunk_text": "arXiv:2303.05904v1  [cs.LG]  10 Mar 2023Deep Anomaly Detection on Tennessee Eastman Process Data", "retrieval_score": 0.5931026935577393, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_2859_chunk_0", "chunk_text": "Feed-forward neural network unfolding", "retrieval_score": 0.5727931261062622, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "D Feed → Prod Sep Underflow"}, {"chunk_id": "2112.08180v1_chunk_0", "chunk_text": "Feed-forward neural network unfolding", "retrieval_score": 0.5727931261062622, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "D Feed → Prod Sep Underflow"}, {"chunk_id": "pdf_819_chunk_0", "chunk_text": "Geometric Decomposition of Feed Forward", "retrieval_score": 0.5685855150222778, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2301.11573v3_chunk_3", "chunk_text": "The theoretical analysis is verified through\nMonte Carlo simulations and Tennessee Eastman process (TEP) dataset.", "retrieval_score": 0.563801646232605, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "D Feed → Prod Sep Underflow"}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.5630387663841248, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed → Prod Sep Underflow"}, {"chunk_id": "pdf_1917_chunk_3", "chunk_text": "Stadium\nAve, West Lafayette, 47907, IN, USA\nAbstract\nMachine learning algorithms are increasingly being applied to fault detection\nand diagnosis (FDD) in chemical processes. However, existing data-driven\nFDD platforms often lack interpretability for process operators and strug-\ngle to identify root causes of previously unseen faults. This paper presents\nFaultExplainer , an interactive tool designed to improve fault detection, diag-\nnosis, and explanation in the Tennessee Eastman Process (TEP).", "retrieval_score": 0.5597909688949585, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed → Prod Sep Underflow"}, {"chunk_id": "2403.10842v4_chunk_0", "chunk_text": "Twin Transformer using Gated Dynamic Learnable Attention mechanism for\nFault Detection and Diagnosis in the Tennessee Eastman Process", "retrieval_score": 0.5585823655128479, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "D Feed → Prod Sep Underflow"}, {"chunk_id": "2309.00157v1_chunk_3", "chunk_text": "We validate the approach\nusing the Benchmark Tennessee Eastman while doing an ablation study of the\nmodel update parameters.", "retrieval_score": 0.5539426803588867, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "D Feed → Prod Sep Underflow"}, {"chunk_id": "pdf_2343_chunk_1", "chunk_text": "1\nAutoencoder-assisted Feature Ensemble Net for\nIncipient Faults\n<PERSON><PERSON><PERSON>, <PERSON>, Member, IEEE , <PERSON><PERSON><PERSON>, Member, IEEE\nAbstract —Deep learning has shown the great power in the field\nof fault detection. However, for incipient faults with tiny ampli-\ntude, the detection performance of the current deep learning\nnetworks (DLNs) is not satisfactory. Even if prior information\nabout the faults is utilized, DLNs can’t successfully detect faults\n3, 9 and 15 in Tennessee Eastman process (TEP).", "retrieval_score": 0.5526908040046692, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed → Prod Sep Underflow"}, {"chunk_id": "pdf_9441_chunk_0", "chunk_text": "On the space of coefﬁcients of a Feed Forward", "retrieval_score": 0.5518442392349243, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_4254_chunk_5", "chunk_text": "Keywords: Tennessee Eastman Process, Chemical Processes, Graph neural\nnetworks, Fault diagnosis, Sensor dataarXiv:2210. 11164v1  [cs. AI]  20 Oct 2022\n1. Introduction\nDuring the production, equipment often stops due to the various faults. The process of \fndin.", "retrieval_score": 0.5517138242721558, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "D Feed → Prod Sep Underflow"}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5505053997039795, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "D Feed → Prod Sep Underflow"}, {"chunk_id": "1709.02232v1_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5505053997039795, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}], "original_retrieval_results": 15}