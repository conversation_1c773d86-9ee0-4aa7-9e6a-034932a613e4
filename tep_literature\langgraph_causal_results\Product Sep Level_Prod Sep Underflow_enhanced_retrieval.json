{"variable_pair": ["Product Sep Level", "Prod Sep Underflow"], "results": [{"chunk": {"chunk_id": "1706.02043v1_chunk_2", "paper_id": "1706.02043v1", "paper_title": "Reduced chemistry for butanol isomers at engine-relevant conditions", "chunk_text": "During the reduction\nprocess, issues were encountered with pressure-dependent reactions formulated\nusing the logarithmic pressure interpolation approach; these issues are\ndiscussed and recommendations made to avoid ambiguity in its future\nimplementation in mechanism development.", "found_variables": ["press", "pressure"], "variable_count": 2, "chunk_length": 279}, "score": 0.5297638177871704, "query": "Product Sep Level Prod Sep Underflow interaction"}, {"chunk": {"chunk_id": "pdf_5902_chunk_3", "paper_id": "pdf_5902", "paper_title": "arXiv:cond-mat/9902259v1  [cond-mat.supr-con]  18 Feb 1999to appear in Phys. Rev. B", "chunk_text": "One, based\non the chemical imbalance at SDinterface, results in a pro-\nnounced maximum of the TEP peak near θ=π/2 (where the\nso-called s+idmixed pairing state is formed) for two iden-\ntical superconductors with Tcd=Tcs≡Tc. Another eﬀect,\nwhich should manifest itself at SDinterface comprising an\ns-wave low- Tcsuperconductor and a d-wave high- Tcsuper-\nconductor with Tcd∝negationslash=Tcs, predicts Sp∝Tcd−Tcsfor the\nTEP peak value.", "found_variables": ["TEP"], "variable_count": 1, "chunk_length": 433}, "score": 0.5217288136482239, "query": "Product Sep Level Prod Sep Underflow interaction"}, {"chunk": {"chunk_id": "2505.11273v1_chunk_2", "paper_id": "2505.11273v1", "paper_title": "Bilevel Transmission Expansion Planning with Joint Chance-Constrained\nDispatch", "chunk_text": "Since the\nday-ahead market clears before uncertainty realizes, explicitly modelling these\nuncertainties at the lower-level market clearing becomes important in bilevel\nTEP problems. In this paper, we introduce a novel bilevel TEP framework with\nlower-level joint chance-constrained market clearing that manages line flow\nconstraints under wind uncertainty and accounts for the effect of network\ntariffs on participants' actual marginal costs and utility.", "found_variables": ["work", "level", "flow", "TEP"], "variable_count": 4, "chunk_length": 454}, "score": 0.****************, "query": "Product Sep Level and Prod Sep Underflow"}, {"chunk": {"chunk_id": "pdf_2039_chunk_3", "paper_id": "pdf_2039", "paper_title": "Reduced chemistry for butanol isomers at", "chunk_text": "During the reduction process, issues encountered with pressure-dependent\nreactions formulated using the logarithmic pressure interpolation approach were dis-\ncussed, with recommendations made to avoid ambiguity in its future implementation\nin mechanism development.", "found_variables": ["press", "pressure"], "variable_count": 2, "chunk_length": 265}, "score": 0.****************, "query": "Product Sep Level Prod Sep Underflow interaction"}, {"chunk": {"chunk_id": "pdf_2069_chunk_1", "paper_id": "pdf_2069", "paper_title": "IEEE TRANSACTIONS ON NEURAL NETWORKS AND LEARNING SYSTEMS 1", "chunk_text": "IEEE TRANSACTIONS ON NEURAL NETWORKS AND LEARNING SYSTEMS 1\nSCLIFD:Supervised Contrastive Knowledge\nDistillation for Incremental Fault Diagnosis under\nLimited Fault Data\nPeng <PERSON>,Member, IEEE , <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>\u0003,Member, IEEE ,\nand <PERSON><PERSON>\u0003,Fellow, IEEE\nAbstract —Intelligent fault diagnosis has made extraordinary\nadvancements currently. Nonetheless, few works tackle class-\nincremental learning for fault diagnosis under limited fault data,\ni. e.", "found_variables": ["distillation", "work"], "variable_count": 2, "chunk_length": 496}, "score": 0.5090162754058838, "query": "correlation Product Sep Level Prod Sep Underflow"}, {"chunk": {"chunk_id": "pdf_9257_chunk_2", "paper_id": "pdf_9257", "paper_title": "Learning Production Process Heterogeneity Across Industries:  ", "chunk_text": "We find that the greater the functional distance between two industries’ production processes, the \nlower are the number of M&As , deal completion rate s, announcement return s, and post-M&A sur-\nvival likelihood. Our results highlight the importance of structural heterogeneity in production t ech-\nnolog y to firms’ business integration decisions. (JEL G3, G34,  L2, M1 ) \nKey words :  Deep learning, production process  heterogeneity , M&A’s, integration synergy, the \nboundaries of the firm.", "found_variables": ["rate"], "variable_count": 1, "chunk_length": 495}, "score": 0.5084824562072754, "query": "correlation Product Sep Level Prod Sep Underflow"}, {"chunk": {"chunk_id": "2302.05929v1_chunk_0", "paper_id": "2302.05929v1", "paper_title": "SCLIFD:Supervised Contrastive Knowledge Distillation for Incremental\nFault Diagnosis under Limited Fault Data", "chunk_text": "SCLIFD:Supervised Contrastive Knowledge Distillation for Incremental\nFault Diagnosis under Limited Fault Data", "found_variables": ["distillation"], "variable_count": 1, "chunk_length": 109}, "score": 0.5065734386444092, "query": "correlation Product Sep Level Prod Sep Underflow"}, {"chunk": {"chunk_id": "1307.0249v1_chunk_1", "paper_id": "1307.0249v1", "paper_title": "Electric Field Effect Thermoelectric Transport in Individual Silicon and\nGermanium/Silicon Nanowire", "chunk_text": "We have simultaneously measured conductance and thermoelectric power (TEP) of\nindividual silicon and germanium/silicon core/shell nanowires in the field\neffect transistor device configuration. As the applied gate voltage changes,\nthe TEP shows distinctly different behaviors while the electrical conductance\nexhibits the turn-off, subthreshold, and saturation regimes respectively. At\nroom temperature, peak TEP value of $\\sim 300 \\mu$V/K is observed in the\nsubthreshold regime of the Si devices.", "found_variables": ["temperature", "temp", "TEP"], "variable_count": 3, "chunk_length": 496}, "score": 0.5030955672264099, "query": "Product Sep Level and Prod Sep Underflow"}, {"chunk": {"chunk_id": "pdf_2215_chunk_2", "paper_id": "pdf_2215", "paper_title": "Explainability: Relevance based Dynamic Deep Learning", "chunk_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>,<\naChemical Engineering Department, University of Waterloo, Ontario, Canada, N2L3G1\nbManufacturing Technologies, Sanoﬁ Pasteur, Toronto, Ontario, Canada, M2R3T4\ncChemical Engineering Department, University of Waterloo, Ontario, Canada, N2L3G1\nARTICLE INFO\nKeywords :\nexplainability\nfault detection and diagnosis\nautoencoders\ndeep learning\nTennessee Eastman ProcessABSTRACT\nThefocusofthisworkisonStatisticalProcessControl(SPC)ofamanufacturingprocessbased\non available measurements.", "found_variables": ["work", "fault detection", "Tennessee Eastman"], "variable_count": 3, "chunk_length": 528}, "score": 0.5022595524787903, "query": "correlation Product Sep Level Prod Sep Underflow"}, {"chunk": {"chunk_id": "2301.05537v4_chunk_2", "paper_id": "2301.05537v4", "paper_title": "Almost Surely $\\sqrt{T}$ Regret for Adaptive LQR", "chunk_text": "The\ncontroller features a circuit-breaking mechanism, which circumvents potential\nsafety breach and guarantees the convergence of the system parameter estimate,\nbut is shown to be triggered only finitely often and hence has negligible\neffect on the asymptotic performance of the controller. The proposed controller\nis also validated via simulation on Tennessee Eastman Process~(TEP), a commonly\nused industrial process example.", "found_variables": ["vent", "TEP", "Tennessee Eastman"], "variable_count": 3, "chunk_length": 427}, "score": 0.5001903772354126, "query": "Product Sep Level Prod Sep Underflow interaction"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}