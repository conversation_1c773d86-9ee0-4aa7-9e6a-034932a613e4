{"variable_pair": ["Purge Rate", "Component E Purge Gas"], "aggregation": {"has_causal_relationship": true, "final_association": "A", "final_direction": "Purge Rate → Component E Purge Gas", "association_votes": {"A": 7, "?": 4, "IA": 4}, "direction_votes": {"Purge Rate → Component E Purge Gas": 8, "Purge Rate - Component E Purge Gas": 3}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5488657355308533, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Component E Purge Gas"}, {"chunk_id": "pdf_9406_chunk_5", "chunk_text": "The performance of the method is demonstrated\nand contrasted to (dynamic) principal component analysis, which is widely applied in the industry,\nin the benchmark Tennessee Eastman process (TEP) and a real chemical manufacturing dataset. Keywords: fault detection, fault identi\fcation, recurrent neural networks, variational dropout,\nBayesian inference, Tennessee Eastman process. 1.", "retrieval_score": 0.5402724146842957, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "1911.04386v2_chunk_4", "chunk_text": "The\noutstanding performance of this method is demonstrated and contrasted to\n(dynamic) principal component analysis, which are widely applied in the\nindustry, in the benchmark Tennessee Eastman process~(TEP) and a real chemical\nmanufacturing dataset.", "retrieval_score": 0.5394982099533081, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Purge Rate → Component E Purge Gas"}, {"chunk_id": "1607.07423v3_chunk_5", "chunk_text": "We illustrate the\nsuccessful use of $K_T$ chart using the Tennessee Eastman process data.", "retrieval_score": 0.5331756472587585, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_223_chunk_5", "chunk_text": "Otherwise, the gas-to-core mass ratio (GCR) reaches above ∼10% which is too\nlarge to explain the measured properties of mini-Neptunes, necessitating other gas-limiting processes\nsuch as late-time core assembly. The effect of entropy advection on gas accretion weakens even further\nbeyond 0. 1 AU. We present an updated scaling relation between GCR and the penetration depth of the\nadvective flows which varies non-trivially with orbital distances, core masses and dusty vs. dust-free\nopacity.", "retrieval_score": 0.5303452610969543, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Purge Rate → Component E Purge Gas"}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.5269365310668945, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Purge Rate - Component E Purge Gas"}, {"chunk_id": "1709.02232v1_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5231296420097351, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.****************, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_1659_chunk_4", "chunk_text": "%\nnickel, we ﬁnd a power-law scaling of dose with exponent 0. 25 –0. 30 accounts for the e ﬀects of dose rate on precipitate volume\nfraction over 6 orders of magnitude in dose rate. In conjunct ion with dose rate, carbon is identiﬁed as performing a leadi ng role\nin determining precipitate sizes, adding to the known e ﬀects of nickel, manganese and irradiation temperature.", "retrieval_score": 0.****************, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Purge Rate → Component E Purge Gas"}, {"chunk_id": "pdf_6959_chunk_5", "chunk_text": "Simulation results demonstrate the efficacy of this approach, showcasing superior performance in \naccuracy, false alarm rate, and misclassifica tion rate compared to existing methods. This \nmethodology provides a robust and interpretable solution for fault detection and diagnosis in the \nTEP, highlighting its potential for industrial applications. Keywords. Fault Detection, Tennessee Eastman Process, Supervised Feature Selection, Attention \nMechanism, Bidirectional Long Short -Term Memory.", "retrieval_score": 0.5219651460647583, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Component E Purge Gas"}, {"chunk_id": "2203.11321v1_chunk_4", "chunk_text": "Finally, as a case study, the proposed\nmodel is implemented in the well-known Tennessee Eastman process, and the\nresults are presented.", "retrieval_score": 0.5194379091262817, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Component E Purge Gas"}, {"chunk_id": "2212.12092v1_chunk_5", "chunk_text": "Finally, we use the benchmark Tennessee Eastman to\nperform experiments to test the ensemble classifier's prediction and anomaly\ndetection capabilities.", "retrieval_score": 0.5150163173675537, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Purge Rate → Component E Purge Gas"}, {"chunk_id": "pdf_7900_chunk_5", "chunk_text": "Furthermore, our detailed \ncomparisons show our approach improves on the leading American Society for Testing and \nMaterials (ASTM) E900 -15 standard model for RPV embrittlement on every metric we assessed, \ndemonstrating th e efficacy of machine learning approaches for this type of highly demanding \nmaterials property prediction.\n\n1. Introduction:  \nNuclear power is a key component of global clean energy production, producing roughly \n20% of the power", "retrieval_score": 0.5141051411628723, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Purge Rate - Component E Purge Gas"}, {"chunk_id": "pdf_927_chunk_4", "chunk_text": "A \ngreat deal of experimental, theoretical and numeric al works has been conducted so far and \nhelped us to better understand these processes [6]. Whatever the concerned application, gas \ntemperature has a great influence on the electrical  parameters and on the nature and \nproduction of primary species formed by plasma  and must then play an important role. The increase of temperature leads to modify current  and voltage shapes as it has been shown \nin [7].", "retrieval_score": 0.513079822063446, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Purge Rate - Component E Purge Gas"}, {"chunk_id": "2308.11247v2_chunk_0", "chunk_text": "Benchmarking Domain Adaptation for Chemical Processes on the Tennessee\nEastman Process", "retrieval_score": 0.5130125880241394, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Purge Rate → Component E Purge Gas"}], "original_retrieval_results": 15}