{"variable_pair": ["Reactor Feed Rate", "Purge Rate"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Reactor Feed Rate → Purge Rate", "association_votes": {"A": 2, "⊥": 4, "IA": 7, "?": 2}, "direction_votes": {"Purge Rate → Reactor Feed Rate": 1, "Reactor Feed Rate → Purge Rate": 8}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "2107.08078v1_chunk_4", "chunk_text": "Our sensitivity\nanalysis indicates that updating the infeed rate of the system, the processing\nspeed of equipment, and bale sequencing based on the moisture level of biomass\nimproves the processing rate of the reactor and reduces operating costs.", "retrieval_score": 0.6181901693344116, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Purge Rate"}, {"chunk_id": "2103.05025v1_chunk_0", "chunk_text": "Optimal Control to Handle Variations in Biomass Feedstock\nCharacteristics and Reactor In-Feed Rate", "retrieval_score": 0.5871374607086182, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_2177_chunk_2", "chunk_text": "Eksioglub, <PERSON>ushi <PERSON>c,\naEnergy and Environment Science & Technology Department, Idaho National Laboratory, Idaho Falls, ID\nbIndustrial Engineering Department, University of Arkansas, Fayetteville, AR\ncGlenn Department of Civil Engineering, Clemson University, Clemson, SC\nAbstract\nThe variations in feedstock characteristics such as moisture and particle size distribution\nlead to an inconsistent ﬂow of feedstock from the biomass pre-processing system to the reactor\nin-feed system.", "retrieval_score": 0.5847151875495911, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Purge Rate"}, {"chunk_id": "pdf_7578_chunk_0", "chunk_text": "Correlation between radiation damage and magnetic properties in reactor vessel", "retrieval_score": 0.5619654655456543, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Feed Rate → Purge Rate"}, {"chunk_id": "1807.03623v1_chunk_0", "chunk_text": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "retrieval_score": 0.5576632022857666, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Purge Rate → Reactor Feed Rate"}, {"chunk_id": "pdf_2177_chunk_3", "chunk_text": "These inconsistencies result in low on-stream times at the reactor in-feed\nequipment. This research develops an optimal process control method for a biomass pre-\nprocessing system comprised of milling and densiﬁcation operations to provide the consistent\nﬂow of feedstock to a reactor’s throat. This method uses a mixed-integer optimization\nmodel to identify optimal bale sequencing, equipment in-feed rate, and buﬀer location and\nsize in the biomass pre-processing system.", "retrieval_score": 0.5559466481208801, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Purge Rate"}, {"chunk_id": "2103.05025v1_chunk_1", "chunk_text": "The variations in feedstock characteristics such as moisture and particle\nsize distribution lead to an inconsistent flow of feedstock from the biomass\npre-processing system to the reactor in-feed system. These inconsistencies\nresult in low on-stream times at the reactor in-feed equipment. This research\ndevelops an optimal process control method for a biomass pre-processing system\ncomprised of milling and densification operations to provide the consistent\nflow of feedstock to a reactor's throat.", "retrieval_score": 0.5505785942077637, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_4903_chunk_3", "chunk_text": "We conduct a\nsystematic evaluation of the FARM monitoring framework using the Tennessee Eastman Process\n(TEP) dataset. Results show that FARM performs competitively against state-of-the-art process\nmonitoring algorithms by achieving a good balance among fault detection rate (FDR), fault\ndetection speed (FDS), and false alarm rate (FAR). Specifically, FARM achieved an average\nFDR of 96.", "retrieval_score": 0.5401180386543274, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Purge Rate"}, {"chunk_id": "2504.01276v1_chunk_2", "chunk_text": "We conduct a systematic evaluation of the FARM monitoring framework\nusing the Tennessee Eastman Process (TEP) dataset. Results show that FARM\nperforms competitively against state-of-the-art process monitoring algorithms\nby achieving a good balance among fault detection rate (FDR), fault detection\nspeed (FDS), and false alarm rate (FAR). Specifically, FARM achieved an average\nFDR of 96.", "retrieval_score": 0.5401180386543274, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.537755012512207, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Purge Rate"}, {"chunk_id": "2103.05025v1_chunk_3", "chunk_text": "Our\ncase study based on switchgrass finds that HPC reduces the variation of a\nreactor's feeding rate by 100\\% without increasing the operating cost of the\nbiomass pre-processing system for biomass with moisture ranging from 10 to\n25\\%. A biorefinery can adapt HPC to achieve its design capacity.", "retrieval_score": 0.5365588068962097, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_1999_chunk_5", "chunk_text": "We show\nthe value of multi-stage stochastic programming from an extensive computational experiment. Our\nsensitivity analysis indicates that updating the infeed rate of the system, the processing speed of\nequipment, and bale sequencing based on the moisture level of biomass improves the processing rate\nof the reactor and reduces operating costs. KEYWORDS\nbioenergy, biomass, biomass processing system, multi-stage stochastic programming\n1.", "retrieval_score": 0.5352884531021118, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "1807.03623v1_chunk_1", "chunk_text": "This paper reports on experimental measurements of the gas temperature in a\ndielectric barrier discharge reactor powered by a high voltage pulsed signal. It is shown that the thermal behavior of the reactor follows a first order\nmodel. However, an unexpected runaway phenomenon was observed at a frequency of\n300Hz. A sudden increase in the power source and consequently in reactor\ntemperature which reaches 170{\\deg}C is observed.", "retrieval_score": 0.5310025215148926, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Purge Rate"}, {"chunk_id": "1709.02232v1_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5281076431274414, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Feed Rate → Purge Rate"}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5281075835227966, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}], "original_retrieval_results": 15}