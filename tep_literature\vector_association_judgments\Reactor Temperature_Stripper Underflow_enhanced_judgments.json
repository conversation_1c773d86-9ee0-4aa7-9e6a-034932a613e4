{"variable_pair": ["Reactor Temperature", "Stripper Underflow"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Reactor Temperature → Stripper Underflow", "association_votes": {"A": 2, "?": 3, "IA": 8, "⊥": 2}, "direction_votes": {"Stripper Underflow → Reactor Temperature": 1, "Reactor Temperature - Stripper Underflow": 1, "Reactor Temperature → Stripper Underflow": 8}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "1807.03623v1_chunk_0", "chunk_text": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "retrieval_score": 0.6375061273574829, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Temperature → Stripper Underflow"}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5832332968711853, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2301.11728v1_chunk_3", "chunk_text": "inlet\ntemperature); (b) process parameters given observation data; and (c) partial\nobservations (e. g. temperature in the reactor) given other partial observations\n(e. g. mass fraction in the reactor). The proposed workflow relies on the\nmanifold learning schemes Diffusion Maps and the associated Geometric\nHarmonics. Diffusion Maps is used for discovering a reduced representation of\nthe available data, and Geometric Harmonics for extending functions defined on\nthe manifold.", "retrieval_score": 0.5822927951812744, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Temperature → Stripper Underflow"}, {"chunk_id": "pdf_927_chunk_1", "chunk_text": "This paper reports on experimental measurements of the gas temperature in a dielectric barrier \ndischarge reactor powered by a high voltage pulsed signal. It is shown that the thermal \nbehavior of the reactor follows a first order model. However, an unexpected runaway \nphenomenon was observed at a frequency of 300Hz. A sudden increase in the power source \nand consequently in reactor temperature which reach es 170°C is observed.", "retrieval_score": 0.5784380435943604, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Temperature → Stripper Underflow"}, {"chunk_id": "1807.03623v1_chunk_1", "chunk_text": "This paper reports on experimental measurements of the gas temperature in a\ndielectric barrier discharge reactor powered by a high voltage pulsed signal. It is shown that the thermal behavior of the reactor follows a first order\nmodel. However, an unexpected runaway phenomenon was observed at a frequency of\n300Hz. A sudden increase in the power source and consequently in reactor\ntemperature which reaches 170{\\deg}C is observed.", "retrieval_score": 0.5758273601531982, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_1917_chunk_3", "chunk_text": "Stadium\nAve, West Lafayette, 47907, IN, USA\nAbstract\nMachine learning algorithms are increasingly being applied to fault detection\nand diagnosis (FDD) in chemical processes. However, existing data-driven\nFDD platforms often lack interpretability for process operators and strug-\ngle to identify root causes of previously unseen faults. This paper presents\nFaultExplainer , an interactive tool designed to improve fault detection, diag-\nnosis, and explanation in the Tennessee Eastman Process (TEP).", "retrieval_score": 0.5750962495803833, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Reactor Temperature → Stripper Underflow"}, {"chunk_id": "pdf_6635_chunk_5", "chunk_text": "g. temperature in the reactor) given other par-\ntial observations (e. g. mass fraction in the reactor). The proposed work\row\nrelies on the manifold learning schemes Di\u000busion Maps and the associated\nGeometric Harmonics. Di\u000busion Maps is used for discovering a reduced rep-\nresent.", "retrieval_score": 0.5735515356063843, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_7856_chunk_5", "chunk_text": "In the second stage, a reconstruction error-based threshold approach using the trained\nGAWNO is employed to detect and isolate faults based on the discrepancy values. We validate the\nproposed approach using the Tennessee Eastman Process (TEP) dataset and Avedore wastewater\ntreatment plant (WWTP) and N2Oemissions nam.", "retrieval_score": 0.5677491426467896, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Temperature → Stripper Underflow"}, {"chunk_id": "pdf_927_chunk_0", "chunk_text": "1\n\n1 \nTemperature Runaway in a Pulsed Dielectric Barrier Discharge \nReactor\n\nH.Sad<PERSON> 1,  <PERSON><PERSON> 1, <PERSON><PERSON><PERSON><PERSON> 2\n\n1. Institut PPRIME, UPR CNRS 3346  \n2. Laboratoire de Catalyse en Chimie Organique, UMR CNRS 6503\n\nUniversité de Poitiers, Ecole Supérieure d'Ingénieu rs de Poitiers,  \n40, Avenue du Recteur Pineau, 86022 Poitiers (Franc e)\n\nAbstract :", "retrieval_score": 0.566360354423523, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Stripper Underflow → Reactor Temperature"}, {"chunk_id": "2308.11247v2_chunk_0", "chunk_text": "Benchmarking Domain Adaptation for Chemical Processes on the Tennessee\nEastman Process", "retrieval_score": 0.5626909732818604, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Temperature → Stripper Underflow"}, {"chunk_id": "pdf_7900_chunk_1", "chunk_text": "Keywords:  reactor pressure vessel, embrittlement, tran sition temperature shift, machine \nlearning, neural network", "retrieval_score": 0.5610472559928894, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Temperature → Stripper Underflow"}, {"chunk_id": "2401.04004v1_chunk_3", "chunk_text": "In the second stage, a reconstruction error-based threshold\napproach using the trained GAWNO is employed to detect and isolate faults based\non the discrepancy values. We validate the proposed approach using the\nTennessee Eastman Process (TEP) dataset and Avedore wastewater treatment plant\n(WWTP) and N2O emissions named as WWTPN2O datasets.", "retrieval_score": 0.5581933856010437, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "1709.02232v1_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5551044344902039, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_1996_chunk_0", "chunk_text": "RNN-based Early Cyber-Attack Detection for the Tennessee Eastman Process", "retrieval_score": 0.5551043748855591, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Temperature - Stripper Underflow"}, {"chunk_id": "2412.14492v1_chunk_1", "chunk_text": "Machine learning algorithms are increasingly being applied to fault detection\nand diagnosis (FDD) in chemical processes. However, existing data-driven FDD\nplatforms often lack interpretability for process operators and struggle to\nidentify root causes of previously unseen faults. This paper presents\nFaultExplainer, an interactive tool designed to improve fault detection,\ndiagnosis, and explanation in the Tennessee Eastman Process (TEP).", "retrieval_score": 0.5547928810119629, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Reactor Temperature → Stripper Underflow"}], "original_retrieval_results": 15}