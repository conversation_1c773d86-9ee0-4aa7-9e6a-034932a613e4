{"variable_pair": ["Separator CW Outlet Temp", "Component E Purge Gas"], "aggregation": {"has_causal_relationship": true, "final_association": "IA", "final_direction": "Separator CW Outlet Temp → Component E Purge Gas", "association_votes": {"A": 4, "⊥": 1, "IA": 7, "?": 3}, "direction_votes": {"Separator CW Outlet Temp → Component E Purge Gas": 9, "Separator CW Outlet Temp - Component E Purge Gas": 2}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "1807.03623v1_chunk_0", "chunk_text": "Temperature Runaway in a Pulsed Dielectric Barrier Discharge Reactor", "retrieval_score": 0.5393199920654297, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "1911.04386v2_chunk_4", "chunk_text": "The\noutstanding performance of this method is demonstrated and contrasted to\n(dynamic) principal component analysis, which are widely applied in the\nindustry, in the benchmark Tennessee Eastman process~(TEP) and a real chemical\nmanufacturing dataset.", "retrieval_score": 0.5377253890037537, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Separator CW Outlet Temp → Component E Purge Gas"}, {"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.537625789642334, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_9406_chunk_5", "chunk_text": "The performance of the method is demonstrated\nand contrasted to (dynamic) principal component analysis, which is widely applied in the industry,\nin the benchmark Tennessee Eastman process (TEP) and a real chemical manufacturing dataset. Keywords: fault detection, fault identi\fcation, recurrent neural networks, variational dropout,\nBayesian inference, Tennessee Eastman process. 1.", "retrieval_score": 0.5370985269546509, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.536783754825592, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Separator CW Outlet Temp → Component E Purge Gas"}, {"chunk_id": "pdf_6233_chunk_3", "chunk_text": "For Co-dopingwe clearly see a change in thete mperaturedependent TEP andHall\ncoeﬃcient data when the sample is doped to suﬃcient e(the number of extra electrons associated\nwith the TMdoping) so as to stabilize low temperature superconductivi ty. Remarkably, a similar\nchange is found in the Cu-doped samples at comparable e-value, even though these compounds do\nnot superconduct.", "retrieval_score": 0.533713698387146, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Separator CW Outlet Temp → Component E Purge Gas"}, {"chunk_id": "pdf_7856_chunk_5", "chunk_text": "In the second stage, a reconstruction error-based threshold approach using the trained\nGAWNO is employed to detect and isolate faults based on the discrepancy values. We validate the\nproposed approach using the Tennessee Eastman Process (TEP) dataset and Avedore wastewater\ntreatment plant (WWTP) and N2Oemissions nam.", "retrieval_score": 0.5293745994567871, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Separator CW Outlet Temp → Component E Purge Gas"}, {"chunk_id": "pdf_5293_chunk_3", "chunk_text": "As the applied gate voltage changes, the TEP shows dist inctly diﬀerent behaviors while the\nelectrical conductance exhibits the turn-oﬀ, subthreshol d, and saturation regimes respectively. At\nroom temperature, peak TEP value of ∼300µV/K is observed in the subthreshold regime of the Si\ndevices. The temperature dependence of the saturated TEP va lues are used to estimate the carrier\ndoping of Si nanowires.", "retrieval_score": 0.5255045890808105, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Separator CW Outlet Temp → Component E Purge Gas"}, {"chunk_id": "pdf_927_chunk_1", "chunk_text": "This paper reports on experimental measurements of the gas temperature in a dielectric barrier \ndischarge reactor powered by a high voltage pulsed signal. It is shown that the thermal \nbehavior of the reactor follows a first order model. However, an unexpected runaway \nphenomenon was observed at a frequency of 300Hz. A sudden increase in the power source \nand consequently in reactor temperature which reach es 170°C is observed.", "retrieval_score": 0.5239253044128418, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Separator CW Outlet Temp - Component E Purge Gas"}, {"chunk_id": "pdf_5519_chunk_3", "chunk_text": "The temperature dependence of\nthe TEP shows diffusive linear metallic TEP becoming temperature independent\nbelow 40K. Unlike the others who used Cu(ClO4)2 for the ClO4- doping, the initial\ndoping material we used is anhydrous Fe(ClO4)3 which is crucial to obtain the\npositive TCR from T=1. 5K to 300K.", "retrieval_score": 0.5224365592002869, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Separator CW Outlet Temp → Component E Purge Gas"}, {"chunk_id": "2401.04004v1_chunk_3", "chunk_text": "In the second stage, a reconstruction error-based threshold\napproach using the trained GAWNO is employed to detect and isolate faults based\non the discrepancy values. We validate the proposed approach using the\nTennessee Eastman Process (TEP) dataset and Avedore wastewater treatment plant\n(WWTP) and N2O emissions named as WWTPN2O datasets.", "retrieval_score": 0.5220556259155273, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Separator CW Outlet Temp → Component E Purge Gas"}, {"chunk_id": "2408.01574v2_chunk_2", "chunk_text": "We identify that the likely etchant is HCl (g) formed by the pyrolysis of TBCl\nin the hydrodynamic boundary layer above the substrate. The temperature\ndependence of the etch rate reveals two distinct regimes characterized by\nmarkedly different apparent activation energies. The extracted apparent\nactivation energies suggest that at temperatures below ~800 {\\deg}C the etch\nrate is likely limited by desorption of etch products.", "retrieval_score": 0.5197852253913879, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Separator CW Outlet Temp → Component E Purge Gas"}, {"chunk_id": "2403.13502v4_chunk_0", "chunk_text": "Adversarial Attacks and Defenses in Fault Detection and Diagnosis: A\nComprehensive Benchmark on the Tennessee Eastman Process", "retrieval_score": 0.5193163156509399, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Separator CW Outlet Temp → Component E Purge Gas"}, {"chunk_id": "1807.03623v1_chunk_1", "chunk_text": "This paper reports on experimental measurements of the gas temperature in a\ndielectric barrier discharge reactor powered by a high voltage pulsed signal. It is shown that the thermal behavior of the reactor follows a first order\nmodel. However, an unexpected runaway phenomenon was observed at a frequency of\n300Hz. A sudden increase in the power source and consequently in reactor\ntemperature which reaches 170{\\deg}C is observed.", "retrieval_score": 0.5178992748260498, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Separator CW Outlet Temp - Component E Purge Gas"}, {"chunk_id": "pdf_9787_chunk_6", "chunk_text": "Experimental validation on both the Tennessee Eastman \nProcess (TEP) and the Secure Water Treatment (SWaT) \ntestbeds demonstrates the framework’s superior performan ce. A comprehensive ablation study, supported by extensive visualizations including Receiver Operatin.", "retrieval_score": 0.5162321329116821, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}], "original_retrieval_results": 15}