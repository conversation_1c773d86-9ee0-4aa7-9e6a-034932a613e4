{"variable_pair": ["Prod Sep Pressure", "Component E Purge Gas"], "aggregation": {"has_causal_relationship": true, "final_association": "?", "final_direction": "Prod Sep Pressure → Component E Purge Gas", "association_votes": {"A": 1, "⊥": 4, "?": 6, "IA": 4}, "direction_votes": {"Prod Sep Pressure - Component E Purge Gas": 2, "Prod Sep Pressure → Component E Purge Gas": 3}, "num_documents": 15, "confidence": "high"}, "individual_judgments": [{"chunk_id": "2101.06993v1_chunk_0", "chunk_text": "Deep Compression of Neural Networks for Fault Detection on Tennessee\nEastman Chemical Processes", "retrieval_score": 0.5827505588531494, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "1911.04386v2_chunk_4", "chunk_text": "The\noutstanding performance of this method is demonstrated and contrasted to\n(dynamic) principal component analysis, which are widely applied in the\nindustry, in the benchmark Tennessee Eastman process~(TEP) and a real chemical\nmanufacturing dataset.", "retrieval_score": 0.5646463632583618, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Prod Sep Pressure → Component E Purge Gas"}, {"chunk_id": "pdf_6878_chunk_4", "chunk_text": "The performance of the proposed\nmethod is validated using two industrial process cases, Tennessee Eastman Pro-\ncess (TEP) and Multiphase Flow Facility (MFF). Compared to existing methods,\n∗Corresponding author <PERSON><PERSON><PERSON> addresses: 22332128@zju. edu. cn (<PERSON><PERSON>), qian<PERSON><PERSON>an@zju. edu. cn\n(<PERSON><PERSON><PERSON>), x<PERSON><PERSON><PERSON><PERSON>@zju. edu. cn (<PERSON><PERSON><PERSON>*), songzhihuan@zju. edu. cn\n(Zhihuan Song)\nPreprint submitted to Elsevier June 21, 2024arXiv:2406. 13664v1  [cs.", "retrieval_score": 0.5589505434036255, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Prod Sep Pressure → Component E Purge Gas"}, {"chunk_id": "pdf_3805_chunk_2", "chunk_text": "Abstract: Fault detection and diagnosis (FDD) is a crucial task for ensuring the safety and \nefficiency of industrial processes. We propose a novel FDD methodology for the Tennessee \nEastman Process (TEP), a widely used benchmark for chemical process control. The model \nemploys two separate Transformer branches, enabling independent processing of input data and \npotential extraction of diverse information.", "retrieval_score": 0.5470854043960571, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "1607.07423v3_chunk_5", "chunk_text": "We illustrate the\nsuccessful use of $K_T$ chart using the Tennessee Eastman process data.", "retrieval_score": 0.543790340423584, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2403.10842v4_chunk_1", "chunk_text": "Fault detection and diagnosis (FDD) is a crucial task for ensuring the safety\nand efficiency of industrial processes. We propose a novel FDD methodology for\nthe Tennessee Eastman Process (TEP), a widely used benchmark for chemical\nprocess control. The model employs two separate Transformer branches, enabling\nindependent processing of input data and potential extraction of diverse\ninformation.", "retrieval_score": 0.5434610247612, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "2403.13502v4_chunk_0", "chunk_text": "Adversarial Attacks and Defenses in Fault Detection and Diagnosis: A\nComprehensive Benchmark on the Tennessee Eastman Process", "retrieval_score": 0.5412952303886414, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Prod Sep Pressure - Component E Purge Gas"}, {"chunk_id": "pdf_6959_chunk_5", "chunk_text": "Simulation results demonstrate the efficacy of this approach, showcasing superior performance in \naccuracy, false alarm rate, and misclassifica tion rate compared to existing methods. This \nmethodology provides a robust and interpretable solution for fault detection and diagnosis in the \nTEP, highlighting its potential for industrial applications. Keywords. Fault Detection, Tennessee Eastman Process, Supervised Feature Selection, Attention \nMechanism, Bidirectional Long Short -Term Memory.", "retrieval_score": 0.5412755012512207, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_9406_chunk_5", "chunk_text": "The performance of the method is demonstrated\nand contrasted to (dynamic) principal component analysis, which is widely applied in the industry,\nin the benchmark Tennessee Eastman process (TEP) and a real chemical manufacturing dataset. Keywords: fault detection, fault identi\fcation, recurrent neural networks, variational dropout,\nBayesian inference, Tennessee Eastman process. 1.", "retrieval_score": 0.5375192761421204, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "IA", "causal_direction": "Prod Sep Pressure - Component E Purge Gas"}, {"chunk_id": "2308.11247v2_chunk_0", "chunk_text": "Benchmarking Domain Adaptation for Chemical Processes on the Tennessee\nEastman Process", "retrieval_score": 0.5324670076370239, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2301.11573v3_chunk_3", "chunk_text": "The theoretical analysis is verified through\nMonte Carlo simulations and Tennessee Eastman process (TEP) dataset.", "retrieval_score": 0.5322723388671875, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "⊥", "causal_direction": null}, {"chunk_id": "pdf_1917_chunk_3", "chunk_text": "Stadium\nAve, West Lafayette, 47907, IN, USA\nAbstract\nMachine learning algorithms are increasingly being applied to fault detection\nand diagnosis (FDD) in chemical processes. However, existing data-driven\nFDD platforms often lack interpretability for process operators and strug-\ngle to identify root causes of previously unseen faults. This paper presents\nFaultExplainer , an interactive tool designed to improve fault detection, diag-\nnosis, and explanation in the Tennessee Eastman Process (TEP).", "retrieval_score": 0.5291414260864258, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "pdf_131_chunk_0", "chunk_text": "arXiv:2303.05904v1  [cs.LG]  10 Mar 2023Deep Anomaly Detection on Tennessee Eastman Process Data", "retrieval_score": 0.5285505056381226, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "A", "causal_direction": "Prod Sep Pressure → Component E Purge Gas"}, {"chunk_id": "2203.11321v1_chunk_4", "chunk_text": "Finally, as a case study, the proposed\nmodel is implemented in the well-known Tennessee Eastman process, and the\nresults are presented.", "retrieval_score": 0.5233146548271179, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}, {"chunk_id": "2210.08538v1_chunk_0", "chunk_text": "Advantages of OKID-ERA Identification in Control Systems. An Application\nto the Tennessee Eastman Plant", "retrieval_score": 0.5232834219932556, "retrieval_type": "vector", "relation_type": "vector_similarity", "association_type": "?", "causal_direction": null}], "original_retrieval_results": 15}