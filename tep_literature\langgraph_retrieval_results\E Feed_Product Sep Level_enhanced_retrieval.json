{"variable_pair": ["<PERSON>ed", "Product Sep Level"], "results": [{"chunk": {"chunk_id": "2001.01687v1_chunk_0", "paper_id": "2001.01687v1", "paper_title": "A Supervised Modified Hebbian Learning Method On Feed-forward Neural\nNetworks", "chunk_text": "A Supervised Modified Hebbian Learning Method On Feed-forward Neural\nNetworks", "found_variables": ["feed", "work"], "variable_count": 2, "chunk_length": 77}, "score": 0.5220208764076233, "query": "E Feed and Product Sep Level"}, {"chunk": {"chunk_id": "pdf_3282_chunk_0", "paper_id": "pdf_3282", "paper_title": "Geometric Decomposition of Feed Forward", "chunk_text": "Geometric Decomposition of Feed Forward", "found_variables": ["feed", "composition"], "variable_count": 2, "chunk_length": 39}, "score": 0.5212085843086243, "query": "E Feed and Product Sep Level"}, {"chunk": {"chunk_id": "pdf_1639_chunk_3", "paper_id": "pdf_1639", "paper_title": "arXiv:0906.1548v2  [cond-mat.supr-con]  7 Aug 2009Thermoelectric power and Hall coeﬃcient measurements on", "chunk_text": "For Co-dopingwe clearly see a change in thete mperaturedependent TEP andHall\ncoeﬃcient data when the sample is doped to suﬃcient e(the number of extra electrons associated\nwith the TMdoping) so as to stabilize low temperature superconductivi ty. Remarkably, a similar\nchange is found in the Cu-doped samples at comparable e-value, even though these compounds do\nnot superconduct.", "found_variables": ["temperature", "TEP", "temp"], "variable_count": 3, "chunk_length": 379}, "score": 0.5196892023086548, "query": "E Feed and Product Sep Level"}, {"chunk": {"chunk_id": "pdf_60_chunk_0", "paper_id": "pdf_60", "paper_title": "Feed-forward neural network unfolding", "chunk_text": "Feed-forward neural network unfolding", "found_variables": ["feed", "work"], "variable_count": 2, "chunk_length": 37}, "score": 0.5172900557518005, "query": "E Feed and Product Sep Level"}, {"chunk": {"chunk_id": "2112.08180v1_chunk_0", "paper_id": "2112.08180v1", "paper_title": "Feed-forward neural network unfolding", "chunk_text": "Feed-forward neural network unfolding", "found_variables": ["feed", "work"], "variable_count": 2, "chunk_length": 37}, "score": 0.5172900557518005, "query": "E Feed and Product Sep Level"}, {"chunk": {"chunk_id": "pdf_7639_chunk_0", "paper_id": "pdf_7639", "paper_title": "On the space of coefﬁcients of a Feed Forward", "chunk_text": "On the space of coefﬁcients of a Feed Forward", "found_variables": ["feed", "A Feed"], "variable_count": 2, "chunk_length": 45}, "score": 0.5168315768241882, "query": "E Feed and Product Sep Level"}, {"chunk": {"chunk_id": "pdf_3175_chunk_0", "paper_id": "pdf_3175", "paper_title": "Development of a Feeding Assistive Robot Using a Six Degree of", "chunk_text": "Development of a Feeding Assistive Robot Using a Six Degree of", "found_variables": ["feed", "A Feed"], "variable_count": 2, "chunk_length": 62}, "score": 0.5136633515357971, "query": "E Feed and Product Sep Level"}, {"chunk": {"chunk_id": "pdf_3193_chunk_5", "paper_id": "pdf_3193", "paper_title": "arXiv:1307.0249v1  [cond-mat.mes-hall]  30 Jun 2013Electric Field Eﬀect Thermoelectric Transport in Individu al Silicon and", "chunk_text": "Thermal transport studies in Si NWs have\nshown that increasing the surface roughness [11] or the\nenhanced phonon-drag [11, 12] can increase thermoelec-\ntric eﬃciency in Si NWs. The doping level of individual\nNWs canoften be adjustable using the electricﬁeld eﬀect\n(EFE) using the gate electrode in a ﬁeld eﬀect transistor\n(FET) device conﬁguration. Since electronic properties\nof the NWs are sensitively dependent on the carrier den-\nsity, TEP thus can be adjusted by.", "found_variables": ["TEP", "level", "thermal"], "variable_count": 3, "chunk_length": 468}, "score": 0.5118876099586487, "query": "E Feed and Product Sep Level"}, {"chunk": {"chunk_id": "0906.1548v2_chunk_1", "paper_id": "0906.1548v2", "paper_title": "Thermoelectric power and Hall coefficient measurements on\nBa(Fe(1-x)TMx)2As2 (TM = Co and Cu)", "chunk_text": "Temperature dependent thermoelectric power (TEP) data on Ba(Fe1-xTMx)2As2 (TM\n= Co and Cu), complemented by the Hall coefficient data on the samples from the\nsame batches, have been measured. For Co-doping we clearly see a change in the\ntemperature dependent TEP and Hall coefficient data when the sample is doped to\nsufficient $e$ (the number of extra electrons associated with the TM doping) so\nas to stabilize low temperature superconductivity.", "found_variables": ["temperature", "TEP", "temp"], "variable_count": 3, "chunk_length": 447}, "score": 0.5067384243011475, "query": "E Feed and Product Sep Level"}, {"chunk": {"chunk_id": "1307.0249v1_chunk_1", "paper_id": "1307.0249v1", "paper_title": "Electric Field Effect Thermoelectric Transport in Individual Silicon and\nGermanium/Silicon Nanowire", "chunk_text": "We have simultaneously measured conductance and thermoelectric power (TEP) of\nindividual silicon and germanium/silicon core/shell nanowires in the field\neffect transistor device configuration. As the applied gate voltage changes,\nthe TEP shows distinctly different behaviors while the electrical conductance\nexhibits the turn-off, subthreshold, and saturation regimes respectively. At\nroom temperature, peak TEP value of $\\sim 300 \\mu$V/K is observed in the\nsubthreshold regime of the Si devices.", "found_variables": ["temperature", "TEP", "temp"], "variable_count": 3, "chunk_length": 496}, "score": 0.5037506818771362, "query": "E Feed and Product Sep Level"}], "num_results": 10, "retrieval_method": "enhanced_multi_query"}